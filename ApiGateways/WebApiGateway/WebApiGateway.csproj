<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
    <LangVersion>latest</LangVersion>
    <Version>5.32.0</Version>
  </PropertyGroup>

  <ItemGroup>
	  <PackageReference Include="Serilog.AspNetCore" Version="3.2.0" />
	  <PackageReference Include="Serilog.Enrichers.Thread" Version="3.1.0" />
	  <PackageReference Include="Serilog.Sinks.Async" Version="1.4.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="4.1.0" />
    <PackageReference Include="Serilog.Sinks.Elasticsearch" Version="6.5.0" />
    <PackageReference Include="IdentityServer4.AccessTokenValidation" Version="3.0.1" />
  </ItemGroup>
  <ItemGroup>
    <Compile Remove="Logs\**" />
    <Content Remove="Logs\**" />
    <EmbeddedResource Remove="Logs\**" />
    <None Remove="Logs\**" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Databases\AuthDatabase\Oracle\VnisCore.AuthDatabase.Oracle.Application.Contracts\VnisCore.AuthDatabase.Oracle.Application.Contracts.csproj" />
    <ProjectReference Include="..\..\Databases\AuthDatabase\Oracle\VnisCore.AuthDatabase.Oracle.EntityFrameworkCore\VnisCore.AuthDatabase.Oracle.EntityFrameworkCore.csproj" />
    <ProjectReference Include="..\..\Databases\VnisCore\Core\Oracle\VnisCore.Core.Oracle.Application.Contracts\VnisCore.Core.Oracle.Application.Contracts.csproj" />
    <ProjectReference Include="..\..\Framework\Core\Core.AspNetCore.MultiTenancy\Core.AspNetCore.MultiTenancy.csproj" />
    <ProjectReference Include="..\..\Framework\Core\Core.AspNetCore.Mvc\Core.AspNetCore.Mvc.csproj" />
    <ProjectReference Include="..\..\Framework\Core\Core.Autofac\Core.Autofac.csproj" />
    <ProjectReference Include="..\..\Framework\Core\Core.Caching.StackExchangeRedis\Core.Caching.StackExchangeRedis.csproj" />
    <ProjectReference Include="..\..\Framework\Core\Core.Caching\Core.Caching.csproj" />
    <ProjectReference Include="..\..\Framework\Core\Core.EntityFrameworkCore.Oracle\Core.EntityFrameworkCore.Oracle.csproj" />
    <ProjectReference Include="..\..\Framework\Core\Core.Swashbuckle\Core.Swashbuckle.csproj" />
    <ProjectReference Include="..\..\Framework\Core\Gateway\Gateway.csproj" />
    <ProjectReference Include="..\..\Framework\Shared\Core.Host.Shared\Core.Host.Shared.csproj" />
    <ProjectReference Include="..\..\Framework\Shared\Core.VaultSharp\Core.VaultSharp.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="appsettings.json">
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>
