# Changelog

## [5.12.4](https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/eb81b9482370c1f6dfd46b14e5041878c51e1782) - 2023-07-14

### Performance (1)

- Refector luồng ký theo giờ: tăng thread ký để ký được nhiều hóa đơn

## [5.12.5](https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/0d57d05457d8aeaef70ccd986853951b72428a7e) - 2023-08-01

### Performance(1)

- Config Serilog tối ưu CPU

### changed (1)

- Tuning chức năng export excel: xóa file trên local server, minio sau khi export thành công

### Fixed (1)

- fix lỗi lưu lệch giờ ở api điều chỉnh tăng giảm api tích hợp 01

- [NSHN] Đ<PERSON>a thông báo lỗi định dạng MST ra màn hình

## [5.12.6](https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/d3c1fe4e78699cca2c80c5c8cc90bef33551669e) - 2023-08-08

### Added (1)

- [NSHN] Đưa thông báo lỗi định dạng MST ra màn hình

## [5.12.7](https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/d296904c24b1e8ce04d415df21e8ca00b6b2b04e) - 2023-08-10

### Fixed (2)

- [Family Mart] Fix lỗi không ĐKSD được khi nhập chuỗi 10 chữ số liên tiếp ở đầu CTS không trùng với MST của tài khoản
- [Hóa đơn bán hàng] Fix lỗi không tìm kiếm được dữ liệu theo header extra

## [5.12.8](https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/8f3dce56c3d5ccbb8355a5658e42c6b08afd3727) - 2023-08-12

### Fixed (1)

- Refactor luồng đồng bộ trạng thái ký về Oracle

## [5.12.9] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/8863fe8832efffec255e24d3858dbf44db78d749) - 2023-08-18

### security (1)

- Ẩn link swagger

## [5.12.10] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/16f070aef14c7b4776d77968457253dba15bb146) - 2023-08-23

### Added (1)

- [NSHN] Thêm chức năng hóa đơn điều chỉnh tăng giảm cho sửa phần header extra

## [5.12.11] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/0cbc314470b55e9d94e23fbe8d4cf4380cbed7d0) - 2023-08-25

### Added (1)

- Chuyển dùng tvan kê khai tvan.vninvoice.vn sang tvan hóa đơn

### Fixed (2)

- [NSHN] Lỗi không có giá trị Phí BVMT khi export Bảng kê nội bộ

- [NSHN] Lỗi tạo hóa đơn với khối có nhiều dữ liệu

## [5.12.12] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/bb7316d6b89d739776356f3106836a09599e8ab1) - 2023-09-13

### Fixed (3)

- Sửa thẻ họ và tên người mua hàng trên XML hóa đơn

- Lỗi không xóa được mẫu hóa đơn

- [NSHN] Lỗi sai số lượng ở bảng kê nội bộ

## [5.12.13] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/72b6eae41c8e32105d4ce2e8fa3e10da95d84091) - 2023-09-22

### Fixed (2)

- [NSHN] Xử lý export báo cáo từ trước tới thời điểm hiện tại

- [NSHN] Lỗi sai giá trị cột Tổng thuế GTGT ở Bảng kê nội bộ

## [5.13.0] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/9cffb66c8cb9630ad9eeab1ce0fcec5746658dfc) - 2023-10-04

### Added (1)

- Xây dựng chức năng cảnh báo số lượng hoá đơn gần hết

### changed (2)

- Sửa luồngg cập nhật License

- Thêm logic check chỉ cộng license với những hoá đơn nào tạo ra trong khoảng thời gian dùng license

### fixed (5)

- fix lỗi không đồng bộ thông tin khách hàng sang phần danh mục và tạo tài khoản tra cứu

- fix Family mart – Hóa đơn 01: Lỗi không chặn Số lượng nhập giá trị âm khi import hóa đơn

- fix Báo cáo 01: Chưa trừ tiền chiết khấu ở thành tiên trước thuế trên báo cáo

- fix lỗi đồng bộ license hóa đơn 02

- fix Lỗi không load được danh sách hóa đơn theo group khách hàng

## [5.13.1] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/37777bab8508e3eeb3642ad910cc404429fbdb01) - 2023-10-19

### changed (1)

- [NSHN] Thêm tên đơn vị xuất bảng kê

### fixed (4)

- [Khối bệnh viện] Dữ liệu trung gian không có detail

- [Ký tự động] Thay đổi cấu hình ký tự động có/không nhưng redis không ghi nhận thay đổi

- Sửa hóa đơn 01 nhưng không clear Tổng tiền CK sau thuế

- Lỗi update ProductType = 0 trên mongo khi call api update hóa đơn DCTG

## [5.14.0] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/0cb1927c7de5106475103aae505032a8f581ece2) - 2023-11-24

### fixed (1)

- Signclient đang không ký được hóa đơn điều chỉnh không có detail

## [5.14.1] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/7bb30e7e243722271a9abc71bef894f4cf7c8ea3) - 2023-11-30
### Added (1)

- Điều chỉnh cấu hình các service kết nối redis sentinel (Ngày 2023-12-07)

### changed (5)

- [NSHN] Cho phép điều chỉnh thông tin các loại phí
- [NSHN] Lập hoá đơn điều chỉnh
- [NSHN] Logic chỉnh sửa PBVMT
- [NSHN] Bổ sung cảnh báo lỗi kết nối khi không tạo được hoá đơn tự động
- [NSHN] Cho phép điều chỉnh thông tin các loại phí
### fixed (2)

- [NSHN] Lỗi xuất bảng kê nội bộ NSHN bị chậm
- [BE][FML] Hàm update hóa đơn điều chỉnh đang check cả ngày của chính hóa đơn đang được thực hiện

## [5.14.2] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/0f62883d483f55d4f120a4e89b35dc1bfa7758df) - 2023-12-13

### changed (2)

- [ĐC/TT nhiều lần- HĐ 01 GTGT] Bổ sung cột Tiền thuế vào Chi tiết hóa đơn (Gốc/điều chỉnh/thay thế)
- [CR] Thông báo ra màn hình khi mất kết nối

### fixed (1)

- [API tích hợp] Lỗi API sửa hoá đơn thay thế

## [5.15.0] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/3bb45174719d6b867eaddbf3190b507e1d35a47f) - 2023-12-22
### Added (3)

- Chức năng cảnh báo về CTS hết hạn
- [NSHN] Xây dựng API tra cứu hóa đơn
- [NSHN] Trả phản hồi số hóa đơn, ký hiệu, trạng thái hoá đơn sang phần mềm lõi

### changed (6)
- Rà soát lại các loại ký tự dấu + cho phép nhập của trường địa chỉ người mua và các trường khác + hiển thị khi lên XML (1450) cho hóa đơn 01 và các hóa đơn khác
- Chỉnh sửa api clone mẫu hóa đơn năm mới
- [Signserver] Xử lý các lỗi liên quan đến convert các ký tự đặc biệt dấu + khi ký
- Tối ưu các query OracleDB
- (CR) Sửa logic chứng từ thuế TNCN
- [CR] Thay đổi logic thời gian tạo Chứng từ TNCN
### fixed (4)

- Fix các lỗi bảo mật
- [BE][Quản lý khách hàng] Nhấn tạo mới khách hàng đang báo lỗi
- [BE][Quản lý khách hàng] Lỗi đang không xóa được khách hàng
- [Chứng từ thuế TNCN] Tổng hợp các lỗi phát sinh

## [5.16.0] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/d76ae0c237d65a4a175d299072097cc84af34c9e) - 2024-01-04
### Added (1)

- Xây dựng chức năng tra cứu cho chứng từ thuế TNCN
### fixed (5)

- [NSHN] Lỗi xuất bảng kê nội bộ NSHN bị chậm: phạm vi Bảng kê NSHN cũ đã golive ngày 30/11 nhưng rollback để rà soát
- [BE][API tích hợp] Lỗi hàm update đang check cả ngày hóa đơn của chính bản ghi đang focus
- [CR-NSHN] Bỏ bắt buộc nhập với trường PTPBVMT
- [NSHN] Một số lỗi của bảng kê nội bộ
- [BE][Chứng từ thuế TNCN] Mẫu hết số CurrentNumber = EndNumber vẫn tạo được chứng từ bằng cách call API

## [5.16.1] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/76a09a9b55327164d49b4ba70eeeaf72d53b0f10) - 2024-01-09
### Added (3)
- [CR][ĐC/TT nhiều lần- HĐ 01 GTGT] Bổ sung các xử lý liên quan đến chiều chỉnh detail extra
- In thể hiện hóa đơn bị thay thế không có gạch chéo
- [NSHN] Cảnh báo số lượng email gửi trong ngày quá 500 mail

### fixed (3)

- [5.0] [DC/TT] Chưa validate tiền âm với hóa đơn gốc, thay thế của loại hóa đơn 02
- [Lập điều chỉnh/TT khác] Lỗi phát sinh
- Lỗi định dạng khi tạo/edit chứng từ thuế TNCN

## [5.16.2] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/6b92f4e9baab64b0d7a3553bf83b1eb2fc42d851) - 2024-01-10

### fixed (2)

- [NSHN] Báo cáo 01 - Lỗi hiển thị lặp hóa đơn thay thế khi export báo cáo
- [Báo cáo 01] Lỗi sai thông tin hóa đơn liên quan khi thay thế từ lần thứ 3 trở đi

## [5.16.3] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/047dd194c43ecd225ffcf63a617e43138bc2aa81) - 2024-01-15

### fixed (1)

- [BE][BUG][Tạo hóa đơn NSHN bằng mã khách hàng] Hóa đơn liên quan của hóa đơn thay thế chưa đổi trạng thái tại màn danh sách

## [5.16.4] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/f0985d658c7095177cce2aa05f03507589285e60) - 2024-01-16

### fixed (5)

- [Hotfix] Các lỗi phát sinh của báo cáo 01
- [Báo cáo 01] (998) - Lỗi truyền sai thẻ trạng thái đối với hóa đơn điều chỉnh
- [Báo cáo 01] (nước sạch HN) - Lỗi XML ghi nhận sai thẻ trạng thái với hóa đơ điều chỉnh, excel+ view detail trên form không hiển thị trạng thái hóa đơn diều chỉnh
- [Báo cáo 01] (998) - Lỗi truyền sai thẻ Loại hóa đơn có liên quan của hóa đơn thay thế/điều chỉnh khác
- [Báo cáo 01] (998) - Lỗi không có thông tin hóa đơn liên quan và ghi chú của hóa đơn thay thế ở hóa đơn 04 và vé trên file export

## [5.17.0] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/2c2b7061bb2275fc668e2b139e89acddd64fb340) - 2024-01-29
### changed (1)
- [Điều chỉnh/thay thế khác] Api tích hợp sửa hóa đơn điều chỉnh thay thế khác đang check sau logic ngày hóa đơn
### fixed (2)

- [NSHN] Cho phép tạo hóa đơn theo Mã khách hàng + bổ sung logic lập điều chỉnh/TT nhiều lần qua API nước sạch
- [FE] Thuế TNCN - Lỗi không tạo được mẫu chứng từ khi MST chưa có ĐKSD

## [5.17.1] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/0753d0faebf52f19844034c2e08fe096de620684) - 2024-02-06

### fixed (1)

- [NSHN][live]Khi tạo hóa đơn tự động không lưu các thông tin chỉ số phụ ở extral

## [5.18.0] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/36c6fe05c385d3f4a0a506b819d9b0119264f5f0) - 2024-02-27

### improvement(2)
- Cập nhật signclient đáp ứng chuẩn TLS 1.2
- Chỉnh sửa cấu hình prefetchcount ở rabbitmq

### changed (2)
- [CR]Bổ sung điều kiện khi làm điều chỉnh HĐ có mã
- Sửa luồng cập nhật License

### added (1)
- Thêm các api để hỗ trợ vận hành hệ thống

## [5.19.0] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/6c7a8ddcdf6b02eba608622748f9e3faafccec0c) - 2024-03-11

### added (8)

- Cập nhật trạng thái TBSS

- Cập nhật chức năng quản lý thông báo sai sót

- [PXK] Tạo TBSSS cho phiếu xuất kho kiêm vận chuyển nội bộ + hàng gửi bán đại lý (không mã, có mã)

- Tạo TBSS cho Hóa đơn Thay thế, Hóa đơn bị thay thế, Hóa đơn Hủy, Hóa đơn bị điều chỉnh

- Bổ sung số thông báo của CQT trong DS TBSS

- Thống kê dữ liệu HĐ từ hệ thống CQT

- Bổ sung cột TT tiếp nhận của CQT khi xem chi tiết TBSS

- (CR) Cập nhật thay đổi cho TBSS

### changed (1)

- Rollback lại bản license của ngày 27/02

- [HĐ GTGT] Bỏ logic bắt buộc nhập  Mã KH, Tên đơn vị, Địa chỉ

### improvement(1)

- Chỉnh sửa cấu hình prefetchcount ở rabbitmq (deploy bổ sung các worker)

## [5.19.1] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/d30b5c46fbb5ce31dc07d9095b98ce942992a592) - 2024-03-23

### security(1)

- Fix lỗi bảo mật IDOR ở trang tra cứu portal

## [5.20.0] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/1aa369a49dad02d96fe27e1b8ea789b2994f2e37) - 2024-03-28

### added(4)

- Xây dựng chức năng điều chỉnh cho vé điện tử trên form

- Xây dựng chức năng điều chỉnh/ Thay thế nhiều lần cho vé điện tử sử dụng API

- Xây dựng chức năng thay thế nhiều lần cho vé điện tử trên form

- [Tra cứu] Không cho phép tra cứu hóa đơn chưa được cấp mã

### fixed (1)

- [NSHN][LIVE] Gửi thiếu số bảng tổng hợp lên Tvan và update sai trạng thái của hóa đơn

## [5.21.0] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/893106f3e48fc4eb4295fbf6c328181eb0e82c94) - 2024-04-11

### security(5)

- API tìm kiếm hoá đơn sau của url https://invoice-mass-portal.vnpaytest.vn/invoice-search không yêu cầu xác thực, qua đó có thể thực hiện tìm kiếm hàng loạt thông tin hoá đơn

- Captcha của trang tìm kiếm đang để expire theo thời gian (3 phút). Trong thời gian captcha còn hiệu lực vẫn có thể thực hiện tìm kiếm thông tin liên tục

- Thiếu cơ chế xác thực trong một số chức năng

- [IDOR] Ứng dụng không kiểm tra phân quyền theo chiều ngang

- Captcha của trang tìm kiếm đang để expire theo thời gian (3 phút). Trong thời gian captcha còn hiệu lực vẫn có thể thực hiện tìm kiếm thông tin liên tục

### improvement(1)

- Tối ưu query lấy hóa đơn để truyền lên tvan

### fixed(1)

- [Vé điện tử 05] API create-batch: Lỗ hóa đơn bị tạo trùng erpId

## [5.21.1] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/3cf9b29586c7df173a02be6938b592719cff5149) - 2024-04-23

### fixed(1)

- HĐ GTGT- Bổ sung hiển thị tên bên mua trên màn hình danh sách hóa đơn

## [5.22.0] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/8bb923eadc7edafef5327fa0354323a734008f73) - 2024-04-23

### added(1)

- [Familymart] Thêm chức năng import file csv dữ liệu hóa đơn vào hệ thống

## [5.23.0] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/fdb9c36373445c9674a7de34acc208e27dadb5ae) - 2024-05-06

### fixed(4)

- Hóa đơn 02: Bị double dữ liệu detail trên oracle

- HD GTGT- API Create-batch and sign: Lỗi ghi nhận sai đơn vị tiền tệ và đồng bộ trạng thái ký

- Hóa đơn: Thuế trả lỗi sai định dạng khi email chưa khoảng trắng

- [Family Mart] Lỗi cache khi đổi cấu hình ký tự động

### security(1)

- Checklist nghiệp vụ yêu cầu đặt mật khẩu

### changed (1)

- CR-Chứng từ khấu trừ thuế TNCN - Bỏ Validate thẻ CCCD/CMND/Hộ chiếu

## [5.24.0] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/ad5c76afeeacbf02f89ec5ba80293fa724f2588c) - 2024-05-23

### added(1)

- [Hóa dơn 01] Bổ sung chức năng import HĐ điều chỉnh

### changed(1)

- [HĐ GTGT] Chỉnh sửa logic tạo XML đối với các HĐ có phần CKTM + Khuyến mại

### improvement(1)

- Dọn dẹp code và các nhánh của dự án

- Log các thông tin khi gọi sang các 3rd

## [5.24.1] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/ba3977f9bfbe8992fcfcb2f82fed0428f98e0a3e) - 2024-05-27

### fixed (5)

- [Điều chỉnh khác] Lỗi đang chặn không cho lập hóa đơn điều chỉnh khác có các giá trị âm

- Lỗi đang không lập được hóa đơn điều chỉnh khi thiếu thông tin chi tiết thuế suất

- Sửa HĐ điều chỉnh khác - Lỗi không cho sửa hóa đơn có giá trị âm

- Lỗi hiển thị không đúng discountType ở màn sửa hóa đơn điều chỉnh khác

- Lỗi đang gửi thiếu trường discountType khi thực hiện sửa hóa đơn điều chỉnh khác (TT32)

## [5.24.2] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/1f92d0d14944416438e4674a9fbb391d1c93da5a) - 2024-05-31

### fixed (2)

- Import excel sai logic tính toán trường hợp thuế suất là kCT (-1) và KKK (-2)

- [import with setting] Khi tạo HĐ GTGT trường thành tiền tại bảng thuế suất = 0

### changed (2)

- [CR] Xử lý logic tạo HĐ (Với các KH xi măng ) mà chi tiết dòng hàng chỉ có duy nhất loại hàng hóa là CKTM (productType =3)

- [CR] Update logic lập HĐ có dòng hàng chiết khấu thương mại là loại CK là chiết khấu theo dòng hàng (ProductType =3)

## [5.24.3] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/deb5a3a0a945bd0f1b0223d9bd089d4752f54739) - 2024-06-04

### fixed (1)

- [Import with Setting] Bỏ các logic check validate tổng tiền, tổng tiền thuế, tổng tiền chiết khấu với luồng import with setting

## [5.24.4] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/d350319fe41ce93ebbd0b8398ce44be260144fb2) - 2024-06-05

### fixed (5)

- Bổ sung logic xử lý trong TH số lượng TS trong Taxtbreakdown lệch với số lượng thuế suất khai bảo ở HHDV

- Các bug liên quan tới thuế suất sau khi update logic CK/TM ( trường invoiceTaxBreakdowns)

- Thêm cấu hình có/không option thêm cấu hình tự động tính toán lại chi tiết thuế suất taxbreakdown

- Bỏ các logic check validate tổng tiền, tổng tiền thuế, tổng tiền chiết khấu với luồng import with setting

- Xử lý logic tạo HĐ (Với các KH xi măng ) mà chi tiết dòng hàng chỉ có duy nhất loại hàng hóa là CKTM (productType =3)

## [5.24.5] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/e2b143e06e57d69e643043d20f134edd9de13998) - 2024-06-17

### changed (1)

- [Import with setting] Bỏ logic Tổng thành tiền các dòng hàng = Tổng thành tiền (HĐ)

### fixed (1)

- Gửi thiếu trường taxBreakdown trong trường hợp tạo HĐ điều chỉnh GTGT

## [5.24.6] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/020d96f6cae07c16da27501dd83bfda18939dc41) - 2024-06-19

### added (1)

- Api hỗ trợ treo thông báo hệ thống

### improvement (2)

- Thay đổi format file Logging (Serilog)

- Check cảnh báo network server RabbitMQ

### changed (1)

- Xử lý case bật cầu hình tự động tính Chi tiết thuế suất

## [5.24.7] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/d39758fbf4e17697078b889f0013213dde89a5a6) - 2024-06-19

### fixed (2)

- Sai thời gian quét license và gửi cảnh báo theo tuần

- Lỗi đăng nhập vào signserver

## [5.25.0] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/2c543c9a7fff6dcb81b1319cda5232d2e2aa5607) - 2024-06-28

### Performance ()

- Tối ưu connection Pool ở portal 

### changed (2)

- Khi tạo user name (chữ viết hoa) nhưng khi hiển thị lại là chữ thường, khi đăng nhâp bằng Username viết Hoa thì khong thể tra cứu HĐ

- Thay đổi thông tin địa chỉ email tại nội dung mail cảnh báo license

## [5.26.0] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/8c86e5728d7f3cd5b4e6d5a13b00723dea3ad10e) - 2024-07-04

### Performance ()

- Tuning xuất dữ liệu excel hóa đơn, báo cáo

- Tuning cho vé điện tử

### fixed (2)

- Đọc sai số tiền hóa đơn (Nghìn tỷ)

- Chỉnh sửa lại tên cột V và W tại template import HĐ GTGT 01

- [HĐ GTGT + HĐ BH] Màn hình loading khi ký hóa đơn xóa hủy chưa được duyệt xóa hủy

### added (1)

- Thêm tính năng xóa nhiều chứng thư số

## [5.26.1] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/79e8081115bb47c905c018c1312a26f800c40870) - 2024-07-08

### fixed (2)

- Import excel Hệ thống đang tự đông tính lại thành tiền => Thành tiền nhập tại file excel đang khác với thành tiền trên web

- Lỗi tra cứu tài khoản có ký tự in hoa

## [5.26.2] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/d0a55ebbef852a0d284306c023df8441f9959640) - 2024-07-16

### security (6)

- Ứng dụng không kiểm tra phần quyền theo chiều ngang

- Ứng dụng không kiểm tra quyền ở các API (Abp/ServiceProxyScript)

- Ứng dụng không kiểm tra quyền ở 1 số API

- Xóa mẫu email

- [Unauthen LeakData] Api ExportPdf

- Ứng dụng không cho phép sửa username, tuy nhiên nếu thay đổi param username ở requests. Server vẫn chấp nhận và thay đổi thông tin

### performance (1)

- Tối ưu câu lệnh lấy DS vé điện tử

### added (1)

- Thêm cấu hình mở chặn Import with setting cho hóa đơn gốc tiền âm

## [5.26.3] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/7eec069b701d55118a0c06cbdabb09196810978f) - 2024-07-19

### fixed (1)

- Khi import điều chỉnh đang validate bắt buộc nhập cột I4 Tên người mua hàng

## [5.26.4] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/8d59fb2ad355b1e11d64b56fad1292cded3cc86a) - 2024-07-31

### fixed (1)

- Bỏ validate tiền đối với hoá đơn điều chỉnh (chỉ check điều chỉnh giảm vượt quá số tiền với hoá đơn gốc)

## [5.26.5] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/2cacc14bd8320c9a260cc4159a3ecc21274f5b8d) - 2024-08-07

### fixed (3)

- Hóa đơn 02-Api Create batch and sign: Lỗi đồng bộ trạng thái ký

- Hóa đơn: Sửa nội dung thông báo khi import hóa đơn có dữ liệu #N/A

- Tạo thiếu và ký lỗi HĐ khi server nhận một lúc nhiều request (200-300)

- Chỉnh sửa logic truyền hóa đơn xóa bỏ trên báo cáo 01/TH-HDDT

### added (1)

- Thêm cảnh báo hóa đơn không mã chưa nhận được phản hồi của CQT trước khi gửi TBSS

### security (2)

- Tổng hợp lỗi bảo mật thông tin tài khoản

- Sử dụng user thường, tạo mới thông tin KH trên hệ thống trái phép

### Performance (3)

-Chỉnh sửa nghiệp vụ trước khi chuyển đổi database Oracle

- Tuning ký theo lô

- Chỉnh sửa giới hạn thời gian tìm kiếm hóa đơn

## [5.26.6] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/3db6c2e6837a850add6a7199650e91b2e05f5c67) - 2024-08-14/16

### fixed(3)

- Tra cứu hóa đơn của tài khoản này, đang hiển thị hóa đơn của tài khoản khác, trên màn hình tra cứu HĐ portal đã đăng nhập

- Khi tạo user name (chữ viết hoa) nhưng khi hiển thị lại là chữ thường, khi đăng nhâp bằng Username viết Hoa thì khong thể tra cứu HĐ

- Lỗi đồng bộ thiếu dữ liệu Hóa đơn từ ES lên form

## [5.26.7] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/acb115d79386ba047abafda9925df6a03d018d5c) - 2024-08-20

### fixed(4)

- [Gửi mail tới khách hàng] Thực hiện gửi mail thành công nhưng kiểm tra địa chỉ mail đã nhập thì không có mail

-  Validate lại trạng thái hóa đơn đủ điều kiện để thay thế/điều chỉnh khi đã pass validate và message đã vào trong queue

- [FE] Chức năng edit số tiền đang hoạt động không đúng

- Chặn tạo trùng ErpId khi thực tạo HĐ bằng jmeter

### Performance (1)

- Thêm index các bảng truyền nhận 

### changed (1)

- Chỉnh sửa lại giới hạn tìm kiếm trên danh sách hóa đơn (Màn hình HĐ user k phải portal)

### added (3)

- Thêm "Họ và tên người mua hàng" tại BKCT, BKTH và chỉnh sửa 1 title cột dữ liệu 

- Thêm action tìm kiếm ở ngoài thành input

- Màn hình tra cứu hóa đơn portal theo mã bảo mật (tra cứu không đăng nhập) bổ sung thêm điều kiện tìm kiếm (Ngày HĐ từ và Đến)

## [5.27.0] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/31e53245cf1d41f779fd1ef11a3b4b4744cd8d70) - (2024-09-26) 

### added (6)

- Chặn không cho phép lập TBSS khi hóa đơn đã được CQT chấp nhận Hủy

- Thêm chức năng export cho phần quản lý TBSS

- Bổ sung logic tạo TBSS cho hóa đơn ngoài hệ thống

- Bổ sung chức năng Thêm HĐ sai sót ở màn hình lập TBSS

- Thêm chức năng gửi mail cho phần quản lý TBSS

- Chỉnh sửa một số logic tại CN Thêm hóa đơn sai sót vào TBSS

### fixed (1)

- Thao tác ký HĐ trên signclient nếu bị lỗi thì đang bị khóa thao tác hóa đơn không thể thao tác ký lại hóa đơn

## [5.27.1] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/7fef52b9575117da66c05d2eaca929841645a3f7) - (2024-10-10)

### fixed(2)

- Gửi email đặt lại mật khẩu không thành công

- Email đặt lại mật khẩu có nội dung mật khẩu đặt lại chứa ký tự(<>) khiến nội dung mật khẩu hiển thị trắng

- fix lock luồng ký tự động theo giờ (sign schedule)

## [5.27.2] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/7c2118e290a066ea28cb3f058614e9dd52b709fe) - (2024-10-15)

### fixed (1)

- fix ký thông báo sai sót của hóa đơn ngoài hệ thống (sai thông tin thẻ LADHDDT)

## [5.27.3] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/b6647f013455959148cc2f6a9b7c448b13ac1ca8) - (2024-10-30)

### improvement (5)

- Đánh Index trường "ConcurrencyStamp" bảng EINVOICEAUTH."VnisUsers"

- Đánh index theo trường "Expiration" bảng EINVOICEAUTH."VnisPersistedGrants"

- Đánh index cho trường IdInvoiceTp bảng VNISDBTG."Invoice01Header"

- Đánh index cho trường InvoiceHeaderId bảng EINVOICECORE50."Invoice02Xml"

- Drop các index không hiệu quả

## [5.27.4] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/04ab1c37aef93c3d42829d810c7f26a6d550e86e) - (2024-11-07)

### improvement (3)

- [FM] Tuning luồng tạo hóa đơn điều chỉnh

- [FM] Tuning tạo hoá đơn tự động của FM

- Chỉnh sửa luồng xác định MTD của phản hồi 202

### hotfix (2)

- [BE] [Sửa HĐ bằng excel] Validate ngày HĐ khi sửa HĐ bằng excel logic đang chưa đúng

- [BE] API tích hợp 05 báo ErpID đã tồn tại khi trước đó cùng số ErpID đó tạo HĐ 05 bị lỗi dữ liệu ở các giá trị nhập vào details

## [5.27.5] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/22cb6c225247eed5bced91ac5c6f2cfbdf54d90d) - (2024-11-12)

### feature (1)

- Thêm tính năng export bảng kê lịch sử email (update (07/11): Sửa lại luồng Resend mail)

### improvement (1)

- [FE] Chỉnh sửa lại cấu hình làm tròn đơn vi hàng thập phân của các đơn vị tiền thuộc loại ngoại tệ (Nguyên tệ không ảnh hưởng)

### hotfix (1)

- [Import Excel] Lỗi tạo trùng erpId khi import hóa đơn

## [5.27.6] (https://git-dvnh.vnpay.vn/vnpay-invoice/invoicev5-backend-services/-/commit/a2e7ac8117451ef5e74283659977d72789733249) - (2024-12-19)

### hotfix (1)

- VINMASS-4815: [NSHN][5.0] Export tổng hợp, chi tiết và báo cáo 01 đang không hiển thị lẻ thập phân

## [5.27.7] - (2024-12-30)

### hotfix (1)

- VINMASS-4828: [Hóa đơn bán hàng 02] Lập DC/TT khác hóa đơn BH - Lỗi không nhập đươc giá trị âm với hóa đơn lập điều chỉnh giảm

## [5.28.0] - (2025-01-07)

### feature (1)

- VINMASS-4803: Tuning getlist invoice03
2025
## [5.28.1] - (2025-01-15)

### feature (2)

- VINMASS-4780: [5.0] Export bệnh viện (02) lấy không đúng trạng thái bị thay thế
- VINMASS-4781: [5.0] Thêm hoá đơn có trạng thái bị thay thế vào sheet xoá của export service của bệnh viện

### improvement (1)
- VINMASS-4840: Refactor flow đồng bộ ErpId của Invoice01 và Invoice02


## [5.28.2] - (2025-01-24)

### hotfix (1)

- VINMASS-4844: [BUG] Không ký thành công TBSS bằng signclient (Ký HDGTGT)

## [5.28.3] - (2025-02-17)

### feature (1)

- VINMASS-4821: Rà soát lỗi liên quan Clone mẫu năm cũ, update mẫu năm cũ hết hạn (Family mart, Nước sạch HN, MIC invoice, Bệnh viện, Mass V5)

### hotfix (4)

- VINMASS-4791: [BUG] Nhập giá trị filter theo tiêu chí nguồn tạo (source) => đang không filter được
- VINMASS-4645: [BE] khi thực hiện export HĐ GTGT bị lệch tổng số bản ghi
- VINMASS-1256: In thể hiện hóa đơn bị thay thế không có gạch chéo
- VINMASS-4846: Lỗi tạo hóa đơn có trùng erpid trên loại hóa đơn 01

## [5.28.4] - (2025-02-26)

### hotfix (1)

- VNPINVV5-4848: [BE][NSHN] Cấu hình export "NSHN" thiếu mẫu hóa đơn TAL

## [5.28.5] - (2025-03-21)

### hotfix (1)

- VNPINVV5-4851: Lỗi tạo hoá đợn điều chỉnh định danh NSHN

## [5.28.6] - (2025-04-18)

### hotfix (2)

- VNPINVV5-4859: XML không validate được trên trang hóa đơn điện tử của TCT
- VNPINVV5-4858: Lỗi hóa đơn thay thế lần 1 in thể hiện reference bị lỗi

## [5.28.7] - (2025-04-21)

### refactor (1)

- VNPINVV5-4800: Thay thế thư viện tính phí IText bằng PdfSharpCore


## [5.28.8] - (2025-05-06)

### hotfix (1)

- VNPINVV5-4865: [BVDKKH] Lỗi sai thông tin SellerFullName và CreatorBy


## [5.29.0] - (2025-05-31)

### feature (1)

- VNPINVV5-4939:	[NĐ 70] Chặn hủy hóa đơn có ngày hóa đơn lớn hơn hoặc bằng ngày 01/06


## [5.30.0] - (2025-06-10)

### feature (8)

- VNPINVV5-4939	[NĐ 70] Chặn hủy hóa đơn có ngày hóa đơn lớn hơn hoặc bằng ngày 01/06
- VNPINVV5-4867	[NĐ 70] Update hóa đơn GTGT
- VNPINVV5-4868	[NĐ 70] Update hóa đơn bán hàng
- VNPINVV5-4869	[NĐ 70] Update vé điện tử (Invoice ticket)
- VNPINVV5-4870	[NĐ 70] Update PXK kiêm VCNB- Phạm vi XML
- VNPINVV5-4871	[NĐ 70] Update PXK hàng gửi bán đại lý- Phạm vi XML dữ liệu PXK
- VNPINVV5-4940	[NĐ 70] Bổ sung các trường cho hóa đơn có chứa hàng hóa đặc trưng (Phạm vi HĐ GTGT)
- VNPINVV5-4954	[Báo cáo 01] Update NĐ 70 - bổ sung mã quan hệ với ngân sách

## [5.30.1] - (2025-06-27)

### hotfix (1)

- VNPINVV5-5120 [NSHN] Lỗi cache phân quyền mẫu, export BK NSHN không có dữ liệu với hai loại mẫu có ký hiệu TTT và TNL 


## [5.31.0] - (2025-07-01)

### feature (8)

- VNPINVV5-4866	[NĐ 70] Cập nhật tờ khai đăng ký/thay đổi thông tin sử dụng HDDT
- VNPINVV5-4872	[NĐ 70] Thông báo HĐĐT có sai sót
- VNPINVV5-4875	[NĐ 70] Tờ khai đăng ký/thay đổi Chứng từ điện tử lên CQT
- VNPINVV5-4956	[NĐ 70] Chặn hủy với các hóa đơn không mã gửi theo bảng tổng hợp
- VNPINVV5-4993	[Báo cáo 01] Update NĐ 70 - Cập nhật các yêu cầu dữ liệu BTH
- VNPINVV5-5006	[ND 70 _CT TTNCN] Update Danh sách chứng từ khấu trừ thuế TNCN
- VNPINVV5-5007	[ND 70 _CT TTNCN] Update thêm mới/ chỉnh sửa chứng từ khấu trừ thuế TNCN
- VNPINVV5-5008	[ND 70_CT TTNCN] Update chi tiết chứng từ khấu trừ thuế TNCN
- VNPINVV5-5009	[ND 70 _ CT TTNCN] Gửi CQT chứng từ khấu trừ thuế TNCN
- VNPINVV5-5017	[ND 70 _CT TTNCN] Update in PDF chứng từ khấu trừ thuế TNCN
- VNPINVV5-5026	[ND 70] Bổ sung thêm model in thể hiện cho hàng hóa đặc trưng


## [5.31.1] - (2025-07-03)

### feature (1)

- VNPINVV5-5133	Mở cho phép sửa thông tin doanh nghiệp

## [5.31.2] - (2025-07-04)

### feature (1)

- VNPINVV5-5127	[NSHN] [NĐ70] Thêm các trường thông tin api

### hotfix (2)

- VNPINVV5-5014	[Bảo mật] Nguy cơ DOS tài khoản người dùng
- VNPINVV5-5015	[Bảo mật] Nguy cơ thao tác các request bypass SSO login trên trang https://invoice-mass.vnpaytest.vn


## [5.31.3] - (2025-07-10)

### feature (1)

- VNPINVV5-4842	Update định dạng MST (bổ sung 12 ký tự)

## [5.31.4] - (2025-07-16)

### feature (4)

- VNPINVV5-5139	[ND 70] sửa lại xml của loại mã thông điệp 301
- VNPINVV5-5147	[ND 70] Bổ sung các trường thông tin mới vào form tạo excel
- VNPINVV5-5111	bổ sung luồng chặn hủy với các hóa đơn từ 31/05 trở về trước
- VNPINVV5-5152	[ND 70] Bổ sung đăng ký sử dụng, thay đổi đăng ký sử dụng

## [5.31.5] - (2025-07-19)

### feature (2)

- VNPINVV5-5199	[BE] Chỉnh sửa lại text nội dung thông báo sai (sau khi thêm 3 trường)
- VNPINVV5-5200	[BE] Khi cập nhật smt 12 ký tự chưa check đồng bộ với MST tại cts của DKSD sprint 50

## [5.31.6] - (2025-07-28)

### hotfix (1)

- VNPINVV5-5269	[BE] Lỗi không sửa được phiếu xuất kho hàng gửi bán đại lý

## [5.32.0] - (2025-07-31)

### feature (12)

- VNPINVV5-5010	[ND 70 _ CT TTNCN] Lập chứng từ khấu trừ thuế TNCN điều chỉnh
- VNPINVV5-5018	[ND 70_CT TTNCN] Lập chứng từ khấu trừ thuế TNCN thay thế
- VNPINVV5-5020	[ND 70 _CT TTNCN] [TBSS] Thêm mới thông báo chứng từ sai sót
- VNPINVV5-5021	[ND 70 _CT TTNCN] Chỉnh sửa thông báo chứng từ sai sót
- VNPINVV5-5024	[ND 70 _ CT TTNCN] Ký số và gửi TBSS chứng từ
- VNPINVV5-5016	[ND 70 _ CT TTNCN] Gửi CQT thông báo sai sót chứng từ điện tử
- VNPINVV5-5022	[ND 70 _ CT TTNCN] Chi tiết thông báo chứng từ sai sót
- VNPINVV5-5019	[ND 70 _ CT TTNCN] Danh sách thông báo chứng từ sai sót
- VNPINVV5-5159	[ND 70 CT TTNCN] Thêm tính năng tải xml tbss
- VNPINVV5-5230	[ND 70] Thêm tính in pdf tbss chứng từ
- VNPINVV5-5160	[ND 70 CT TTNCN] Thêm tính năng xóa tbss chưa ký
- VNPINVV5-5158	[ND 70 CT TTNCN] Thêm chức năng gửi mail tbss chứng từ

