using Core.Data;
using Core.Dto.Shared.Invoices.Invoice02;

using System;
using System.Collections.Generic;
using VnisCore.Core.Oracle.Domain.Interfaces.DecreeNo70;

namespace VnisCore.Core.MongoDB.Entities.Invoices.Invoice02
{
    public class MongoInvoice02Entity : MongoBaseInvoiceHeader, IInvoiceHeaderDecreeNo70
    {
        #region Thông tin người mua
        /// <summary>
        /// Id bản ghi người mua (Customer), có thể null nếu là loại hóa đơn 03XKNB
        /// </summary>
        public long? BuyerId { get; set; }

        /// <summary>
        /// Mã khách hàng
        /// </summary>
        public string BuyerCode { get; set; }

        /// <summary>
        /// Họ tên người mua (nếu là khách lẻ không thuộc công ty nào thì đây là tên công ty)
        /// </summary>
        public string BuyerFullName { get; set; }

        /// <summary>
        /// Đại diện pháp nhân người mua (trường hợp là công ty)
        /// </summary>
        public string BuyerLegalName { get; set; }

        /// <summary>
        /// Mã số thuế người mua
        /// </summary>
        public string BuyerTaxCode { get; set; }

        /// <summary>
        /// Địa chỉ người mua
        /// </summary>
        public string BuyerAddressLine { get; set; }

        /// <summary>
        /// Tên Quận/Huyện người mua
        /// </summary>
        public string BuyerDistrictName { get; set; }

        /// <summary>
        /// Tên thành phố người mua
        /// </summary>
        public string BuyerCityName { get; set; }

        /// <summary>
        /// Mã quốc gia người mua (Việt Nam = VN)
        /// </summary>
        public string BuyerCountryCode { get; set; }

        /// <summary>
        /// Số điện thoại người mua
        /// </summary>
        public string BuyerPhoneNumber { get; set; }

        /// <summary>
        /// Số FAX người mua
        /// </summary>
        public string BuyerFaxNumber { get; set; }

        /// <summary>
        /// Email người mua
        /// </summary>
        public string BuyerEmail { get; set; }

        /// <summary>
        /// Tên ngân hàng người mua
        /// </summary>
        public string BuyerBankName { get; set; }

        /// <summary>
        /// Tài khoản ngân hàng người mua
        /// </summary>
        public string BuyerBankAccount { get; set; }

        /// <summary>
        /// Thời điểm người mua ký
        /// </summary>
        public DateTime? BuyerSignedAt { get; set; }

        /// <summary>
        /// Họ tên người mua ký (Sử dụng tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public string BuyerFullNameSigned { get; set; }

        /// <summary>
        /// Tài khoản người mua ký hóa đơn (Sử dụng tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public Guid? BuyerSignedId { get; set; }
        #endregion

        #region Thông tin thanh toán
        /// <summary>
        /// Tổng tiền chiết khấu trước thuế, là lượng điều chỉnh nếu là hóa đơn tăng/giảm
        /// </summary>
        public decimal TotalDiscountAmount { get; set; }

        /// <summary>
        /// Phương thức thanh toán
        /// </summary>
        public string PaymentMethod { get; set; }

        /// <summary>
        /// Loại chiết khấu
        /// 0 : Không chiết khấu
        /// 1 : Chiết khấu hàng hóa
        /// 2 : Chiết khấu tổng
        /// </summary>
        public short DiscountType { get; set; }

        #endregion

        #region Thông tin tích hợp
        /// <summary>
        /// Id bản ghi người mua bên ERP
        /// </summary>
        public string BuyerErpId { get; set; }

        public string IdInvoiceTp { get; set; }
        #endregion

        public decimal TenantGroup { get; set; } = 5;

        public int InvoiceGroup { get; set; } = 0;

        /// <summary>
        /// tổng số hóa đơn có trong lần tạo
        /// </summary>
        public int TotalInvoices { get; set; } = 0;

        /// <summary>
        /// -1: đồng bộ lỗi 
        /// 0: chưa đồng bộ về core
        /// 1: đã đồng bộ về core
        /// </summary>
        public short IsSyncedToCore { get; set; } = 0;

        /// <summary>
        /// -1: đồng bộ lỗi
        /// 0: chưa đồng bộ
        /// 1: đã đồng bộ
        /// 2: đồng bộ sửa
        /// enum: SyncElasticSearchStatus
        /// </summary>
        public short IsSyncedToElasticSearch { get; set; } = 0;

        // <summary>
        /// -1: đồng bộ lỗi
        /// 0: chưa đồng bộ
        /// 1: đã đồng bộ
        /// </summary>
        public short IsSyncSignTocore { get; set; }

        /// <summary>
        /// -1: đồng bộ lỗi 
        /// 0: chưa đồng bộ về dbtg
        /// 1: đang đồng bộ về dbtg
        /// 2: đã đồng bộ về dbtg
        /// </summary>
        public short IsSyncedInvoiceToDBTG { get; set; } = 0;

        /// <summary>
        /// -1: đồng bộ lỗi 
        /// 0: chưa đồng bộ về core
        /// 1: đang đồng bộ về core
        /// 2: đã đồng bộ về core
        /// </summary>
        public short IsSyncedProductToCore { get; set; } = 0;

        /// <summary>
        /// -1: đồng bộ lỗi 
        /// 0: chưa đồng bộ về core
        /// 1: đang đồng bộ về core
        /// 2: đã đồng bộ về core
        /// </summary>
        public short IsSyncedUnitToCore { get; set; } = 0;

        /// <summary>
        /// -1: đồng bộ lỗi 
        /// 0: chưa đồng bộ về core
        /// 1: đang đồng bộ về core
        /// 2: đã đồng bộ về core
        /// </summary>
        public short IsSyncedCustomerToCore { get; set; } = 0;

        ///// <summary>
        ///// -1: đồng bộ lỗi 
        ///// 0: chưa đồng bộ về core
        ///// 1: đã đồng bộ về core
        ///// </summary>
        public short IsSyncedToShareDb { get; set; } = 0;

        /// <summary>
        /// -2: hd không có BuyerEmail
        /// -1: tạo nội dung nỗi
        /// 0: chưa tạo nội dung
        /// 1: đã tạo nội dung
        /// </summary>
        public short IsCreatedContentEmail { get; set; } = 0;

        /// <summary>
        /// 0: chưa sinh số
        /// 1: đã sinh số
        /// </summary>
        public short IsGeneratedNumber { get; set; } = 0;

        public short IsSyncVerificationCodeTocore { get; set; } = 0;

        public List<Invoice02DetailDto> InvoiceDetails { get; set; }

        public Invoice02ReferenceDto InvoiceReference { get; set; }

        public Invoice02ReferenceOldDto InvoiceReferenceOld { get; set; }

        public ExtraPropertyDictionary ExtraProperties { get; set; }
        public string StoreCode { get; set; }
        public string StoreName { get; set; }
        public string BudgetUnitCode { get; set; }
        public string BuyerIDNumber { get; set; }
        public string BuyerPassportNumber { get; set; }
    }
}
