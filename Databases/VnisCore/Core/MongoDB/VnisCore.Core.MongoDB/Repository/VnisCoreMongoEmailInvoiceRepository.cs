using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading;
using System.Threading.Tasks;
using Core.Domain.Repositories.MongoDB;
using Core.MongoDB;
using Core.Shared.Constants;
using MongoDB.Bson;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using VnisCore.Core.MongoDB.Entities;
using VnisCore.Core.MongoDB.IRepository;

namespace VnisCore.Core.MongoDB.Repository
{
    public class VnisCoreMongoEmailInvoiceRepository : MongoDbRepository<IVnisCoreMongoDbContext, MongoEmailInvoiceEntity, Guid>, IVnisCoreMongoEmailInvoiceRepository
    {
        public VnisCoreMongoEmailInvoiceRepository(IMongoDbContextProvider<IVnisCoreMongoDbContext> dbContextProvider) : base(dbContextProvider)
        {
        }

        public async Task<MongoEmailInvoiceEntity> GetByInvoiceId(long? id, CancellationToken cancellationToken = default)
        {
            return await (await GetMongoQueryableAsync(cancellationToken))
                    .FirstOrDefaultAsync(ct => ct.InvoiceHeaderId == id,
                        GetCancellationToken(cancellationToken));
        }


        public async Task<long> CountAsync(Guid? tenantId, string sorting, string filter, int[] mailStatuses, CancellationToken cancellationToken)
        {
            return await (await GetMongoQueryableAsync(cancellationToken))
                .WhereIf<MongoEmailInvoiceEntity, IMongoQueryable<MongoEmailInvoiceEntity>>(
                    !filter.IsNullOrWhiteSpace(),
                    u => u.To.Contains(filter)
                    || u.InvoiceNo.Contains(filter)
                    || u.SerialNo.Contains(filter)
                )
                .WhereIf(tenantId.HasValue, x => x.TenantId == tenantId)
                .WhereIf(!mailStatuses.IsNullOrEmpty(), x => mailStatuses.Contains(x.Status))
                .OrderBy(sorting.IsNullOrWhiteSpace() ? nameof(MongoEmailInvoiceEntity.CreationTime) : sorting)
                .As<IMongoQueryable<MongoEmailInvoiceEntity>>()
                .CountAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<long> OldCountAsync(Guid? tenantId, string sorting, int maxResultCount, int skipCount, string filter, DateTime? createFromDate, DateTime? createToDate, CancellationToken cancellationToken = default)
        {
            return await (await GetMongoQueryableAsync(cancellationToken))
                            .WhereIf<MongoEmailInvoiceEntity, IMongoQueryable<MongoEmailInvoiceEntity>>(
                                !filter.IsNullOrWhiteSpace(),
                                u => u.To.ToLower().Contains(filter)
                                || u.InvoiceNo.Contains(filter)
                                || u.SerialNo.ToLower().Contains(filter)
                                || u.FullNameCreator.ToLower().Contains(filter)

                            )
                            .WhereIf(createFromDate.HasValue, x => x.CreationTime >= createFromDate.Value.ToUniversalTime())
                            .WhereIf(createToDate.HasValue, x => x.CreationTime <= createToDate.Value.Date.AddDays(1).ToUniversalTime())
                            .WhereIf(tenantId.HasValue, x => x.TenantId == tenantId)
                            .OrderBy(sorting.IsNullOrWhiteSpace() ? nameof(MongoEmailInvoiceEntity.CreationTime) : sorting)
                            .As<IMongoQueryable<MongoEmailInvoiceEntity>>()
                            .CountAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<long> CountAsync(Guid? tenantId, string sorting, int maxResultCount, int skipCount, string keyword, DateTime? createFromDate, DateTime? createToDate, int[] mailStatuses, CancellationToken cancellationToken = default)
        {
            BsonDocument filter = new BsonDocument();

            if (tenantId.HasValue)
            {
                var byteTenantId = GuidConverter.ToBytes(tenantId.Value, GuidRepresentation.CSharpLegacy);
                filter.Add("TenantId", new BsonBinaryData(byteTenantId, BsonBinarySubType.UuidLegacy, GuidRepresentation.CSharpLegacy));
            }
            if (!mailStatuses.IsNullOrEmpty())
            {
                var mailStatusesBsonArray = new BsonArray();
                mailStatuses.ToList().ForEach(x => { mailStatusesBsonArray.Add(x); });
                filter.Add("Status", new BsonDocument()
                    .Add("$in", mailStatusesBsonArray));
            }
            if (!string.IsNullOrEmpty(keyword))
            {
                filter.Add("$or", new BsonArray()
                {
                    new BsonDocument().Add("InvoiceNo", new BsonRegularExpression($".*{keyword}.*")),
                    new BsonDocument().Add("SerialNo", new BsonRegularExpression($".*{keyword}.*", "i")),
                    new BsonDocument().Add("To", new BsonRegularExpression($".*{keyword}.*", "i")),
                    new BsonDocument().Add("Subject", new BsonRegularExpression($".*{keyword}.*", "i")),
                    new BsonDocument().Add("FullNameCreator", new BsonRegularExpression($".*{keyword}.*", "i")),
                });

                //filter.Add("To", new BsonRegularExpression($"^{keyword}.*", "i"));
                //filter.Add("InvoiceNo", new BsonRegularExpression($"^{keyword}.*"));
                //filter.Add("SerialNo", new BsonRegularExpression($"^{keyword}.*", "i"));
                //filter.Add("Subject", new BsonRegularExpression($"^{keyword}.*", "i"));
                //filter.Add("FullNameCreator", new BsonRegularExpression($"^{keyword}.*", "i"));
            }

            if (createFromDate.HasValue && createToDate.HasValue)
            {
                filter.Add("$and", new BsonArray()
                {
                    new BsonDocument
                    {
                        { "CreationTime", new BsonDocument().Add("$gte", new BsonDateTime(createFromDate.Value.ToUniversalTime())) }
                    },
                    new BsonDocument
                    {
                        { "CreationTime", new BsonDocument().Add("$lte", new BsonDateTime(createToDate.Value.Date.AddDays(1).ToUniversalTime())) }
                    }
                });
            }
            else if (createFromDate.HasValue)
            {
                filter.Add("CreationTime", new BsonDocument().Add("$gte", new BsonDateTime(createFromDate.Value.ToUniversalTime())));
            }
            else if (createToDate.HasValue)
            {
                filter.Add("CreationTime", new BsonDocument().Add("$lte", new BsonDateTime(createToDate.Value.Date.AddDays(1).ToUniversalTime())));

            }

            var collecttion = await GetCollectionAsync();
            return await collecttion.CountDocumentsAsync(filter, null, cancellationToken);
        }

        public async Task<List<MongoEmailInvoiceEntity>> OldGetListAsync(Guid? tenantId, string sorting, int maxResultCount, int skipCount, string filter, DateTime? createFromDate, DateTime? createToDate,
            CancellationToken cancellationToken)
        {
            return await (await GetMongoQueryableAsync(cancellationToken))
                .WhereIf<MongoEmailInvoiceEntity, IMongoQueryable<MongoEmailInvoiceEntity>>(
                    !filter.IsNullOrWhiteSpace(),
                    u => u.To.ToLower().Contains(filter)
                    || u.InvoiceNo.Contains(filter)
                    || u.SerialNo.ToLower().Contains(filter)
                    || u.Subject.ToLower().Contains(filter)
                    || u.FullNameCreator.ToLower().Contains(filter)
                )
                .WhereIf(createFromDate.HasValue, x => x.CreationTime >= createFromDate.Value.ToUniversalTime())
                .WhereIf(createToDate.HasValue, x => x.CreationTime <= createToDate.Value.Date.AddDays(1).ToUniversalTime())
                .WhereIf(tenantId.HasValue, x => x.TenantId == tenantId)
                .OrderByDescending(x => x.CreationTime)
                .As<IMongoQueryable<MongoEmailInvoiceEntity>>()
                .PageBy<MongoEmailInvoiceEntity, IMongoQueryable<MongoEmailInvoiceEntity>>(skipCount, maxResultCount)
                .ToListAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<List<MongoEmailInvoiceEntity>> GetListAsync(Guid? tenantId, string sorting, int maxResultCount, int skipCount, string keyword, DateTime? createFromDate, DateTime? createToDate, int[] mailStatuses,
                    CancellationToken cancellationToken)
        {
            BsonDocument filter = new BsonDocument();

            if (tenantId.HasValue)
            {
                var byteTenantId = GuidConverter.ToBytes(tenantId.Value, GuidRepresentation.CSharpLegacy);
                filter.Add("TenantId", new BsonBinaryData(byteTenantId, BsonBinarySubType.UuidLegacy, GuidRepresentation.CSharpLegacy));
            }
            if (!mailStatuses.IsNullOrEmpty())
            {
                var mailStatusesBsonArray = new BsonArray();
                mailStatuses.ToList().ForEach(x => { mailStatusesBsonArray.Add(x); });
                filter.Add("Status", new BsonDocument()
                    .Add("$in", mailStatusesBsonArray));
            }
            if (!string.IsNullOrEmpty(keyword))
            {
                filter.Add("$or", new BsonArray()
                {
                    new BsonDocument().Add("InvoiceNo", new BsonRegularExpression($".*{keyword}.*")),
                    new BsonDocument().Add("SerialNo", new BsonRegularExpression($".*{keyword}.*", "i")),
                    new BsonDocument().Add("To", new BsonRegularExpression($".*{keyword}.*", "i")),
                    new BsonDocument().Add("Subject", new BsonRegularExpression($".*{keyword}.*", "i")),
                    new BsonDocument().Add("FullNameCreator", new BsonRegularExpression($".*{keyword}.*", "i")),
                });

                //filter.Add("To", new BsonRegularExpression($"^{keyword}.*", "i"));
                //filter.Add("InvoiceNo", new BsonRegularExpression($"^{keyword}.*"));
                //filter.Add("SerialNo", new BsonRegularExpression($"^{keyword}.*", "i"));
                //filter.Add("Subject", new BsonRegularExpression($"^{keyword}.*", "i"));
                //filter.Add("FullNameCreator", new BsonRegularExpression($"^{keyword}.*", "i"));
            }

            if (createFromDate.HasValue && createToDate.HasValue)
            {
                filter.Add("$and", new BsonArray()
                {
                    new BsonDocument
                    {
                        { "CreationTime", new BsonDocument().Add("$gte", new BsonDateTime(createFromDate.Value.ToUniversalTime())) }
                    },
                    new BsonDocument
                    {
                        { "CreationTime", new BsonDocument().Add("$lte", new BsonDateTime(createToDate.Value.Date.AddDays(1).ToUniversalTime())) }
                    }
                });
            }
            else if (createFromDate.HasValue)
            {
                filter.Add("CreationTime", new BsonDocument().Add("$gte", new BsonDateTime(createFromDate.Value.ToUniversalTime())));
            }
            else if (createToDate.HasValue)
            {
                filter.Add("CreationTime", new BsonDocument().Add("$lte", new BsonDateTime(createToDate.Value.Date.AddDays(1).ToUniversalTime())));

            }

            BsonDocument projection = new BsonDocument();

            projection.Add("Subject", "$Subject");
            projection.Add("From", "From");
            projection.Add("To", "$To");
            projection.Add("Status", "$Status");
            projection.Add("EmailAction", "$EmailAction");
            projection.Add("InvoiceNo", "$InvoiceNo");
            projection.Add("SerialNo", "$SerialNo");
            projection.Add("FullNameCreator", "$FullNameCreator");
            projection.Add("TenantId", "$TenantId");
            projection.Add("CreationTime", "$CreationTime");
            projection.Add("TemplateNo", "$TemplateNo");
            projection.Add("_id", 1);

            BsonDocument sort = new BsonDocument();
            sort.Add("CreationTime", -1);

            var options = new FindOptions<MongoEmailInvoiceEntity>()
            {
                Projection = projection,
                Sort = sort,
                Skip = skipCount,
                Limit = maxResultCount
            };

            var batch = new List<MongoEmailInvoiceEntity>();

            using (var cursor = await (await GetCollectionAsync()).FindAsync(filter, options))
            {
                while (await cursor.MoveNextAsync())
                {
                    batch.AddRange(cursor.Current.ToList());
                }
            }

            return batch;
        }

        public async Task<List<MongoEmailInvoiceEntity>> GetListExportAsync(Guid? tenantId, string sorting, int maxResultCount, int skipCount, string keyword, DateTime? createFromDate, DateTime? createToDate, int[] mailStatuses,
                    CancellationToken cancellationToken)
        {
            BsonDocument filter = new BsonDocument();

            if (tenantId.HasValue)
            {
                var byteTenantId = GuidConverter.ToBytes(tenantId.Value, GuidRepresentation.CSharpLegacy);
                filter.Add("TenantId", new BsonBinaryData(byteTenantId, BsonBinarySubType.UuidLegacy, GuidRepresentation.CSharpLegacy));
            }
            if (!mailStatuses.IsNullOrEmpty())
            {
                var mailStatusesBsonArray = new BsonArray();
                mailStatuses.ToList().ForEach(x => { mailStatusesBsonArray.Add(x); });
                filter.Add("Status", new BsonDocument()
                    .Add("$in", mailStatusesBsonArray));
            }
            if (!string.IsNullOrEmpty(keyword))
            {
                filter.Add("$or", new BsonArray()
                {
                    new BsonDocument().Add("InvoiceNo", new BsonRegularExpression($".*{keyword}.*")),
                    new BsonDocument().Add("SerialNo", new BsonRegularExpression($".*{keyword}.*", "i")),
                    new BsonDocument().Add("To", new BsonRegularExpression($".*{keyword}.*", "i")),
                    new BsonDocument().Add("Subject", new BsonRegularExpression($".*{keyword}.*", "i")),
                    new BsonDocument().Add("FullNameCreator", new BsonRegularExpression($".*{keyword}.*", "i")),
                });
            }

            if (createFromDate.HasValue && createToDate.HasValue)
            {
                filter.Add("$and", new BsonArray()
                {
                    new BsonDocument
                    {
                        { "CreationTime", new BsonDocument().Add("$gte", new BsonDateTime(createFromDate.Value.ToUniversalTime())) }
                    },
                    new BsonDocument
                    {
                        { "CreationTime", new BsonDocument().Add("$lte", new BsonDateTime(createToDate.Value.Date.AddDays(1).ToUniversalTime())) }
                    }
                });
            }
            else if (createFromDate.HasValue)
            {
                filter.Add("CreationTime", new BsonDocument().Add("$gte", new BsonDateTime(createFromDate.Value.ToUniversalTime())));
            }
            else if (createToDate.HasValue)
            {
                filter.Add("CreationTime", new BsonDocument().Add("$lte", new BsonDateTime(createToDate.Value.Date.AddDays(1).ToUniversalTime())));

            }

            BsonDocument projection = new BsonDocument();

            projection.Add("TemplateNo", "$TemplateNo");
            projection.Add("SerialNo", "$SerialNo");
            projection.Add("InvoiceNo", "$InvoiceNo");
            projection.Add("CreationTime", "$CreationTime");
            projection.Add("FullNameCreator", "$FullNameCreator");
            projection.Add("TenantId", "$TenantId");
            projection.Add("Status", "$Status");
            projection.Add("To", "$To");
            projection.Add("InvoiceHeaderId", "$InvoiceHeaderId");
            projection.Add("_id", 1);

            BsonDocument sort = new BsonDocument();
            sort.Add("CreationTime", 1);

            var options = new FindOptions<MongoEmailInvoiceEntity>()
            {
                Projection = projection,
                Sort = sort,
                Skip = skipCount,
                Limit = maxResultCount
            };

            var batch = new List<MongoEmailInvoiceEntity>();

            using (var cursor = await (await GetCollectionAsync()).FindAsync(filter, options))
            {
                while (await cursor.MoveNextAsync())
                {
                    batch.AddRange(cursor.Current.ToList());
                }
            }

            return batch;
        }

        public async Task<MongoEmailInvoiceEntity> GetById(Guid id, Guid tenantId, CancellationToken cancellationToken = default)
        {
            return await (await GetMongoQueryableAsync(cancellationToken))
                    .FirstOrDefaultAsync(ct => ct.Id == id && ct.TenantId == tenantId,
                        GetCancellationToken(cancellationToken));
        }

        public async Task<List<MongoEmailInvoiceEntity>> GetListSendMailInvoiceWithCursorAsync(VnisType invoiceType, int numberTake, List<decimal> tenantGroups, List<int> invoiceGroups, CancellationToken cancellationToken = default)
        {
            BsonDocument filter = new BsonDocument();
            filter.Add("Status", new BsonInt64(0L));
            filter.Add("SendMailSource", new BsonInt64((int)SendMailSource.Auto));
            filter.Add("TemplateNo", new BsonInt64((int)invoiceType));
            filter.Add("SerialNo", new BsonDocument().Add("$ne", BsonNull.Value));
            filter.Add("InvoiceNo", new BsonDocument().Add("$ne", BsonNull.Value));

            var grpTenantBsonArray = new BsonArray();
            tenantGroups.ForEach(x => { grpTenantBsonArray.Add($"{x}"); });
            filter.Add("TenantGroup", new BsonDocument()
                .Add("$in", grpTenantBsonArray));

            var grpInvBsonArray = new BsonArray();
            invoiceGroups.ForEach(x => { grpInvBsonArray.Add(x); });
            filter.Add("InvoiceGroup", new BsonDocument()
                .Add("$in", grpInvBsonArray));

            BsonDocument sort = new BsonDocument();
            sort.Add("ConcurrencyStamp", 1);

            var options = new FindOptions<MongoEmailInvoiceEntity>()
            {
                Sort = sort,
                Limit = numberTake
            };

            var batch = new List<MongoEmailInvoiceEntity>();
            using (var cursor = await (await GetCollectionAsync()).FindAsync(filter, options, cancellationToken))
            {
                while (await cursor.MoveNextAsync())
                {
                    batch.AddRange(cursor.Current.ToList());
                }
            }
            return batch;
        }

        public async Task<bool> AnySendMailInvoiceAsync(VnisType invoiceType, List<decimal> tenantGroups, List<int> invoiceGroups, CancellationToken cancellationToken = default)
        {
            var filter = new BsonDocument();
            filter.Add("Status", new BsonInt64(0L))
                .Add("SendMailSource", new BsonInt64((int)SendMailSource.Auto))
                .Add("TemplateNo", new BsonInt64((int)invoiceType))
                .Add("SerialNo", new BsonDocument().Add("$ne", BsonNull.Value))
                .Add("InvoiceNo", new BsonDocument().Add("$ne", BsonNull.Value));

            var tenantsGroupArr = new BsonArray();
            tenantGroups.ForEach(x => { tenantsGroupArr.Add($"{x}"); });
            filter.Add("TenantGroup", new BsonDocument().Add("$in", tenantsGroupArr));

            var invoicesGroupArr = new BsonArray();
            invoiceGroups.ForEach(x => { invoicesGroupArr.Add(x); });
            filter.Add("InvoiceGroup", new BsonDocument().Add("$in", invoicesGroupArr));

            var batch = new List<MongoEmailInvoiceEntity>();

            return (await (await GetCollectionAsync()).FindAsync(filter, null, cancellationToken)).Any();

            //return await (await GetMongoQueryableAsync(cancellationToken))
            //    .Where(x => x.Status == 0 && x.SendMailSource == (int)SendMailSource.Auto && tenantGroups.Contains(x.TenantGroup) && invoiceGroups.Contains(x.InvoiceGroup))
            //    .As<IMongoQueryable<MongoEmailInvoiceEntity>>()
            //    .AnyAsync(GetCancellationToken(cancellationToken));
        }

        public async Task UpdateManyStatusSendMailInvoiceAsync(List<MongoEmailInvoiceEntity> documents, short status, CancellationToken cancellationToken = default)
        {
            var ids = documents.Select(x => x.Id);
            var filter = Builders<MongoEmailInvoiceEntity>.Filter.In(item => item.Id, ids);
            var update = Builders<MongoEmailInvoiceEntity>.Update.Set("Status", status);

            await (await GetCollectionAsync()).UpdateManyAsync(filter, update, null, cancellationToken);
        }

        public async Task UpdateOneStatusSendMailInvoiceAsync(MongoEmailInvoiceEntity document, short status, CancellationToken cancellationToken = default)
        {
            var filter = Builders<MongoEmailInvoiceEntity>.Filter.Eq(x => x.Id, document.Id);
            var update = Builders<MongoEmailInvoiceEntity>.Update.Set("Status", status);

            await (await GetCollectionAsync()).UpdateOneAsync(filter, update, null, cancellationToken);
        }

        public async Task<long> CountMailsSendedSuccessAsync(Guid tenantId, CancellationToken cancellationToken = default)
        {
            return await (await GetMongoQueryableAsync(cancellationToken))
                .Where(x => x.TenantId == tenantId 
                        && ((x.LastModificationTime >= DateTime.Now.Date && x.LastModificationTime < DateTime.Now) || (x.CreationTime >= DateTime.Now.Date && x.CreationTime < DateTime.Now))
                )
                .As<IMongoQueryable<MongoEmailInvoiceEntity>>()
                .Select(x=>x.TimeResend)
                .SumAsync();
        }
    }
}
