using Core.Domain.Repositories.MongoDB;
using Core.MongoDB;
using Core.Shared.Constants;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading;
using System.Threading.Tasks;
using VnisCore.Core.MongoDB.Entities;
using VnisCore.Core.MongoDB.IRepository;

namespace VnisCore.Core.MongoDB.Repository
{
    public class VnisCoreMongoEmailRepository : MongoDbRepository<IVnisCoreMongoDbContext, MongoEmailEntity, Guid>, IVnisCoreMongoEmailRepository
    {
        public VnisCoreMongoEmailRepository(IMongoDbContextProvider<IVnisCoreMongoDbContext> dbContextProvider) : base(dbContextProvider)
        {
        }

        public async Task<long> CountAsync(Guid? tenanId, string sorting, string filter, short[] mailStatuses, DateTime? fromDate,
            DateTime? toDate, CancellationToken cancellationToken)
        {
            return await (await GetMongoQueryableAsync(cancellationToken))
                .WhereIf<MongoEmailEntity, IMongoQueryable<MongoEmailEntity>>(
                    !filter.IsNullOrWhiteSpace(),
                    u => u.To.ToLower().Contains(filter)
                    || u.EmailAction.ToLower().Contains(filter)
                    || u.Subject.ToLower().Contains(filter)

                )
                .Where(x => x.TenantId == tenanId.Value)
                .WhereIf(fromDate.HasValue, x => x.CreationTime >= fromDate.Value)
                .WhereIf(toDate.HasValue, x => x.CreationTime < toDate.Value.AddDays(1))
                .WhereIf(!mailStatuses.IsNullOrEmpty(), x =>mailStatuses.Contains(x.Status))
                .OrderBy(sorting.IsNullOrWhiteSpace() ? nameof(MongoEmailEntity.CreationTime) : sorting)
                .As<IMongoQueryable<MongoEmailEntity>>()
                .CountAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<List<MongoEmailEntity>> GetListAsync(Guid? tenantId, 
            string sorting, 
            int maxResultCount, 
            int skipCount, 
            string filter,
            short[] mailStatuses, 
            DateTime? fromDate,
            DateTime? toDate,
            CancellationToken cancellationToken)
        {
            return await (await GetMongoQueryableAsync(cancellationToken))
                .WhereIf<MongoEmailEntity, IMongoQueryable<MongoEmailEntity>>(
                    !filter.IsNullOrWhiteSpace(),
                    u => u.To.ToLower().Contains(filter)
                    || u.EmailAction.ToLower().Contains(filter)
                    || u.Subject.ToLower().Contains(filter)
                )
                .Where(x => x.TenantId == tenantId.Value)
                .WhereIf(fromDate.HasValue, x => x.CreationTime >= fromDate.Value)
                .WhereIf(toDate.HasValue, x => x.CreationTime < toDate.Value.AddDays(1))
                .WhereIf(!mailStatuses.IsNullOrEmpty(), x => mailStatuses.Contains(x.Status))
                .OrderByDescending(x => x.CreationTime)
                .As<IMongoQueryable<MongoEmailEntity>>()
                .PageBy<MongoEmailEntity, IMongoQueryable<MongoEmailEntity>>(skipCount, maxResultCount)
                .ToListAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<MongoEmailEntity> GetById(Guid id, Guid tenantId, CancellationToken cancellationToken = default)
        {
            return await(await GetMongoQueryableAsync(cancellationToken))
                    .FirstOrDefaultAsync(ct => ct.Id == id && ct.TenantId == tenantId,
                        GetCancellationToken(cancellationToken));
        }

        /// <summary>
        /// Count Số lượng mail hệ thống đã gửi thành công
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<long> CountMailsSendedSuccessAsync(Guid tenantId, CancellationToken cancellationToken = default)
        {
            return await (await GetMongoQueryableAsync(cancellationToken))
                .Where(x => x.TenantId == tenantId
                        && ((x.LastModificationTime >= DateTime.Now.Date && x.LastModificationTime < DateTime.Now) || (x.CreationTime >= DateTime.Now.Date && x.CreationTime < DateTime.Now))
                )
                .As<IMongoQueryable<MongoEmailEntity>>()
                .Select(x => x.TimeResend)
                .SumAsync();
        }
    }
}
