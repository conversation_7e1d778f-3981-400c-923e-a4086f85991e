using Core;
using Core.EntityFrameworkCore.Modeling;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;
using VnisCore.Core.Oracle.Domain.BaseEntities;

namespace VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02
{
    [Table("Invoice02SpecificProductExtra")]
    public class Invoice02SpecificProductExtraEntity : BaseInvoiceSpecificProductExtra
    {

    }

    public static class DbContextModelInvoice01SpecificProductExtraCreatingExtensions
    {
        public static void ConfigureInvoice02SpecificProductExtraEntity(this ModelBuilder builder)
        {
            Check.NotNull(builder, nameof(builder));

            builder.Entity<Invoice02SpecificProductExtraEntity>(b =>
            {
                b.ConfigureByConvention(); //auto configure for the base class props

                b.ToTable("Invoice02SpecificProductExtra").HasComment("<PERSON>ảng lưu trữ các giá trị của thông tin hàng hóa đặc trưng của từng hàng hóa");
                b.<PERSON>(e => e.Id);
                b.Property(x => x.Id).HasDefaultValueSql(@"""SEQ_Invoice02SpecificProductExtra_Id"".NEXTVAL");

                b.Property(x => x.InvoiceHeaderId).IsRequired().HasComment("Id hóa đơn");
                b.Property(x => x.InvoiceDetailId).IsRequired().HasComment("Id chi tiết hóa đơn");
                b.Property(x => x.SpecificProductFieldId).IsRequired().HasComment("Id hàng hóa đặc trưng");
                b.Property(x => x.Type).IsRequired().HasComment("Loại hàng hóa đặc trưng");
                b.Property(x => x.FieldName).IsRequired().HasMaxLength(20).HasComment("Tên trường hàng hóa đặc trưng");
                b.Property(x => x.FieldValue).IsRequired().HasMaxLength(200).HasComment("Giá trị mô tả cho tên trường hàng hóa đặc trưng");

                b.HasIndex(x => new { x.InvoiceHeaderId, x.TenantId, x.IsDeleted });
                b.HasIndex(x => new { x.InvoiceDetailId, x.SpecificProductFieldId, x.TenantId, x.IsDeleted });
            });
        }
    }
}