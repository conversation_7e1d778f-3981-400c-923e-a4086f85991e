using Microsoft.EntityFrameworkCore.Migrations;

namespace VnisCore.Core.Oracle.EntityFrameworkCore.DbMigrations.Migrations
{
    public partial class UpdateTaxReportHeader01Table : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<short>(
                name: "TypeMerchandise",
                table: "TaxReport01Header",
                type: "NUMBER(5)",
                nullable: false,
                defaultValue: (short)0);

            migrationBuilder.AddColumn<short>(
                name: "TypeReport",
                table: "TaxReport01Header",
                type: "NUMBER(5)",
                nullable: false,
                defaultValue: (short)0);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "TypeMerchandise",
                table: "TaxReport01Header");

            migrationBuilder.DropColumn(
                name: "TypeReport",
                table: "TaxReport01Header");
        }
    }
}
