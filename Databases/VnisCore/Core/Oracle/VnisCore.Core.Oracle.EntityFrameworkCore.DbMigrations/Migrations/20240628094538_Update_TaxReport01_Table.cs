using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace VnisCore.Core.Oracle.EntityFrameworkCore.DbMigrations.Migrations
{
    public partial class Update_TaxReport01_Table : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<long>(
                name: "TvanMonitorId",
                table: "TaxReport01TvanInfo",
                type: "NUMBER(19)",
                nullable: true,
                oldClrType: typeof(long),
                oldType: "NUMBER(19)");

            migrationBuilder.AddColumn<short>(
                name: "InvoiceType",
                table: "TaxReport01TvanInfo",
                type: "NUMBER(5)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "LTbao",
                table: "TaxReport01TvanInfo",
                type: "NUMBER(10)",
                nullable: true);

            migrationBuilder.AddColumn<long>(
                name: "TaxReport01DetailMappingEntityId",
                table: "TaxReport01TvanInfo",
                type: "NUMBER(19)",
                nullable: false,
                defaultValue: 0L);

            migrationBuilder.AddColumn<long>(
                name: "TaxReport01HeaderEntityId",
                table: "TaxReport01TvanInfo",
                type: "NUMBER(19)",
                nullable: false,
                defaultValue: 0L);

            migrationBuilder.AddColumn<string>(
                name: "MessageCode",
                table: "TaxReport01DetailMapping",
                type: "NVARCHAR2(2000)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MessageCodeReference",
                table: "TaxReport01DetailMapping",
                type: "NVARCHAR2(2000)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "TvanReceivedTime",
                table: "TaxReport01DetailMapping",
                type: "TIMESTAMP(7)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "TvanSentTime",
                table: "TaxReport01DetailMapping",
                type: "TIMESTAMP(7)",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_TaxReport01TvanInfo_TaxReport01DetailMappingEntityId",
                table: "TaxReport01TvanInfo",
                column: "TaxReport01DetailMappingEntityId");

            migrationBuilder.CreateIndex(
                name: "IX_TaxReport01TvanInfo_TaxReport01HeaderEntityId",
                table: "TaxReport01TvanInfo",
                column: "TaxReport01HeaderEntityId");

            migrationBuilder.AddForeignKey(
                name: "FK_TaxReport01TvanInfo_TaxReport01DetailMapping_TaxReport01DetailMappingEntityId",
                table: "TaxReport01TvanInfo",
                column: "TaxReport01DetailMappingEntityId",
                principalTable: "TaxReport01DetailMapping",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_TaxReport01TvanInfo_TaxReport01Header_TaxReport01HeaderEntityId",
                table: "TaxReport01TvanInfo",
                column: "TaxReport01HeaderEntityId",
                principalTable: "TaxReport01Header",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_TaxReport01TvanInfo_TaxReport01DetailMapping_TaxReport01DetailMappingEntityId",
                table: "TaxReport01TvanInfo");

            migrationBuilder.DropForeignKey(
                name: "FK_TaxReport01TvanInfo_TaxReport01Header_TaxReport01HeaderEntityId",
                table: "TaxReport01TvanInfo");

            migrationBuilder.DropIndex(
                name: "IX_TaxReport01TvanInfo_TaxReport01DetailMappingEntityId",
                table: "TaxReport01TvanInfo");

            migrationBuilder.DropIndex(
                name: "IX_TaxReport01TvanInfo_TaxReport01HeaderEntityId",
                table: "TaxReport01TvanInfo");

            migrationBuilder.DropColumn(
                name: "InvoiceType",
                table: "TaxReport01TvanInfo");

            migrationBuilder.DropColumn(
                name: "LTbao",
                table: "TaxReport01TvanInfo");

            migrationBuilder.DropColumn(
                name: "TaxReport01DetailMappingEntityId",
                table: "TaxReport01TvanInfo");

            migrationBuilder.DropColumn(
                name: "TaxReport01HeaderEntityId",
                table: "TaxReport01TvanInfo");

            migrationBuilder.DropColumn(
                name: "MessageCode",
                table: "TaxReport01DetailMapping");

            migrationBuilder.DropColumn(
                name: "MessageCodeReference",
                table: "TaxReport01DetailMapping");

            migrationBuilder.DropColumn(
                name: "TvanReceivedTime",
                table: "TaxReport01DetailMapping");

            migrationBuilder.DropColumn(
                name: "TvanSentTime",
                table: "TaxReport01DetailMapping");

            migrationBuilder.AlterColumn<long>(
                name: "TvanMonitorId",
                table: "TaxReport01TvanInfo",
                type: "NUMBER(19)",
                nullable: false,
                defaultValue: 0L,
                oldClrType: typeof(long),
                oldType: "NUMBER(19)",
                oldNullable: true);
        }
    }
}
