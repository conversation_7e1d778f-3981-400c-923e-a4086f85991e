using Microsoft.EntityFrameworkCore.Migrations;

namespace VnisCore.Core.Oracle.EntityFrameworkCore.DbMigrations.Migrations
{
    public partial class AddFKTaxReportHeaderIdTaxReportDetailMappingIdIntoReportXmlTvanReportXmlTb : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<long>(
                name: "TaxReport01DetailMappingId",
                table: "TvanReportXml",
                type: "NUMBER(19)",
                nullable: false,
                defaultValue: 0L);

            migrationBuilder.AddColumn<long>(
                name: "TaxReport01HeaderId",
                table: "TvanReportXml",
                type: "NUMBER(19)",
                nullable: false,
                defaultValue: 0L);

            migrationBuilder.AddColumn<long>(
                name: "TaxReport01DetailMappingId",
                table: "ReportXml",
                type: "NUMBER(19)",
                nullable: false,
                defaultValue: 0L);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "TaxReport01DetailMappingId",
                table: "TvanReportXml");

            migrationBuilder.DropColumn(
                name: "TaxReport01HeaderId",
                table: "TvanReportXml");

            migrationBuilder.DropColumn(
                name: "TaxReport01DetailMappingId",
                table: "ReportXml");
        }
    }
}
