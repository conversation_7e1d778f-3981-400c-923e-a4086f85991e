using Microsoft.EntityFrameworkCore.Migrations;

namespace VnisCore.Core.Oracle.EntityFrameworkCore.DbMigrations.Migrations
{
    public partial class Change_Null_Field_Buyer_Invoice02Header_table : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "BuyerFullName",
                table: "Invoice02Header",
                type: "NVARCHAR2(400)",
                maxLength: 400,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "NVARCHAR2(400)",
                oldMaxLength: 400);

            migrationBuilder.AlterColumn<string>(
                name: "BuyerAddressLine",
                table: "Invoice02Header",
                type: "NVARCHAR2(400)",
                maxLength: 400,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "NVARCHAR2(400)",
                oldMaxLength: 400);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "BuyerFullName",
                table: "Invoice02Header",
                type: "NVARCHAR2(400)",
                maxLength: 400,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "NVARCHAR2(400)",
                oldMaxLength: 400,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "BuyerAddressLine",
                table: "Invoice02Header",
                type: "NVARCHAR2(400)",
                maxLength: 400,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "NVARCHAR2(400)",
                oldMaxLength: 400,
                oldNullable: true);
        }
    }
}
