using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace VnisCore.Core.Oracle.EntityFrameworkCore.DbMigrations.Migrations
{
    public partial class AddInvoice02SpecificProductExtra : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Invoice02SpecificProductExtra",
                columns: table => new
                {
                    Id = table.Column<long>(type: "NUMBER(19)", nullable: false, defaultValueSql: "\"SEQ_Invoice02SpecificProductExtra_Id\".NEXTVAL"),
                    CreationTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: false),
                    CreatorId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    IsDeleted = table.Column<bool>(type: "NUMBER(1)", nullable: false, defaultValue: false),
                    DeleterId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: true),
                    TenantId = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    IsActive = table.Column<bool>(type: "NUMBER(1)", nullable: false),
                    InvoiceHeaderId = table.Column<long>(type: "NUMBER(19)", nullable: false, comment: "Id hóa đơn"),
                    InvoiceDetailId = table.Column<long>(type: "NUMBER(19)", nullable: false, comment: "Id chi tiết hóa đơn"),
                    SpecificProductFieldId = table.Column<long>(type: "NUMBER(19)", nullable: false, comment: "Id hàng hóa đặc trưng"),
                    Type = table.Column<int>(type: "NUMBER(10)", nullable: false, comment: "Loại hàng hóa đặc trưng"),
                    FieldName = table.Column<string>(type: "NVARCHAR2(20)", maxLength: 20, nullable: false, comment: "Tên trường hàng hóa đặc trưng"),
                    FieldValue = table.Column<string>(type: "NVARCHAR2(200)", maxLength: 200, nullable: false, comment: "Giá trị mô tả cho tên trường hàng hóa đặc trưng")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Invoice02SpecificProductExtra", x => x.Id);
                },
                comment: "Bảng lưu trữ các giá trị của thông tin hàng hóa đặc trưng của từng hàng hóa");

            migrationBuilder.CreateIndex(
                name: "IX_Invoice02SpecificProductExtra_InvoiceDetailId_SpecificProductFieldId_TenantId_IsDeleted",
                table: "Invoice02SpecificProductExtra",
                columns: new[] { "InvoiceDetailId", "SpecificProductFieldId", "TenantId", "IsDeleted" });

            migrationBuilder.CreateIndex(
                name: "IX_Invoice02SpecificProductExtra_InvoiceHeaderId_TenantId_IsDeleted",
                table: "Invoice02SpecificProductExtra",
                columns: new[] { "InvoiceHeaderId", "TenantId", "IsDeleted" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Invoice02SpecificProductExtra");
        }
    }
}
