using Core.DependencyInjection;
using Microsoft.Extensions.Configuration;
using StackExchange.Redis;

namespace Core.Account.Web.Services
{
    public interface IRedisConfiguration : ISingletonDependency
    {
        IDatabase GetInstance();
    }

    public class RedisConfiguration : IRedisConfiguration
    {
        private readonly IConfiguration _configuration;
        private IDatabase _redisDatabase;
        public RedisConfiguration(IConfiguration configuration)
        {
            _configuration = configuration;
        }
        public IDatabase GetInstance()
        {
            var hostName = _configuration["Redis:HostName"];
            var database = _configuration["Redis:Database"];
            var configuration = _configuration["Redis:Configuration"];
            if (_redisDatabase == null)
            {
                int.TryParse(database, out int defaulDatabase);

                var redisConnection = ConnectionMultiplexer.Connect(configuration);
                _redisDatabase = redisConnection.GetDatabase(defaulDatabase);
            }

            return _redisDatabase;
        }
    }
}
