using System;

namespace Core.Account.Web.Services
{
    public interface IRedisService
    {
        long Increment(string key, TimeSpan? cacheExpiration, long value = 1);
    }

    public class RedisService : IRedisService
    {
        private readonly IRedisConfiguration _redisConfiguration;
        public RedisService(
            IRedisConfiguration redisConfiguration)
        {
            _redisConfiguration = redisConfiguration;
        }

        public long Increment(string key, TimeSpan? cacheExpiration, long value = 1)
        {
            var db = _redisConfiguration.GetInstance();
            var cacheValue = db.StringIncrement(key, value);

            if (cacheExpiration.HasValue)
            {
                db.KeyExpire(key, cacheExpiration);
            }

            return cacheValue;
        }
    }


}
