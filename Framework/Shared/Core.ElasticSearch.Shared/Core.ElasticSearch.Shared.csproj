<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\Databases\VnisCore\Core\MongoDB\VnisCore.Core.MongoDB\VnisCore.Core.MongoDB.csproj" />
    <ProjectReference Include="..\..\..\Databases\VnisCore\Core\Oracle\VnisCore.Core.Oracle.Domain\VnisCore.Core.Oracle.Domain.csproj" />
    <ProjectReference Include="..\..\Core\Core.AutoMapper\Core.AutoMapper.csproj" />
    <ProjectReference Include="..\..\Core\Core.Ddd.Application\Core.Ddd.Application.csproj" />
    <ProjectReference Include="..\Core.Shared\Core.Shared.csproj" />
  </ItemGroup>

</Project>
