using Core.Application;
using Core.AutoMapper;
using Core.ElasticSearch.Shared.Invoices.Invoice01.IRepository;
using Core.ElasticSearch.Shared.Invoices.Invoice01.Repository;
using Core.ElasticSearch.Shared.Invoices.Invoice02.IRepository;
using Core.ElasticSearch.Shared.Invoices.Invoice02.Repository;
using Core.Modularity;
using Core.Shared;
using Microsoft.Extensions.DependencyInjection;
using VnisCore.Core.MongoDB;
using VnisCore.Core.Oracle.Domain;

namespace Core.ElasticSearch.Shared
{
    [DependsOn(
        typeof(AbpDddApplicationModule),
        typeof(AbpAutoMapperModule),
        typeof(SharedModule),
        typeof(VnisCoreOracleDomainModule),
        typeof(VnisCoreMongoDbModule)
    )]
    public class ElasticSearchSharedModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            context.Services.AddScoped<Core.Shared.ElasticSearch.ElasticSearch, Core.Shared.ElasticSearch.ElasticSearch>();

            #region Add dependency repo
            context.Services.AddScoped<IESInvoice01Repository, ESInvoice01Repository>();
            context.Services.AddScoped<IESInvoice02Repository, ESInvoice02Repository>();
            #endregion 
        }
    }
}