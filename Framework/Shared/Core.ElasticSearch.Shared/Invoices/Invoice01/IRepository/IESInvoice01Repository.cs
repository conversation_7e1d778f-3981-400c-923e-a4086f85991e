using System.Collections.Generic;
using System.Threading.Tasks;
using VnisCore.Core.MongoDB.Entities.Invoices.Invoice01;

namespace Core.ElasticSearch.Shared.Invoices.Invoice01.IRepository
{
    public interface IESInvoice01Repository
    {
        #region Đồng bộ ES dành cho Form or API
        /// <summary>
        /// Update ES cho nghiệp vụ xoá bỏ
        /// </summary>
        /// <param name="invoice01s"></param>
        /// <param name="hasApproveDelete"></param>
        /// <param name="deleteTime"></param>
        /// <param name="groups"></param>
        /// <param name="group"></param>
        /// <returns></returns>
        //Task UpdateEsDeleteAsync(List<Invoice01HeaderEntity> invoice01s, bool hasApproveDelete, DateTime deleteTime, List<string> groups, string group);
        #endregion


        #region Đồng bộ ES dành cho Worker
        /// <summary>
        /// Update ES cho nghiệp vụ duyệt: duy<PERSON>t để ký, duy<PERSON>t xoá huỷ, duy<PERSON>t xo<PERSON> bỏ
        /// </summary>
        /// <param name="mongoInvoice01s"></param>
        /// <param name="tenantGroupIndexEs"></param>
        /// <returns></returns>
        Task UpdateEsApproveAsync(List<MongoInvoice01Entity> mongoInvoice01s, string tenantGroupIndexEs);

        /// <summary>
        /// Update ES: Xoá huỷ
        /// </summary>
        /// <param name="mongoInvoice01s"></param>
        /// <param name="tenantGroupIndexEs"></param>
        /// <returns></returns>
        Task UpdateESCancelAsync(List<MongoInvoice01Entity> mongoInvoice01s, string tenantGroupIndexEs);

        /// <summary>
        /// Sync ES: xoá bỏ
        /// </summary>
        /// <param name="mongoInvoice01s"></param>
        /// <param name="tenantGroupIndexEs"></param>
        /// <returns></returns>
        Task UpdateESDeleteAsync(List<MongoInvoice01Entity> mongoInvoice01s, string tenantGroupIndexEs);

        /// <summary>
        /// Sync ES: trang thai ky
        /// </summary>
        /// <param name="mongoInvoice01s"></param>
        /// <param name="tenantGroupIndexEs"></param>
        /// <returns></returns>
        Task UpdateESSignStatusAsync(List<MongoInvoice01Entity> mongoInvoice01s, string tenantGroupIndexEs);

        /// <summary>
        /// Sync ES: mã của Cơ quan thuế
        /// </summary>
        /// <param name="mongoInvoice01s"></param>
        /// <param name="tenantGroupIndexEs"></param>
        /// <returns></returns>
        Task UpdateESVerificationCodeAsync(List<MongoInvoice01Entity> mongoInvoice01s, string tenantGroupIndexEs);

        /// <summary>
        /// update Trạng thái đã kê khai của hóa đơn không mã
        /// </summary>
        /// <param name="mongoInvoice01s"></param>
        /// <param name="tenantGroupIndexEs"></param>
        /// <returns></returns>
        Task UpdateESIsDeclaredAsync(List<MongoInvoice01Entity> mongoInvoice01s, string tenantGroupIndexEs);
        #endregion
    }
}
