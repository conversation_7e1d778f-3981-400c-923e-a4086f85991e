using Core.ElasticSearch.Shared.Invoices.Invoice01.IRepository;
using Core.Shared.Constants;
using Core.Shared.ElasticSearch.Dto.Invoice01;
using Core.Shared.Extensions;

using Elasticsearch.Net;

using Serilog;

using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using VnisCore.Core.MongoDB.Entities.Invoices.Invoice01;
using VnisCore.Core.MongoDB.IRepository;

namespace Core.ElasticSearch.Shared.Invoices.Invoice01.Repository
{
    public class ESInvoice01Repository : IESInvoice01Repository
    {
        private readonly Core.Shared.ElasticSearch.ElasticSearch _elasticSearch;
        private readonly IVnisCoreMongoInvoice01Repository _vnisCoreMongoInvoice01Repository;

        public ESInvoice01Repository(
            Core.Shared.ElasticSearch.ElasticSearch elasticSearch,
            IVnisCoreMongoInvoice01Repository vnisCoreMongoInvoice01Repository)
        {
            _elasticSearch = elasticSearch;
            _vnisCoreMongoInvoice01Repository = vnisCoreMongoInvoice01Repository;
        }

        /// <summary>
        /// Sync ES cho nghiệp vụ duyệt: duyệt để ký, duyệt xoá huỷ, duyệt xoá bỏ
        /// </summary>
        /// <param name="mongoInvoice01s"></param>
        /// <param name="tenantGroupIndexEs"></param>
        /// <returns></returns>
        public async Task UpdateEsApproveAsync(List<MongoInvoice01Entity> mongoInvoice01s, string tenantGroupIndexEs)
        {
            var client = _elasticSearch.Client("invoice01", tenantGroupIndexEs);

            var bulkResponse = await client.BulkAsync(b => b
                        .UpdateMany(mongoInvoice01s, (descriptor, update) => descriptor
                            .Id(update.Id)
                            .Script(s => s
                                .Source($"ctx._source.{nameof(Invoice01HeaderEsDto.ApproveDeleteStatus).FirstCharToLowerCase()} = params.{nameof(Invoice01HeaderEsDto.ApproveDeleteStatus).FirstCharToLowerCase()};" +
                                        $"ctx._source.{nameof(Invoice01HeaderEsDto.ApproveCancelStatus).FirstCharToLowerCase()} = params.{nameof(Invoice01HeaderEsDto.ApproveCancelStatus).FirstCharToLowerCase()};" +
                                        $"ctx._source.{nameof(Invoice01HeaderEsDto.ApproveStatus).FirstCharToLowerCase()} = params.{nameof(Invoice01HeaderEsDto.ApproveStatus).FirstCharToLowerCase()};" +
                                        $"ctx._source.{nameof(Invoice01HeaderEsDto.InvoiceStatus).FirstCharToLowerCase()} = params.{nameof(Invoice01HeaderEsDto.InvoiceStatus).FirstCharToLowerCase()};" 
                                )
                                .Params(p => p
                                    .Add($"{nameof(Invoice01HeaderEsDto.ApproveDeleteStatus).FirstCharToLowerCase()}", update.ApproveDeleteStatus)
                                    .Add($"{nameof(Invoice01HeaderEsDto.ApproveCancelStatus).FirstCharToLowerCase()}", update.ApproveCancelStatus)
                                    .Add($"{nameof(Invoice01HeaderEsDto.ApproveStatus).FirstCharToLowerCase()}", update.ApproveStatus)
                                    .Add($"{nameof(Invoice01HeaderEsDto.InvoiceStatus).FirstCharToLowerCase()}", update.InvoiceStatus)
                                )
                            )
                        ).Refresh(Refresh.True));

            if (bulkResponse.IsValid)
            {
                await _vnisCoreMongoInvoice01Repository.UpdateIsSyncedToElasticSearchAsync(mongoInvoice01s.Select(x => x.Id).ToList(), (short)SyncElasticSearchStatus.Synced.GetHashCode());
                Log.Information(@$"RESPONSE UPDATE ES APPROVE STATUS: {bulkResponse.IsValid} - {bulkResponse.Items} - {bulkResponse.DebugInformation}");
            }
            else
            {
                await _vnisCoreMongoInvoice01Repository.UpdateIsSyncedToElasticSearchAsync(mongoInvoice01s.Select(x => x.Id).ToList(), (short)SyncElasticSearchStatus.PendingSyncUpdate.GetHashCode());
                Log.Information(@$"RESPONSE UPDATE ES APPROVE STATUS: {bulkResponse.Errors} - {bulkResponse.DebugInformation} - {bulkResponse.ItemsWithErrors} - {bulkResponse.OriginalException}");
            }
        }


        /// <summary>
        /// Sync ES: Xoá huỷ
        /// </summary>
        /// <param name="mongoInvoice01s"></param>
        /// <param name="tenantGroupIndexEs"></param>
        /// <returns></returns>
        public async Task UpdateESCancelAsync(List<MongoInvoice01Entity> mongoInvoice01s, string tenantGroupIndexEs)
        {
            var client = _elasticSearch.Client("invoice01", tenantGroupIndexEs);

            var bulkResponse = await client.BulkAsync(b => b
                        .UpdateMany(mongoInvoice01s, (descriptor, update) => descriptor
                            .Id(update.Id)
                            .Script(s => s
                                .Source($"ctx._source.{nameof(Invoice01HeaderEsDto.CancelTime).FirstCharToLowerCase()} = params.{nameof(Invoice01HeaderEsDto.CancelTime).FirstCharToLowerCase()};" +
                                        $"ctx._source.{nameof(Invoice01HeaderEsDto.ApproveCancelStatus).FirstCharToLowerCase()} = params.{nameof(Invoice01HeaderEsDto.ApproveCancelStatus).FirstCharToLowerCase()};" +
                                        $"ctx._source.{nameof(Invoice01HeaderEsDto.CancelTimeNumber).FirstCharToLowerCase()} = params.{nameof(Invoice01HeaderEsDto.CancelTimeNumber).FirstCharToLowerCase()};" +
                                        $"ctx._source.{nameof(Invoice01HeaderEsDto.InvoiceStatus).FirstCharToLowerCase()} = params.{nameof(Invoice01HeaderEsDto.InvoiceStatus).FirstCharToLowerCase()};")
                                .Params(p => p
                                    .Add($"{nameof(Invoice01HeaderEsDto.CancelTime).FirstCharToLowerCase()}", update.CancelTime.HasValue ? update.CancelTime : null)
                                    .Add($"{nameof(Invoice01HeaderEsDto.ApproveCancelStatus).FirstCharToLowerCase()}", update.ApproveCancelStatus)
                                    .Add($"{nameof(Invoice01HeaderEsDto.CancelTimeNumber).FirstCharToLowerCase()}", update.CancelTime.HasValue ? int.Parse(update.CancelTime.Value.ToString("yyyyMMdd")) : null)
                                    .Add($"{nameof(Invoice01HeaderEsDto.InvoiceStatus).FirstCharToLowerCase()}", update.InvoiceStatus)
                                )
                            )
                        ).Refresh(Refresh.True));

            if (bulkResponse.IsValid)
            {
                await _vnisCoreMongoInvoice01Repository.UpdateIsSyncedToElasticSearchAsync(mongoInvoice01s.Select(x => x.Id).ToList(), (short)SyncElasticSearchStatus.Synced.GetHashCode());
                Log.Information(@$"RESPONSE UPDATE ES CANCEL STATUS: {bulkResponse.IsValid} - {bulkResponse.Items} - {bulkResponse.DebugInformation}");
            }
            else
            {
                await _vnisCoreMongoInvoice01Repository.UpdateIsSyncedToElasticSearchAsync(mongoInvoice01s.Select(x => x.Id).ToList(), (short)SyncElasticSearchStatus.PendingSyncUpdate.GetHashCode());
                Log.Error(@$"RESPONSE UPDATE ES CANCEL STATUS: {bulkResponse.Errors} - {bulkResponse.DebugInformation} - {bulkResponse.ItemsWithErrors} - {bulkResponse.OriginalException}");
            }
        }

        /// <summary>
        /// Sync ES: Xoá bỏ
        /// </summary>
        /// <param name="mongoInvoice01s"></param>
        /// <param name="tenantGroupIndexEs"></param>
        /// <returns></returns>
        public async Task UpdateESDeleteAsync(List<MongoInvoice01Entity> mongoInvoice01s, string tenantGroupIndexEs)
        {
            var client = _elasticSearch.Client("invoice01", tenantGroupIndexEs);
            var bulkResponse = await client.BulkAsync(b => b
                       .UpdateMany(mongoInvoice01s, (descriptor, update) => descriptor
                           .Id(update.Id)
                           .Script(s => s
                               .Source($"ctx._source.{nameof(Invoice01HeaderEsDto.DeleteTime).FirstCharToLowerCase()} = params.{nameof(Invoice01HeaderEsDto.DeleteTime).FirstCharToLowerCase()};" +
                                       $"ctx._source.{nameof(Invoice01HeaderEsDto.ApproveDeleteStatus).FirstCharToLowerCase()} = params.{nameof(Invoice01HeaderEsDto.ApproveDeleteStatus).FirstCharToLowerCase()};" +
                                       $"ctx._source.{nameof(Invoice01HeaderEsDto.InvoiceStatus).FirstCharToLowerCase()} = params.{nameof(Invoice01HeaderEsDto.InvoiceStatus).FirstCharToLowerCase()};" +
                                       $"ctx._source.{nameof(Invoice01HeaderEsDto.DeleteTimeNumber).FirstCharToLowerCase()} = params.{nameof(Invoice01HeaderEsDto.DeleteTimeNumber).FirstCharToLowerCase()};"
                                       )
                               .Params(p => p
                                   .Add($"{nameof(Invoice01HeaderEsDto.DeleteTime).FirstCharToLowerCase()}", update.DeleteTime.HasValue ? update.DeleteTime : null)
                                   .Add($"{nameof(Invoice01HeaderEsDto.ApproveDeleteStatus).FirstCharToLowerCase()}", update.ApproveDeleteStatus)
                                   .Add($"{nameof(Invoice01HeaderEsDto.InvoiceStatus).FirstCharToLowerCase()}", update.InvoiceStatus)
                                   .Add($"{nameof(Invoice01HeaderEsDto.DeleteTimeNumber).FirstCharToLowerCase()}", update.DeleteTime.HasValue ? int.Parse(update.DeleteTime.Value.ToString("yyyyMMdd")) :  null)
                                   )
                           )
                       ).Refresh(Refresh.True));

            if (bulkResponse.IsValid)
            {
                await _vnisCoreMongoInvoice01Repository.UpdateIsSyncedToElasticSearchAsync(mongoInvoice01s.Select(x => x.Id).ToList(), (short)SyncElasticSearchStatus.Synced.GetHashCode());
                Log.Information(@$"RESPONSE UPDATE ES DELETE STATUS: {bulkResponse.IsValid} - {bulkResponse.Items} - {bulkResponse.DebugInformation}");
            }
            else
            {
                await _vnisCoreMongoInvoice01Repository.UpdateIsSyncedToElasticSearchAsync(mongoInvoice01s.Select(x => x.Id).ToList(), (short)SyncElasticSearchStatus.PendingSyncUpdate.GetHashCode());
                Log.Error(@$"RESPONSE UPDATE ES DELETE STATUS: {bulkResponse.Errors} - {bulkResponse.DebugInformation} - {bulkResponse.ItemsWithErrors} - {bulkResponse.OriginalException}");
            }
        }

        /// <summary>
        /// Sync ES: trang thai ky
        /// </summary>
        /// <param name="mongoInvoice01s"></param>
        /// <param name="tenantGroupIndexEs"></param>
        /// <returns></returns>
        public async Task UpdateESSignStatusAsync(List<MongoInvoice01Entity> mongoInvoice01s, string tenantGroupIndexEs)
        {
            var client = _elasticSearch.Client("invoice01", tenantGroupIndexEs);
            var bulkResponse = await client.BulkAsync(b => b
                      .UpdateMany(mongoInvoice01s, (descriptor, update) => descriptor
                          .Id(update.Id)
                          .Script(s => s
                              .Source($"ctx._source.{nameof(Invoice01HeaderEsDto.SignStatus).FirstCharToLowerCase()} = params.{nameof(Invoice01HeaderEsDto.SignStatus).FirstCharToLowerCase()}")
                                .Params(p => p
                                    .Add($"{nameof(Invoice01HeaderEsDto.SignStatus).FirstCharToLowerCase()}", update.SignStatus)
                                )
                          )
                      ).Refresh(Refresh.True));

            if (bulkResponse.IsValid)
            {
                var invalidItems = bulkResponse.ItemsWithErrors.ToList();
                if (invalidItems.Any())
                {
                    var idsInvalid = invalidItems.Select(x => long.Parse(x.Id)).ToList();
                    if (idsInvalid.Any())
                        await _vnisCoreMongoInvoice01Repository.UpdateIsSyncedToElasticSearchAsync(idsInvalid, (short)SyncElasticSearchStatus.PendingSyncUpdate.GetHashCode());

                    var idsValid = (mongoInvoice01s.Select(x => x.Id).ToList()).Except(idsInvalid).ToList();
                    if (idsValid.Any())
                        await _vnisCoreMongoInvoice01Repository.UpdateIsSyncedToElasticSearchAsync(idsValid, (short)SyncElasticSearchStatus.Synced.GetHashCode());

                    Log.Error($@"Invalid Items update SignStatus: {string.Join(",", idsInvalid)}");
                }
                else
                {
                    await _vnisCoreMongoInvoice01Repository.UpdateIsSyncedToElasticSearchAsync(mongoInvoice01s.Select(x => x.Id).ToList(), (short)SyncElasticSearchStatus.Synced.GetHashCode());
                    Log.Information(@$"RESPONSE UPDATE ES SIGN STATUS: {bulkResponse.IsValid} - {bulkResponse.Items} - {bulkResponse.DebugInformation}");
                }
            }
            else
            {
                var invalidItems = bulkResponse.ItemsWithErrors.ToList();
                var idsInvalid = invalidItems.Select(x => long.Parse(x.Id)).ToList();
                if (idsInvalid.Any())
                    await _vnisCoreMongoInvoice01Repository.UpdateIsSyncedToElasticSearchAsync(idsInvalid, (short)SyncElasticSearchStatus.PendingSyncUpdate.GetHashCode());

                var idsValid = (mongoInvoice01s.Select(x => x.Id).ToList()).Except(idsInvalid).ToList();
                if (idsValid.Any())
                    await _vnisCoreMongoInvoice01Repository.UpdateIsSyncedToElasticSearchAsync(idsValid, (short)SyncElasticSearchStatus.Synced.GetHashCode());

                Log.Error($@"Invalid Items update SignStatus: {string.Join(",", idsInvalid)}");
                Log.Error(@$"RESPONSE UPDATE ES SIGN STATUS: {bulkResponse.Errors} - {bulkResponse.DebugInformation} - {bulkResponse.ItemsWithErrors} - {bulkResponse.OriginalException}");
            }
        }

        public async Task UpdateESVerificationCodeAsync(List<MongoInvoice01Entity> mongoInvoice01s, string tenantGroupIndexEs)
        {
            var client = _elasticSearch.Client("invoice01", tenantGroupIndexEs);
            var bulkResponse = await client.BulkAsync(b => b
                      .UpdateMany(mongoInvoice01s, (descriptor, update) => descriptor
                          .Id(update.Id)
                          .Script(s => s
                              .Source($"ctx._source.{nameof(Invoice01HeaderEsDto.VerificationCode).FirstCharToLowerCase()} = params.{nameof(Invoice01HeaderEsDto.VerificationCode).FirstCharToLowerCase()}")
                                .Params(p => p
                                    .Add($"{nameof(Invoice01HeaderEsDto.VerificationCode).FirstCharToLowerCase()}", update.VerificationCode)
                                )
                          )
                      ).Refresh(Refresh.True));

            if (bulkResponse.IsValid)
            {
                await _vnisCoreMongoInvoice01Repository.UpdateIsSyncedToElasticSearchAsync(mongoInvoice01s.Select(x => x.Id).ToList(), (short)SyncElasticSearchStatus.Synced.GetHashCode());
                Log.Information(@$"RESPONSE UPDATE ES VERIFICATION CODE: {bulkResponse.IsValid} - {bulkResponse.Items} - {bulkResponse.DebugInformation}");
            }
            else
            {
                await _vnisCoreMongoInvoice01Repository.UpdateIsSyncedToElasticSearchAsync(mongoInvoice01s.Select(x => x.Id).ToList(), (short)SyncElasticSearchStatus.PendingSyncUpdate.GetHashCode());
                Log.Error(@$"RESPONSE UPDATE ES VERIFICATION CODE: {bulkResponse.Errors} - {bulkResponse.DebugInformation} - {bulkResponse.ItemsWithErrors} - {bulkResponse.OriginalException}");
            }
        }

        /// <summary>
        /// update Trạng thái đã kê khai của hóa đơn không mã
        /// </summary>
        /// <param name="mongoInvoice01s"></param>
        /// <param name="tenantGroupIndexEs"></param>
        /// <returns></returns>
        public async Task UpdateESIsDeclaredAsync(List<MongoInvoice01Entity> mongoInvoice01s, string tenantGroupIndexEs)
        {
            var client = _elasticSearch.Client("invoice01", tenantGroupIndexEs);
            var bulkResponse = await client.BulkAsync(b => b
                      .UpdateMany(mongoInvoice01s, (descriptor, update) => descriptor
                          .Id(update.Id)
                          .Script(s => s
                              .Source($"ctx._source.{nameof(Invoice01HeaderEsDto.IsDeclared).FirstCharToLowerCase()} = params.{nameof(Invoice01HeaderEsDto.IsDeclared).FirstCharToLowerCase()}")
                                .Params(p => p
                                    .Add($"{nameof(Invoice01HeaderEsDto.IsDeclared).FirstCharToLowerCase()}", update.IsDeclared)
                                )
                          )
                      ).Refresh(Refresh.True));

            if (bulkResponse.IsValid)
            {
                await _vnisCoreMongoInvoice01Repository.UpdateIsSyncedToElasticSearchAsync(mongoInvoice01s.Select(x => x.Id).ToList(), (short)SyncElasticSearchStatus.Synced.GetHashCode());
                Log.Information(@$"RESPONSE UPDATE ES IsDeclared: {bulkResponse.IsValid} - {bulkResponse.Items} - {bulkResponse.DebugInformation}");
            }
            else
            {
                await _vnisCoreMongoInvoice01Repository.UpdateIsSyncedToElasticSearchAsync(mongoInvoice01s.Select(x => x.Id).ToList(), (short)SyncElasticSearchStatus.PendingSyncUpdate.GetHashCode());
                Log.Error(@$"RESPONSE UPDATE ES IsDeclared: {bulkResponse.Errors} - {bulkResponse.DebugInformation} - {bulkResponse.ItemsWithErrors} - {bulkResponse.OriginalException}");
            }
        }

        /// <summary>
        /// update ES cho nghiệp vụ xoá huỷ or xoá bỏ
        /// </summary>
        /// <param name="invoice01s"></param>
        /// <param name="hasApproveDelete"></param>
        /// <param name="deleteTime"></param>
        /// <param name="groups"></param>
        /// <param name="group"></param>
        /// <returns></returns>
        //public async Task UpdateEsDeleteAsync(List<Invoice01HeaderEntity> invoice01s, bool hasApproveDelete, DateTime deleteTime, List<string> groups, string group)
        //{
        //    if (!groups.Any())
        //        throw new UserFriendlyException("Chưa có cấu hình TenantGroupsPrivate");


        //    var tenantGroupIndexEs = "group-x";
        //    var tenantGroup = group.ToString().Replace(",", ".");
        //    if (groups.Contains(tenantGroup))
        //        tenantGroupIndexEs = $"group-{tenantGroup}";

        //    var client = _elasticSearch.Client("invoice01", tenantGroupIndexEs);

        //    if (hasApproveDelete)
        //    {
        //        var bulkResponse = await client.BulkAsync(b => b
        //                .UpdateMany(invoice01s, (descriptor, update) => descriptor
        //                    .Id(update.Id)
        //                    .Script(s => s
        //                        .Source($"ctx._source.{nameof(Invoice01HeaderEsDto.DeleteTime).FirstCharToLowerCase()} = params.{nameof(Invoice01HeaderEsDto.DeleteTime).FirstCharToLowerCase()};" +
        //                                $"ctx._source.{nameof(Invoice01HeaderEsDto.ApproveDeleteStatus).FirstCharToLowerCase()} = params.{nameof(Invoice01HeaderEsDto.ApproveDeleteStatus).FirstCharToLowerCase()};" +
        //                                $"ctx._source.{nameof(Invoice01HeaderEsDto.DeleteTimeNumber).FirstCharToLowerCase()} = params.{nameof(Invoice01HeaderEsDto.DeleteTimeNumber).FirstCharToLowerCase()};"
        //                                )
        //                        .Params(p => p
        //                            .Add($"{nameof(Invoice01HeaderEsDto.DeleteTime).FirstCharToLowerCase()}", deleteTime)
        //                            .Add($"{nameof(Invoice01HeaderEsDto.ApproveDeleteStatus).FirstCharToLowerCase()}", (short)ApproveStatus.ChoDuyet)
        //                            .Add($"{nameof(Invoice01HeaderEsDto.DeleteTimeNumber).FirstCharToLowerCase()}", int.Parse(deleteTime.ToString("yyyyMMdd")))
        //                            )
        //                    )
        //                ).Refresh(Refresh.True));

        //        if (!bulkResponse.IsValid)
        //            Log.Error(@$"RESPONSE UPDATE ES DELETE: {bulkResponse.Errors} - {bulkResponse.DebugInformation} - {bulkResponse.ItemsWithErrors} - {bulkResponse.OriginalException}");
        //    }
        //    else
        //    {
        //        var bulkResponse = await client.BulkAsync(b => b
        //                .UpdateMany(invoice01s, (descriptor, update) => descriptor
        //                    .Id(update.Id)
        //                    .Script(s => s
        //                        .Source($"ctx._source.{nameof(Invoice01HeaderEsDto.DeleteTime).FirstCharToLowerCase()} = params.{nameof(Invoice01HeaderEsDto.DeleteTime).FirstCharToLowerCase()};" +
        //                                $"ctx._source.{nameof(Invoice01HeaderEsDto.InvoiceStatus).FirstCharToLowerCase()} = params.{nameof(Invoice01HeaderEsDto.InvoiceStatus).FirstCharToLowerCase()};" +
        //                                $"ctx._source.{nameof(Invoice01HeaderEsDto.DeleteTimeNumber).FirstCharToLowerCase()} = params.{nameof(Invoice01HeaderEsDto.DeleteTimeNumber).FirstCharToLowerCase()};"
        //                                )
        //                        .Params(p => p
        //                            .Add($"{nameof(Invoice01HeaderEsDto.DeleteTime).FirstCharToLowerCase()}", deleteTime)
        //                            .Add($"{nameof(Invoice01HeaderEsDto.InvoiceStatus).FirstCharToLowerCase()}", (short)InvoiceStatus.XoaBo)
        //                            .Add($"{nameof(Invoice01HeaderEsDto.DeleteTimeNumber).FirstCharToLowerCase()}", int.Parse(deleteTime.ToString("yyyyMMdd")))
        //                            )
        //                    )
        //                ).Refresh(Refresh.True));

        //        if (!bulkResponse.IsValid)
        //            Log.Error(@$"RESPONSE UPDATE ES DELETE: {bulkResponse.Errors} - {bulkResponse.DebugInformation} - {bulkResponse.ItemsWithErrors} - {bulkResponse.OriginalException}");
        //    }
        //}


    }
}
