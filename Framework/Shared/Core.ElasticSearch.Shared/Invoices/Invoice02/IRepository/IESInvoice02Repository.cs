using System.Collections.Generic;
using System.Threading.Tasks;

using VnisCore.Core.MongoDB.Entities.Invoices.Invoice02;

namespace Core.ElasticSearch.Shared.Invoices.Invoice02.IRepository
{
    public interface IESInvoice02Repository
    {
        #region Đồng bộ ES dành cho Form or API
        
        #endregion


        #region Đồng bộ ES dành cho Worker
        /// <summary>
        /// Update ES cho nghiệp vụ duyệt: duyệt để ký, duyệt xoá huỷ, duyệt xoá bỏ
        /// </summary>
        /// <param name="mongoInvoices"></param>
        /// <param name="tenantGroupIndexEs"></param>
        /// <returns></returns>
        Task UpdateEsApproveAsync(List<MongoInvoice02Entity> mongoInvoices, string tenantGroupIndexEs);

        /// <summary>
        /// Update ES: Xoá huỷ
        /// </summary>
        /// <param name="mongoInvoices"></param>
        /// <param name="tenantGroupIndexEs"></param>
        /// <returns></returns>
        Task UpdateESCancelAsync(List<MongoInvoice02Entity> mongoInvoices, string tenantGroupIndexEs);

        /// <summary>
        /// Sync ES: xoá bỏ
        /// </summary>
        /// <param name="mongoInvoices"></param>
        /// <param name="tenantGroupIndexEs"></param>
        /// <returns></returns>
        Task UpdateESDeleteAsync(List<MongoInvoice02Entity> mongoInvoices, string tenantGroupIndexEs);

        /// <summary>
        /// Sync ES: trang thai ky
        /// </summary>
        /// <param name="mongoInvoices"></param>
        /// <param name="tenantGroupIndexEs"></param>
        /// <returns></returns>
        Task UpdateESSignStatusAsync(List<MongoInvoice02Entity> mongoInvoices, string tenantGroupIndexEs);

        /// <summary>
        /// Sync ES: mã của Cơ quan thuế
        /// </summary>
        /// <param name="mongoInvoices"></param>
        /// <param name="tenantGroupIndexEs"></param>
        /// <returns></returns>
        Task UpdateESVerificationCodeAsync(List<MongoInvoice02Entity> mongoInvoices, string tenantGroupIndexEs);

        /// <summary>
        /// update trạng thái đã kê khai cho hóa đơn ko mã
        /// </summary>
        /// <param name="mongoInvoices"></param>
        /// <param name="tenantGroupIndexEs"></param>
        /// <returns></returns>
        Task UpdateESIsDecraredAsync(List<MongoInvoice02Entity> mongoInvoices, string tenantGroupIndexEs);
        #endregion
    }
}
