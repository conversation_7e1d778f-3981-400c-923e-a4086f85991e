using Core.ElasticSearch.Shared.Invoices.Invoice02.IRepository;
using Core.Shared.Constants;
using Core.Shared.ElasticSearch.Dto.Invoice02;
using Core.Shared.Extensions;

using Elasticsearch.Net;

using Serilog;

using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using VnisCore.Core.MongoDB.Entities.Invoices.Invoice02;
using VnisCore.Core.MongoDB.IRepository.Invoices.Invoice02;

namespace Core.ElasticSearch.Shared.Invoices.Invoice02.Repository
{
    public class ESInvoice02Repository : IESInvoice02Repository
    {
        private readonly Core.Shared.ElasticSearch.ElasticSearch _elasticSearch;
        private readonly IVnisCoreMongoInvoice02Repository _vnisCoreMongoInvoice02Repository;

        public ESInvoice02Repository(
            Core.Shared.ElasticSearch.ElasticSearch elasticSearch,
            IVnisCoreMongoInvoice02Repository vnisCoreMongoInvoice02Repository)
        {
            _elasticSearch = elasticSearch;
            _vnisCoreMongoInvoice02Repository = vnisCoreMongoInvoice02Repository;
        }

        /// <summary>
        /// Sync ES cho nghiệp vụ duyệt: duyệt để ký, duyệt xoá huỷ, duyệt xoá bỏ
        /// </summary>
        /// <param name="mongoInvoices"></param>
        /// <param name="tenantGroupIndexEs"></param>
        /// <returns></returns>
        public async Task UpdateEsApproveAsync(List<MongoInvoice02Entity> mongoInvoices, string tenantGroupIndexEs)
        {
            var client = _elasticSearch.Client("invoice02", tenantGroupIndexEs);

            var bulkResponse = await client.BulkAsync(b => b
                        .UpdateMany(mongoInvoices, (descriptor, update) => descriptor
                            .Id(update.Id)
                            .Script(s => s
                                .Source($"ctx._source.{nameof(Invoice02HeaderEsDto.ApproveDeleteStatus).FirstCharToLowerCase()} = params.{nameof(Invoice02HeaderEsDto.ApproveDeleteStatus).FirstCharToLowerCase()};" +
                                        $"ctx._source.{nameof(Invoice02HeaderEsDto.ApproveCancelStatus).FirstCharToLowerCase()} = params.{nameof(Invoice02HeaderEsDto.ApproveCancelStatus).FirstCharToLowerCase()};" +
                                        $"ctx._source.{nameof(Invoice02HeaderEsDto.ApproveStatus).FirstCharToLowerCase()} = params.{nameof(Invoice02HeaderEsDto.ApproveStatus).FirstCharToLowerCase()};" +
                                        $"ctx._source.{nameof(Invoice02HeaderEsDto.InvoiceStatus).FirstCharToLowerCase()} = params.{nameof(Invoice02HeaderEsDto.InvoiceStatus).FirstCharToLowerCase()};"
                                )
                                .Params(p => p
                                    .Add($"{nameof(Invoice02HeaderEsDto.ApproveDeleteStatus).FirstCharToLowerCase()}", update.ApproveDeleteStatus)
                                    .Add($"{nameof(Invoice02HeaderEsDto.ApproveCancelStatus).FirstCharToLowerCase()}", update.ApproveCancelStatus)
                                    .Add($"{nameof(Invoice02HeaderEsDto.ApproveStatus).FirstCharToLowerCase()}", update.ApproveStatus)
                                    .Add($"{nameof(Invoice02HeaderEsDto.InvoiceStatus).FirstCharToLowerCase()}", update.InvoiceStatus)
                                )
                            )
                        ).Refresh(Refresh.True));

            if (bulkResponse.IsValid)
            {
                await _vnisCoreMongoInvoice02Repository.UpdateIsSyncedToElasticSearchAsync(mongoInvoices.Select(x=> x.Id).ToList(), (short)SyncElasticSearchStatus.Synced.GetHashCode());
                Log.Information(@$"RESPONSE UPDATE ES APPROVE STATUS: {bulkResponse.IsValid} - {bulkResponse.Items} - {bulkResponse.DebugInformation}");
            }
            else
            {
                await _vnisCoreMongoInvoice02Repository.UpdateIsSyncedToElasticSearchAsync(mongoInvoices.Select(x => x.Id).ToList(), (short)SyncElasticSearchStatus.PendingSyncUpdate.GetHashCode());
                Log.Information(@$"RESPONSE UPDATE ES APPROVE STATUS: {bulkResponse.Errors} - {bulkResponse.DebugInformation} - {bulkResponse.ItemsWithErrors} - {bulkResponse.OriginalException}");
            }
        }


        /// <summary>
        /// Sync ES: Xoá huỷ
        /// </summary>
        /// <param name="mongoInvoices"></param>
        /// <param name="tenantGroupIndexEs"></param>
        /// <returns></returns>
        public async Task UpdateESCancelAsync(List<MongoInvoice02Entity> mongoInvoices, string tenantGroupIndexEs)
        {
            var client = _elasticSearch.Client("invoice02", tenantGroupIndexEs);

            var bulkResponse = await client.BulkAsync(b => b
                        .UpdateMany(mongoInvoices, (descriptor, update) => descriptor
                            .Id(update.Id)
                            .Script(s => s
                                .Source($"ctx._source.{nameof(Invoice02HeaderEsDto.CancelTime).FirstCharToLowerCase()} = params.{nameof(Invoice02HeaderEsDto.CancelTime).FirstCharToLowerCase()};" +
                                        $"ctx._source.{nameof(Invoice02HeaderEsDto.ApproveCancelStatus).FirstCharToLowerCase()} = params.{nameof(Invoice02HeaderEsDto.ApproveCancelStatus).FirstCharToLowerCase()};" +
                                        $"ctx._source.{nameof(Invoice02HeaderEsDto.CancelTimeNumber).FirstCharToLowerCase()} = params.{nameof(Invoice02HeaderEsDto.CancelTimeNumber).FirstCharToLowerCase()};" +
                                        $"ctx._source.{nameof(Invoice02HeaderEsDto.InvoiceStatus).FirstCharToLowerCase()} = params.{nameof(Invoice02HeaderEsDto.InvoiceStatus).FirstCharToLowerCase()};")
                                .Params(p => p
                                    .Add($"{nameof(Invoice02HeaderEsDto.CancelTime).FirstCharToLowerCase()}", update.CancelTime.HasValue ? update.CancelTime : null)
                                    .Add($"{nameof(Invoice02HeaderEsDto.ApproveCancelStatus).FirstCharToLowerCase()}", update.ApproveCancelStatus)
                                    .Add($"{nameof(Invoice02HeaderEsDto.CancelTimeNumber).FirstCharToLowerCase()}", update.CancelTime.HasValue ? int.Parse(update.CancelTime.Value.ToString("yyyyMMdd")) : null)
                                    .Add($"{nameof(Invoice02HeaderEsDto.InvoiceStatus).FirstCharToLowerCase()}", update.InvoiceStatus)
                                )
                            )
                        ).Refresh(Refresh.True));

            if (bulkResponse.IsValid)
            {
                await _vnisCoreMongoInvoice02Repository.UpdateIsSyncedToElasticSearchAsync(mongoInvoices.Select(x => x.Id).ToList(), (short)SyncElasticSearchStatus.Synced.GetHashCode());
                Log.Information(@$"RESPONSE UPDATE ES CANCEL STATUS: {bulkResponse.IsValid} - {bulkResponse.Items} - {bulkResponse.DebugInformation}");
            }
            else
            {
                await _vnisCoreMongoInvoice02Repository.UpdateIsSyncedToElasticSearchAsync(mongoInvoices.Select(x => x.Id).ToList(), (short)SyncElasticSearchStatus.PendingSyncUpdate.GetHashCode());
                Log.Error(@$"RESPONSE UPDATE ES CANCEL STATUS: {bulkResponse.Errors} - {bulkResponse.DebugInformation} - {bulkResponse.ItemsWithErrors} - {bulkResponse.OriginalException}");
            }
        }

        /// <summary>
        /// Sync ES: Xoá bỏ
        /// </summary>
        /// <param name="mongoInvoices"></param>
        /// <param name="tenantGroupIndexEs"></param>
        /// <returns></returns>
        public async Task UpdateESDeleteAsync(List<MongoInvoice02Entity> mongoInvoices, string tenantGroupIndexEs)
        {
            var client = _elasticSearch.Client("invoice02", tenantGroupIndexEs);
            var bulkResponse = await client.BulkAsync(b => b
                       .UpdateMany(mongoInvoices, (descriptor, update) => descriptor
                           .Id(update.Id)
                           .Script(s => s
                               .Source($"ctx._source.{nameof(Invoice02HeaderEsDto.DeleteTime).FirstCharToLowerCase()} = params.{nameof(Invoice02HeaderEsDto.DeleteTime).FirstCharToLowerCase()};" +
                                       $"ctx._source.{nameof(Invoice02HeaderEsDto.ApproveDeleteStatus).FirstCharToLowerCase()} = params.{nameof(Invoice02HeaderEsDto.ApproveDeleteStatus).FirstCharToLowerCase()};" +
                                       $"ctx._source.{nameof(Invoice02HeaderEsDto.InvoiceStatus).FirstCharToLowerCase()} = params.{nameof(Invoice02HeaderEsDto.InvoiceStatus).FirstCharToLowerCase()};" +
                                       $"ctx._source.{nameof(Invoice02HeaderEsDto.DeleteTimeNumber).FirstCharToLowerCase()} = params.{nameof(Invoice02HeaderEsDto.DeleteTimeNumber).FirstCharToLowerCase()};"
                                       )
                               .Params(p => p
                                   .Add($"{nameof(Invoice02HeaderEsDto.DeleteTime).FirstCharToLowerCase()}", update.DeleteTime.HasValue ? update.DeleteTime : null)
                                   .Add($"{nameof(Invoice02HeaderEsDto.ApproveDeleteStatus).FirstCharToLowerCase()}", update.ApproveDeleteStatus)
                                   .Add($"{nameof(Invoice02HeaderEsDto.InvoiceStatus).FirstCharToLowerCase()}", update.InvoiceStatus)
                                   .Add($"{nameof(Invoice02HeaderEsDto.DeleteTimeNumber).FirstCharToLowerCase()}", update.DeleteTime.HasValue ? int.Parse(update.DeleteTime.Value.ToString("yyyyMMdd")) : null)
                                   )
                           )
                       ).Refresh(Refresh.True));

            if (bulkResponse.IsValid)
            {
                await _vnisCoreMongoInvoice02Repository.UpdateIsSyncedToElasticSearchAsync(mongoInvoices.Select(x => x.Id).ToList(), (short)SyncElasticSearchStatus.Synced.GetHashCode());
                Log.Information(@$"RESPONSE UPDATE ES DELETE STATUS: {bulkResponse.IsValid} - {bulkResponse.Items} - {bulkResponse.DebugInformation}");
            }
            else
            {
                await _vnisCoreMongoInvoice02Repository.UpdateIsSyncedToElasticSearchAsync(mongoInvoices.Select(x => x.Id).ToList(), (short)SyncElasticSearchStatus.PendingSyncUpdate.GetHashCode());
                Log.Error(@$"RESPONSE UPDATE ES DELETE STATUS: {bulkResponse.Errors} - {bulkResponse.DebugInformation} - {bulkResponse.ItemsWithErrors} - {bulkResponse.OriginalException}");
            }
        }

        /// <summary>
        /// Sync ES: trang thai ky
        /// </summary>
        /// <param name="mongoInvoices"></param>
        /// <param name="tenantGroupIndexEs"></param>
        /// <returns></returns>
        public async Task UpdateESSignStatusAsync(List<MongoInvoice02Entity> mongoInvoices, string tenantGroupIndexEs)
        {
            var client = _elasticSearch.Client("invoice02", tenantGroupIndexEs);
            var bulkResponse = await client.BulkAsync(b => b
                      .UpdateMany(mongoInvoices, (descriptor, update) => descriptor
                          .Id(update.Id)
                          .Script(s => s
                              .Source($"ctx._source.{nameof(Invoice02HeaderEsDto.SignStatus).FirstCharToLowerCase()} = params.{nameof(Invoice02HeaderEsDto.SignStatus).FirstCharToLowerCase()}")
                                .Params(p => p
                                    .Add($"{nameof(Invoice02HeaderEsDto.SignStatus).FirstCharToLowerCase()}", update.SignStatus)
                                )
                          )
                      ).Refresh(Refresh.True));

            if (bulkResponse.IsValid)
            {
                var invalidItems = bulkResponse.ItemsWithErrors.ToList();
                if (invalidItems.Any())
                {
                    var idsInvalid = invalidItems.Select(x => long.Parse(x.Id)).ToList();
                    if (idsInvalid.Any())
                        await _vnisCoreMongoInvoice02Repository.UpdateIsSyncedToElasticSearchAsync(idsInvalid, (short)SyncElasticSearchStatus.PendingSyncUpdate.GetHashCode());

                    var idsValid = (mongoInvoices.Select(x => x.Id).ToList()).Except(idsInvalid).ToList();
                    if (idsValid.Any())
                        await _vnisCoreMongoInvoice02Repository.UpdateIsSyncedToElasticSearchAsync(idsValid, (short)SyncElasticSearchStatus.Synced.GetHashCode());

                    Log.Error($@"Invalid Items update SignStatus: {string.Join(",", idsInvalid)}");
                }
                else
                {
                    await _vnisCoreMongoInvoice02Repository.UpdateIsSyncedToElasticSearchAsync(mongoInvoices.Select(x => x.Id).ToList(), (short)SyncElasticSearchStatus.Synced.GetHashCode());
                    Log.Information(@$"RESPONSE UPDATE ES SIGN STATUS: {bulkResponse.IsValid} - {bulkResponse.Items} - {bulkResponse.DebugInformation}");
                }
            }
            else
            {
                var invalidItems = bulkResponse.ItemsWithErrors.ToList();
                var idsInvalid = invalidItems.Select(x => long.Parse(x.Id)).ToList();
                if (idsInvalid.Any())
                    await _vnisCoreMongoInvoice02Repository.UpdateIsSyncedToElasticSearchAsync(mongoInvoices.Select(x => x.Id).ToList(), (short)SyncElasticSearchStatus.PendingSyncUpdate.GetHashCode());

                var idsValid = (mongoInvoices.Select(x => x.Id).ToList()).Except(idsInvalid).ToList();
                if (idsValid.Any())
                    await _vnisCoreMongoInvoice02Repository.UpdateIsSyncedToElasticSearchAsync(idsValid, (short)SyncElasticSearchStatus.Synced.GetHashCode());

                Log.Error($@"Invalid Items update SignStatus: {string.Join(",", idsInvalid)}");
                Log.Error(@$"RESPONSE UPDATE ES SIGN STATUS: {bulkResponse.Errors} - {bulkResponse.DebugInformation} - {bulkResponse.ItemsWithErrors} - {bulkResponse.OriginalException}");
            }
        }

        public async Task UpdateESVerificationCodeAsync(List<MongoInvoice02Entity> mongoInvoices, string tenantGroupIndexEs)
        {
            var client = _elasticSearch.Client("invoice02", tenantGroupIndexEs);
            var bulkResponse = await client.BulkAsync(b => b
                      .UpdateMany(mongoInvoices, (descriptor, update) => descriptor
                          .Id(update.Id)
                          .Script(s => s
                              .Source($"ctx._source.{nameof(Invoice02HeaderEsDto.VerificationCode).FirstCharToLowerCase()} = params.{nameof(Invoice02HeaderEsDto.VerificationCode).FirstCharToLowerCase()}")
                                .Params(p => p
                                    .Add($"{nameof(Invoice02HeaderEsDto.VerificationCode).FirstCharToLowerCase()}", update.VerificationCode)
                                )
                          )
                      ).Refresh(Refresh.True));

            if (bulkResponse.IsValid)
            {
                await _vnisCoreMongoInvoice02Repository.UpdateIsSyncedToElasticSearchAsync(mongoInvoices.Select(x => x.Id).ToList(), (short)SyncElasticSearchStatus.Synced.GetHashCode());
                Log.Information(@$"RESPONSE UPDATE ES VERIFICATION CODE: {bulkResponse.IsValid} - {bulkResponse.Items} - {bulkResponse.DebugInformation}");
            }
            else
            {
                await _vnisCoreMongoInvoice02Repository.UpdateIsSyncedToElasticSearchAsync(mongoInvoices.Select(x => x.Id).ToList(), (short)SyncElasticSearchStatus.PendingSyncUpdate.GetHashCode());
                Log.Error(@$"RESPONSE UPDATE ES VERIFICATION CODE: {bulkResponse.Errors} - {bulkResponse.DebugInformation} - {bulkResponse.ItemsWithErrors} - {bulkResponse.OriginalException}");
            }
        }

        /// <summary>
        /// update Trạng thái đã kê khai của hóa đơn không mã
        /// </summary>
        /// <param name="mongoInvoices"></param>
        /// <param name="tenantGroupIndexEs"></param>
        /// <returns></returns>
        public async Task UpdateESIsDecraredAsync(List<MongoInvoice02Entity> mongoInvoices, string tenantGroupIndexEs)
        {
            var client = _elasticSearch.Client("invoice02", tenantGroupIndexEs);
            var bulkResponse = await client.BulkAsync(b => b
                      .UpdateMany(mongoInvoices, (descriptor, update) => descriptor
                          .Id(update.Id)
                          .Script(s => s
                              .Source($"ctx._source.{nameof(Invoice02HeaderEsDto.IsDeclared).FirstCharToLowerCase()} = params.{nameof(Invoice02HeaderEsDto.IsDeclared).FirstCharToLowerCase()}")
                                .Params(p => p
                                    .Add($"{nameof(Invoice02HeaderEsDto.IsDeclared).FirstCharToLowerCase()}", update.IsDeclared)
                                )
                          )
                      ).Refresh(Refresh.True));

            if (bulkResponse.IsValid)
            {
                await _vnisCoreMongoInvoice02Repository.UpdateIsSyncedToElasticSearchAsync(mongoInvoices.Select(x => x.Id).ToList(), (short)SyncElasticSearchStatus.Synced.GetHashCode());
                Log.Information(@$"RESPONSE UPDATE ES IsDeclared: {bulkResponse.IsValid} - {bulkResponse.Items} - {bulkResponse.DebugInformation}");
            }
            else
            {
                await _vnisCoreMongoInvoice02Repository.UpdateIsSyncedToElasticSearchAsync(mongoInvoices.Select(x => x.Id).ToList(), (short)SyncElasticSearchStatus.PendingSyncUpdate.GetHashCode());
                Log.Error(@$"RESPONSE UPDATE ES IsDeclared: {bulkResponse.Errors} - {bulkResponse.DebugInformation} - {bulkResponse.ItemsWithErrors} - {bulkResponse.OriginalException}");
            }
        }
    }
}
