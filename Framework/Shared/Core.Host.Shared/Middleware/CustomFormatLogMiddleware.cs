using Core.DependencyInjection;
using Core.MultiTenancy;
using Core.Users;
using Microsoft.AspNetCore.Http;
using Serilog.Context;
using System;
using System.Threading.Tasks;

namespace Core.Host.Shared.Middleware
{
    public class CustomFormatLogMiddleware : IMiddleware, ITransientDependency
    {
        private readonly ICurrentTenant _currentTenant;
        private readonly ICurrentUser _currentUser;

        public CustomFormatLogMiddleware(
            ICurrentTenant currentTenant,
            ICurrentUser currentUser)
        {
            _currentTenant = currentTenant;
            _currentUser = currentUser;
        }

        public async Task InvokeAsync(HttpContext context, RequestDelegate next)
        {
            if (_currentUser != null && _currentUser.Id != null && _currentUser.Id != Guid.Empty)
            {
                LogContext.PushProperty("UserName", _currentUser.UserName.ToString());
            }

            if (_currentTenant != null && _currentTenant.Id != null && _currentTenant.Id != Guid.Empty)
            {
                LogContext.PushProperty("TenantId", _currentTenant.Id.ToString());
                LogContext.PushProperty("TaxCode", _currentTenant.TaxCode.ToString());
            }

            await next(context);
        }
    }
}
