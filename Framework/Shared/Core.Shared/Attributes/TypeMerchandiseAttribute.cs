using Core.Shared.Constants;
using System;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace Core.Shared.Attributes
{
    public class TypeMerchandiseAttribute : ValidationAttribute
    {
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value == null)
                return ValidationResult.Success;

            var source = (short)value;

            var productTypes = Enum.GetValues(typeof(TypeMerchandise)).Cast<int>().ToList();

            if (!productTypes.Any(x => x == source))
                return new ValidationResult(ErrorMessage = $"Định dạng tính chất hàng hoá không đúng");

            return ValidationResult.Success;
        }
    }
}
