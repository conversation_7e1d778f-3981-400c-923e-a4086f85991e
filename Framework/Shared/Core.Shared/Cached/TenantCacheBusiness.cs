using Core.DependencyInjection;
using Core.MultiTenancy;
using Core.Shared.Cached.Dtos;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Dapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Core.Shared.Cached
{
    public interface ITenantCacheBusiness : IScopedDependency
    {
        Task<List<TenantCaching>> GetAllTenantAsync();
    }
    public class TenantCacheBusiness : ITenantCacheBusiness
    {
        private readonly IAppFactory _appFactory;
        private readonly IRedisCacheService _redisCacheService;

        public TenantCacheBusiness(IAppFactory appFactory,
            IRedisCacheService redisCacheService)
        {
            _appFactory = appFactory;
            _redisCacheService = redisCacheService;
        }
        public async Task<List<TenantCaching>> GetAllTenantAsync()
        {
            var key = CacheKeyBuilder.BuildCacheKeyWithPath(CacheKeyPath.t, CacheKeyPath.Tenant, CacheKeyPath.AllTenant);
            // Thiết lập TTL key = 24 giờ
            var cacheExpiration = new TimeSpan(24, 0, 0);

            var value = _redisCacheService.Get<List<TenantCaching>>(key);
            if (value == null)
            {
                // Case: Ko có giá trị tại Cache
                // Lấy giá trị DB
                var tenants = await _appFactory.AuthDatabase.Connection.QueryAsync<TenantCaching>($@"
                                                    SELECT 
	                                                    ""Id"" ,
	                                                    ""TaxCode"",
                                                        ""Group""
                                                    FROM ""VnisTenants"" vt 
                                                    WHERE ""IsDeleted"" = 0");
                if (tenants != null)
                {
                    value = tenants.ToList(); 
                    _redisCacheService.Set<List<TenantCaching>>(key, value, cacheExpiration);
                }
            }

            return value;
        }
    }
}
