using System.ComponentModel.DataAnnotations;

namespace Core.Shared.Constants
{
    /// <summary>
    /// Trạng thái hoá đơn của Bảng Tổng Hợp
    /// </summary>
    public enum TaxReport01Status
    {
        /// <summary>
        /// Phân loại hoá đơn: 
        /// Gốc
        /// Bị Điều chỉnh
        /// Bị thay thế ( được update từ phân loại HĐ Gốc → Bị thay thế)
        /// </summary>
        [Display(Name = "Mới")]
        Moi = 0,

        /// <summary>
        /// Phân loại hoá đơn: HĐ xoá bỏ
        /// </summary>
        [Display(Name = "Huỷ")]
        Huy = 1,

        /// <summary>
        /// Phân loại hoá đơn: HĐ Điều chỉnh
        /// </summary>
        [Display(Name = "Điều chỉnh")]
        DieuChinh = 2,

        /// <summary>
        /// Phân loại hoá đơn: 
        /// HĐ Thay thế
        /// Bị thay thế ( được update từ phân loại HĐ thay thế → Bị thay thế)
        /// </summary>
        [Display(Name = "Thay thế")]
        ThayThe = 3
    }
}
