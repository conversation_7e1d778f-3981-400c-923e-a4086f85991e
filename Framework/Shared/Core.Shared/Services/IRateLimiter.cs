using Core.Shared.Cached;
using Core.Shared.Models;
using Microsoft.Extensions.Configuration;
using System;

namespace Core.Shared.Services
{
    public interface IRateLimiter
    {
        RateLimiterModel AllowRequest(string taxCode, string userType);
    }

    public class RateLimiter : IRateLimiter
    {
        private readonly IRedisCacheService _redisCacheService;
        private readonly IConfiguration _configuration;

        public RateLimiter(
            IRedisCacheService redisCacheService,
            IConfiguration configuration
            )
        {
            _redisCacheService = redisCacheService;
            _configuration = configuration;
        }

        public RateLimiterModel AllowRequest(string taxCode, string userType)
        {
            var rateLimiterModel = new RateLimiterModel();

            int.TryParse(_configuration.GetSection("Settings:LimitReq").Value, out var limitReq);
            int.TryParse(_configuration.GetSection("Settings:Expiry").Value, out var expiry);

            if (limitReq == 0)
            {
                limitReq = 5;
            }

            if (expiry == 0)
            {
                expiry = 5;
            }

            rateLimiterModel.LimitReq = limitReq;
            rateLimiterModel.Expiry = expiry;

            TimeSpan timeSpan = new TimeSpan(0, expiry, 0);
            var cacheKey = $@"t:ForgotPassword:{userType}:{taxCode}";
            var count = _redisCacheService.Increment(cacheKey, timeSpan);
            if (count > limitReq)
            {
                rateLimiterModel.AllowReq = false;
                return rateLimiterModel;
            }
            else
            {
                rateLimiterModel.AllowReq = true;
                return rateLimiterModel;
            }
        }
    }
}
