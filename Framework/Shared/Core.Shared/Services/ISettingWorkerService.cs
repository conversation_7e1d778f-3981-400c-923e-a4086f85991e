using Core.DependencyInjection;
using Core.MultiTenancy;
using Core.Shared.Cached;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Dapper;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Core.Shared.Services
{
    /// <summary>
    /// Lấy Setting nguồn Worker
    /// </summary>
    public interface ISettingWorkerService : IScopedDependency
    {
        Task<int> GetByCodeAsync(Guid tenantId, string code, string taxCode);
    }
    public class SettingWorkerService : ISettingWorkerService
    {
        private readonly IAppFactory _appFactory;
        private readonly IRedisCacheService _redisCacheService;
        public SettingWorkerService(IAppFactory appFactory,
            IRedisCacheService redisCacheService)
        {
            _appFactory = appFactory;
            _redisCacheService = redisCacheService;
        }
        public async Task<int> GetByCodeAsync(Guid tenantId, string code, string taxCode)
        {
            var key = CacheKeyBuilder.BuildCacheKeyWithPath(CacheKeyPath.t, taxCode, CacheKeyPath.Setting, CacheKeyPath.Tvan, code);
            // Thiết lập TTL key = 24 giờ
            // bool isExistTenantSetting = true;
            // bool isExistDefaultTenantSetting = true;

            var cacheExpiration = new TimeSpan(24, 0, 0);
            var value = _redisCacheService.Get<int?>(key);

            if (value == null)
            {
                // Case: Ko có giá trị tại Cache
                // Lấy giá trị DB
                var settings = await _appFactory.AuthDatabase.Connection.QueryAsync<TenantSetting>($@"
                                                    SELECT 
                                                        ""Id"",
                                                        ""Code"",
                                                        ""Value"",
                                                        ""TenantId""
                                                    FROM ""VnisSettings"" 
                                                    WHERE ""Code"" = '{code}' 
                                                    AND (""TenantId"" = '{OracleExtension.ConvertGuidToRaw(tenantId)}' 
                                                           OR ""TenantId"" = '{OracleExtension.ConvertGuidToRaw(Guid.Empty)}' ) 
                                                    And ""IsDeleted"" = 0");

                TenantSetting setting;
                if (settings != null && settings.Any())
                {
                    if (settings.Where(x => x.TenantId == tenantId).Any())
                    {
                        setting = settings.Where(x => x.TenantId == tenantId).First();
                        value = int.Parse(setting.Value);
                    }
                    else
                    {
                        setting = settings.Where(x => x.TenantId == Guid.Empty).First();
                        value = int.Parse(setting.Value);
                    }
                }

                key = CacheKeyBuilder.BuildCacheKeyWithPath(CacheKeyPath.t, taxCode, CacheKeyPath.Setting, CacheKeyPath.Tvan, code);
                _redisCacheService.Set<int>(key, value.Value, cacheExpiration);

                // Nếu không lưu cache
                //if (!isExistTenantSetting)
                //{
                //    key = CacheKeyBuilder.BuildCacheKeyWithPath(CacheKeyPath.t, taxCode, CacheKeyPath.Setting, CacheKeyPath.Tvan, code);
                //    _redisCacheService.Set<int>(key, value.Value, cacheExpiration);
                //}
                //if (!isExistDefaultTenantSetting)
                //{
                //    key = CacheKeyBuilder.BuildCacheKeyWithPath(CacheKeyPath.t, CacheKeyPath.DefaultTenant, CacheKeyPath.Setting, CacheKeyPath.Tvan, code);
                //    _redisCacheService.Set<int>(key, value.Value, cacheExpiration);
                //}
            }

            //if (value == null)
            //{
            //    // Ko có giá trị
            //    // Lấy cache tenant mặc định
            //    isExistTenantSetting = false;

            //    key = CacheKeyBuilder.BuildCacheKeyWithPath(CacheKeyPath.t, CacheKeyPath.DefaultTenant, CacheKeyPath.Setting, CacheKeyPath.Tvan, code);
            //    value = _redisCacheService.Get<int?>(key);
            //    if(value == null)
            //    {
            //        isExistDefaultTenantSetting = false;
            //    }
            //}

            return value.Value;
        }

    }
}
