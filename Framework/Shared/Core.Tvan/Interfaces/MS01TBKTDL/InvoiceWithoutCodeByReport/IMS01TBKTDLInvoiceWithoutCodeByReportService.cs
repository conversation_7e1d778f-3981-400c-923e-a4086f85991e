using Core.Tvan.Enums;
using System.Threading.Tasks;

namespace Core.Tvan.Interfaces.MS01TBKTDL.InvoiceWithoutCodeByReport
{
    /// <summary>
    /// phản hồi 204 Loại 2 cho hoá đơn không mã gửi bằng BTH (400)
    /// </summary>
    public interface IMS01TBKTDLInvoiceWithoutCodeByReportService
    {
        Task HandleResponseTvan(string xml, int ltbao, TransmissionPartnerEnum transmissionPartner);
    }
}
