using AutoMapper;

using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.ElasticSearch;
using Core.Shared.ElasticSearch.Dto.Invoice01;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.FileManager.Interfaces;
using Core.TenantManagement;
using Core.Tvan.Abstractions;
using Core.Tvan.Constants;
using Core.Tvan.Enums;
using Core.Tvan.Interfaces.InvoiceHasCode;
using Core.Tvan.Models;
using Core.Tvan.Models.Xmls.Invoices.Base;
using Core.Tvan.Models.Xmls.Invoices.Invoice01;

using Dapper;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;

using Serilog;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;

using VnisCore.Core.MongoDB.Entities.Invoices.Invoice01;
using VnisCore.Core.MongoDB.IRepository;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Core.Oracle.Domain.Entities.Tvan.Invoice01;

namespace Core.Tvan.Services.Invoices.InvoiceHasCode
{
    public class TvanResponseInvoice01HasCodeService : BaseTvanResponseInvoiceHasCodeService, IInvoiceHasCodeService
    {
        private readonly IAppFactory _appFactory;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;
        private readonly IFileService _fileService;
        private readonly IVnisCoreMongoInvoice01Repository _mongoInvoice01Repository;
        private readonly IConfiguration _configuration;
        private readonly ElasticSearch _elasticSearch;

        public TvanResponseInvoice01HasCodeService(IAppFactory appFactory,
                                                   IStringLocalizer<CoreLocalizationResource> localizier,
                                                   IVnisCoreMongoInvoice01Repository mongoInvoice01Repository,
                                                   IFileService fileService,
                                                   IConfiguration configuration,
                                                   ElasticSearch elasticSearch) : base(appFactory)
        {
            _appFactory = appFactory;
            _localizier = localizier;
            _fileService = fileService;
            _mongoInvoice01Repository = mongoInvoice01Repository;
            _configuration = configuration;
            _elasticSearch = elasticSearch;
        }

        public async Task HandleResponseTvan(string xml, TransmissionPartnerEnum transmissionPartner)
        {
            await ReceiveXmlFromTvanAsync<HDonModel<DLHDonInvoiceModel<TTChungDLHDonInvoice01Model, NDHDonInvoice01Model>>>(xml, transmissionPartner);
        }

        public override async Task<object> UpdateInvoiceHeader<T>(object tvanResponse)
        {
            var response = (HDonModel<DLHDonInvoiceModel<TTChungDLHDonInvoice01Model, NDHDonInvoice01Model>>)tvanResponse;
            long idInvoiceHeader = 0;

            //update oracle
            try
            {
                var invoice01HeaderRepos = _appFactory.Repository<Invoice01HeaderEntity, long>();
                var invoice01HeaderEntity = await invoice01HeaderRepos.FirstOrDefaultAsync(x => x.TemplateNo == response.DLHDon.TTChung.KHMSHDon
                                                                                                && x.SerialNo == response.DLHDon.TTChung.KHHDon
                                                                                                && x.Number == int.Parse(response.DLHDon.TTChung.SHDon)
                                                                                                && x.SellerTaxCode == response.DLHDon.NDHDon.NBan.MST
                                                                                                && !x.IsDeleted);

                if (invoice01HeaderEntity == null)
                {
                    var mongoInvoice01 = await _mongoInvoice01Repository.GetByInfoAsync(response.DLHDon.NDHDon.NBan.MST, response.DLHDon.TTChung.KHMSHDon, response.DLHDon.TTChung.KHHDon, int.Parse(response.DLHDon.TTChung.SHDon));

                    if (mongoInvoice01 == null)
                        throw new UserFriendlyException(_localizier["Vnis.BE.TvanInvoice.Invoice01TvanHasCode.InvoiceNotFound", new string[] { response.DLHDon.NDHDon.NBan.MST, response.DLHDon.TTChung.KHHDon, response.DLHDon.TTChung.SHDon, response.DLHDon.TTChung.SHDon }]);

                    invoice01HeaderEntity = _appFactory.ObjectMapper.Map<MongoInvoice01Entity, Invoice01HeaderEntity>(mongoInvoice01);
                }
                else
                {
                    idInvoiceHeader = invoice01HeaderEntity.Id;
                    invoice01HeaderEntity.VerificationCode = response.MCCQT;
                    invoice01HeaderEntity.StatusTvan = (short)TvanStatus.TCTAccept;

                    await invoice01HeaderRepos.UpdateAsync(invoice01HeaderEntity);
                }

                return invoice01HeaderEntity;
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
                throw;
            }
            finally
            {
                //update mongo
                try
                {
                    if (idInvoiceHeader != 0)
                    {
                        await UpdateEsAndMongoAsync(idInvoiceHeader, response.MCCQT, response.DLHDon.NDHDon.NBan.MST);
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, $"Đồng bộ mã hóa đơn {response.DLHDon.NDHDon.NBan.MST}-{response.DLHDon.TTChung.KHMSHDon}{response.DLHDon.TTChung.KHHDon}-{response.DLHDon.TTChung.SHDon} về mongo lỗi:{ex.Message}");
                }
            }
        }

        public override async Task UploadMinio<T>(string xml, object invoiceHeader, object responseInvoiceHasCode, TransmissionPartnerEnum transmissionPartner)
        {
            //lưu xml
            //up lên minio trước
            //lưu file vào minio trước rồi mới lưu vào db

            var invoice01HeaderEntity = (Invoice01HeaderEntity)invoiceHeader;

            var fileName = $"{invoice01HeaderEntity.SellerTaxCode}-{invoice01HeaderEntity.TemplateNo}-{invoice01HeaderEntity.SerialNo}-{invoice01HeaderEntity.InvoiceNo}.xml".Replace("/", "_");
            var tvanInvoice01HasCodeXmlEntity = new TvanInvoice01HasCodeXmlEntity
            {
                ContentType = ContentType.Xml,
                FileName = fileName,
                PhysicalFileName = $"{invoice01HeaderEntity.SellerTaxCode}-{invoice01HeaderEntity.TemplateNo}-{invoice01HeaderEntity.SerialNo}-{invoice01HeaderEntity.InvoiceNo}_{DateTime.Now.Ticks}.xml".Replace("/", "_"),
                Length = Encoding.UTF8.GetBytes(xml).Length,
                TenantId = invoice01HeaderEntity.TenantId,
                Invoice01HeaderId = invoice01HeaderEntity.Id
            };

            var pathFileMinio = $"{MediaFileType.Invoice01HasCodeTvanXml}/{invoice01HeaderEntity.TenantId}/{DateTime.Now.Year}/{DateTime.Now.Month:00}/{DateTime.Now.Day:00}/{DateTime.Now.Hour:00}/{tvanInvoice01HasCodeXmlEntity.PhysicalFileName}";
            await _fileService.UploadAsync(pathFileMinio, Encoding.UTF8.GetBytes(xml));

            var repoXml = _appFactory.Repository<TvanInvoice01HasCodeXmlEntity, long>();
            await repoXml.InsertAsync(tvanInvoice01HasCodeXmlEntity);
            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

            //TODO: refactor
            // lưu thông tin phản hồi của TCT vào bảng Invoice01HasCodeTvanInfo
            //var tvanResponse = (TDiepModel<TTChungModel, DLieuInvoice01HasCodeResponseModel>)responseInvoiceHasCode;

            ////lấy dữ liệu thẻ dữ liệu
            //XmlDocument doc = new XmlDocument();
            //doc.LoadXml(xml);
            //var ttChungNode = doc.SelectSingleNode(@"//TDiep/TTChung");
            //var ttChungXml = ttChungNode.InnerXml;

            //XmlSerializer serializer = new XmlSerializer(typeof(TTChungModel));
            //StringReader strReader = new StringReader(ttChungXml);

            //var tvanTTChungResponse = (TTChungModel)serializer.Deserialize(strReader);

            //lấy dữ liệu thẻ dữ liệu
            XmlDocument doc = new XmlDocument();
            doc.LoadXml(xml);
            var mLTDiepNode = doc.SelectSingleNode(@"//TDiep/TTChung/MLTDiep");
            var mTDiepNode = doc.SelectSingleNode(@"//TDiep/TTChung/MTDiep");
            var mTDTChieuNode = doc.SelectSingleNode(@"//TDiep/TTChung/MTDTChieu");

            var reposInvoice01HasCodeTvanInfo = _appFactory.Repository<TvanInfoInvoice01HasCodeEntity, long>();
            var invoice01HasCodeTvanInfoEntity = new TvanInfoInvoice01HasCodeEntity
            {
                InvoiceHeaderId = invoice01HeaderEntity.Id,
                MessageTypeCode = mLTDiepNode.InnerText,
                MessageCode = mTDiepNode.InnerText,
                MessageCodeReference = mTDTChieuNode.InnerText,
                FileId = tvanInvoice01HasCodeXmlEntity.Id,
                IsActive = true,
                TenantId = invoice01HeaderEntity.TenantId,
                Title = MLTDiep._202.ToDisplayName(),
                TransmissionPartner = (short)transmissionPartner
            };

            await reposInvoice01HasCodeTvanInfo.InsertAsync(invoice01HasCodeTvanInfoEntity);
        }

        private async Task UpdateEsAndMongoAsync(long id, string verificationCode, string sellerTaxCode)
        {
            var tenantGroupSetting = _configuration["Settings:TenantGroupsPrivate"];
            var groups = !string.IsNullOrEmpty(tenantGroupSetting) ? tenantGroupSetting.Split(",").ToList() : new List<string>();
            if (!groups.Any())
            {
                Log.Error("Chưa có cấu hình TenantGroupsPrivate");
                return;
            }

            var tenant = await GetTenantAsync(sellerTaxCode);
            var group = tenant.Group;

            var tenantGroupIndexEs = "group-x";
            var tenantGroup = group.ToString().Replace(",", ".");
            if (groups.Contains(tenantGroup))
                tenantGroupIndexEs = $"group-{tenantGroup}";

            var client = _elasticSearch.Client("invoice01", tenantGroupIndexEs);
            try
            {
                var res = await client.UpdateAsync<object>(id, u => u
                        .Script(s => s
                            .Source($"ctx._source.{nameof(Invoice01HeaderEsDto.VerificationCode).FirstCharToLowerCase()} = params.{nameof(Invoice01HeaderEsDto.VerificationCode).FirstCharToLowerCase()};")
                            .Params(p => p
                                .Add($"{nameof(Invoice01HeaderEsDto.VerificationCode).FirstCharToLowerCase()}", verificationCode)
                            )
                        )
                    );

                if (res.IsValid)
                {
                    await _mongoInvoice01Repository.UpdateSyncVerificationCodeToEsAsync(id, verificationCode, (short)TvanStatus.TCTAccept.GetHashCode(), (short)SyncElasticSearchStatus.Synced.GetHashCode());
                    Log.Information(@$"RESPONSE UPDATE ES RESPONSE TVAN 202: Id: {id} - {res.Index} - {res.Result} - {res.OriginalException} - {res.ServerError} - {res.DebugInformation}");
                }
                else
                {
                    await _mongoInvoice01Repository.UpdateSyncVerificationCodeToEsAsync(id, verificationCode, (short)TvanStatus.TCTAccept.GetHashCode(), (short)SyncElasticSearchStatus.PendingSyncStatusTvanResponse.GetHashCode());
                    Log.Error(@$"RESPONSE UPDATE ES RESPONSE TVAN 202: Id: {id} - {res.Index} - {res.Result} - {res.OriginalException} - {res.ServerError} - {res.DebugInformation}");
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
            }
        }

        private async Task<Tenant> GetTenantAsync(string taxcode)
        {
            var config = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<VnisTenantModel, Tenant>().ReverseMap();
            });

            var mapper = new Mapper(config);

            var sql = @$"SELECT ""Id"", ""TaxCode"", ""Name"", ""Group"" FROM ""VnisTenants"" WHERE ""TaxCode"" = '{taxcode}' AND ""IsDeleted"" = 0";

            var tenantDtos = (await _appFactory.AuthDatabase.Connection.QueryAsync<VnisTenantModel>(sql)).FirstOrDefault();
            if (tenantDtos == null)
                return null;

            return mapper.Map<VnisTenantModel, Tenant>(tenantDtos);
        }
    }
}
