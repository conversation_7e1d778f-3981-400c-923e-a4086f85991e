using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Serilog;
using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Threading.Tasks;
using VaultSharp;
using VaultSharp.V1.AuthMethods;
using VaultSharp.V1.AuthMethods.Token;

namespace Core.VaultSharp
{
    public class VaultConfigurationProvider : ConfigurationProvider
    {
        public VaultOptions _config;

        public VaultConfigurationProvider(
            VaultOptions config)
        {
            _config = config;
        }

        public override void Load()
        {
            LoadAsync().Wait();
        }

        public async Task LoadAsync()
        {
            if (string.IsNullOrEmpty(_config.Address) || string.IsNullOrEmpty(_config.Secret))
            {
                Log.Error("Chưa có cấu hình Address hoặc Secret Vault!");
                return;
            }

            #region read config v2
            IAuthMethodInfo authMethod = new TokenAuthMethodInfo(_config.Secret);
            var vaultClientSettings = new VaultClientSettings(_config.Address, authMethod);
            IVaultClient vaultClient = new VaultClient(vaultClientSettings);

            var secretsCommon = await vaultClient.V1.Secrets.KeyValue.V2.ReadSecretAsync(_config.MountPathCommon, null, _config.MountPoint);
            var secretsMicroservice = await vaultClient.V1.Secrets.KeyValue.V2.ReadSecretAsync(_config.MountPathMicroService, null, _config.MountPoint);

            var flattenedDictionary = new Dictionary<string, string>();

            // lấy thông tin chung appsetting
            foreach (var kvp in secretsCommon.Data.Data)
            {
                // Chuyển đổi object thành chuỗi JSON
                var data = JsonConvert.SerializeObject(kvp.Value);
                if (VaultExtensions.IsJson(data))
                {
                    // Phân tích cú pháp JSON thành JsonDocument
                    using (JsonDocument document = JsonDocument.Parse(data))
                    {
                        // Bắt đầu chuyển đổi từ gốc của JSON object
                        VaultExtensions.FlattenJson(kvp.Key, document.RootElement, flattenedDictionary);
                    }
                }
                else
                {
                    // Nếu không phải là JSON, xử lý chuỗi như một giá trị thông thường
                    flattenedDictionary[kvp.Key] = data;
                }
            }

            // lấy thông tin microservice appsetting
            foreach (var kvp in secretsMicroservice.Data.Data)
            {
                // Chuyển đổi object thành chuỗi JSON
                var data = JsonConvert.SerializeObject(kvp.Value);
                if (VaultExtensions.IsJson(data))
                {
                    // Phân tích cú pháp JSON thành JsonDocument
                    using (JsonDocument document = JsonDocument.Parse(data))
                    {
                        // Bắt đầu chuyển đổi từ gốc của JSON object
                        VaultExtensions.FlattenJson(kvp.Key, document.RootElement, flattenedDictionary);
                    }
                }
                else
                {
                    // Nếu không phải là JSON, xử lý chuỗi như một giá trị thông thường
                    flattenedDictionary[kvp.Key] = data;
                }
            }

            foreach (var kvp in flattenedDictionary)
            {
                Data.Add(kvp.Key, kvp.Value);
            }
            #endregion

            #region read config v1
            //try
            //{
            //    IAuthMethodInfo authMethod = new TokenAuthMethodInfo(_config.Secret);
            //    var vaultClientSettings = new VaultClientSettings(_config.Address, authMethod);
            //    IVaultClient vaultClient = new VaultClient(vaultClientSettings);

            //    // lấy cấu hình chung
            //    var kv2SecretCommon = await vaultClient.V1.Secrets.KeyValue.V2.ReadSecretAsync(_config.MountPathCommon, null, _config.MountPoint);
            //    if (kv2SecretCommon.Data.Data.Any())
            //    {
            //        foreach (var kvp in kv2SecretCommon.Data.Data)
            //        {
            //            Data.Add(kvp.Key, kvp.Value?.ToString());
            //        }
            //    }

            //    // lấy cấu hình cho microservice
            //    var kv2SecretMicroService = await vaultClient.V1.Secrets.KeyValue.V2.ReadSecretAsync(_config.MountPathMicroService, null, _config.MountPoint);
            //    if (kv2SecretMicroService.Data.Data.Any())
            //    {
            //        foreach (var kvp in kv2SecretMicroService.Data.Data)
            //        {
            //            Data.Add(kvp.Key, kvp.Value?.ToString());
            //        }
            //    }

            //}
            //catch (Exception ex)
            //{
            //    Log.Error(ex, ex.Message);
            //}
            #endregion
        }
    }

    public class VaultConfigurationSource : IConfigurationSource
    {
        private VaultOptions _config;

        public VaultConfigurationSource(Action<VaultOptions> config)
        {
            _config = new VaultOptions();
            config.Invoke(_config);
        }

        public IConfigurationProvider Build(IConfigurationBuilder builder)
        {
            return new VaultConfigurationProvider(_config);
        }
    }
}
