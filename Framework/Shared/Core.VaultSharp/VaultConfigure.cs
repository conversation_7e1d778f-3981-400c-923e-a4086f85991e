using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;

namespace Core.VaultSharp
{
    public class VaultConfigure
    {
        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureAppConfiguration((hostingContext, config) =>
                {
                    config.SetBasePath(hostingContext.HostingEnvironment.ContentRootPath);
                    config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true); // load appsetting để lấy cấu hình vault
                    config.AddJsonFile("api-gateway-configuration.json", optional: true, reloadOnChange: true);
                    config.AddEnvironmentVariables(prefix: "VAULT_");

                    // lấy cấu hình từ vault
                    var builtConfig = config.Build();
                    if (builtConfig.GetSection("Vault") != null)
                    {
                        config.AddVault(options =>
                        {
                            var vaultOptions = builtConfig.GetSection("Vault");
                            options.Address = vaultOptions["Address"];
                            options.Role = vaultOptions["Role"];
                            options.MountPathCommon = vaultOptions["MountPathCommon"];
                            options.MountPathMicroService = vaultOptions["MountPathMicroService"];
                            options.SecretType = vaultOptions["SecretType"];
                            options.Secret = vaultOptions["Secret"];
                            options.MountPoint = vaultOptions["MountPoint"];
                        });
                    }

                    // môi trường develop thì có thể custome để override lại giá trị lấy từ vault
                    config.AddJsonFile($"appsettings.{hostingContext.HostingEnvironment.EnvironmentName}.json", true, true);
                    config.AddJsonFile($"api-gateway-configuration.{hostingContext.HostingEnvironment.EnvironmentName}.json", true, true);
                });
    }
}
