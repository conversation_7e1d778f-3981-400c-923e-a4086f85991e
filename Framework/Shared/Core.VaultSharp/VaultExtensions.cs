using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Text.Json;

namespace Core.VaultSharp
{
    public static class VaultExtensions
    {
        public static IConfigurationBuilder AddVault(this IConfigurationBuilder configuration, Action<VaultOptions> options)
        {
            var vaultOptions = new VaultConfigurationSource(options);
            configuration.Add(vaultOptions);
            return configuration;
        }

        public static bool IsJson(string input)
        {
            input = input.Trim();
            if ((input.StartsWith("{") && input.EndsWith("}")) || // <PERSON><PERSON><PERSON> tượng JSON
                (input.StartsWith("[") && input.EndsWith("]")))   // Mảng JSON
            {
                try
                {
                    using (JsonDocument doc = JsonDocument.Parse(input))
                    {
                        return true;
                    }
                }
                catch (JsonException)
                {
                    return false;
                }
            }
            return false;
        }

        // Hàm đệ quy để chuyển đổi các key
        public static void FlattenJson(string prefix, JsonElement element, Dictionary<string, string> dictionary)
        {
            switch (element.ValueKind)
            {
                case JsonValueKind.Object:
                    foreach (JsonProperty property in element.EnumerateObject())
                    {
                        var key = string.IsNullOrEmpty(prefix) ? property.Name : $"{prefix}:{property.Name}";
                        FlattenJson(key, property.Value, dictionary);
                    }
                    break;
                case JsonValueKind.Array:
                    int index = 0;
                    foreach (JsonElement arrayElement in element.EnumerateArray())
                    {
                        var key = $"{prefix}:{index}";
                        FlattenJson(key, arrayElement, dictionary);
                        index++;
                    }
                    break;
                default:
                    dictionary[prefix] = element.ToString();
                    break;
            }
        }
    }
}
