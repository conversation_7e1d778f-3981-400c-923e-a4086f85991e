using Core.Modularity;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Serilog;
using Serilog.Events;
using System;

namespace Core.VaultSharp
{
    public class VaultSharpModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            var configuration = context.Services.GetConfiguration();
            ConfigureLogging(configuration);
        }

        private static void ConfigureLogging(IConfiguration configuration)
        {
           var folder = string.IsNullOrEmpty(configuration["Logging:RootFolder:Folder"]) ? $"/{configuration["Service:BaseUrl"]}"
                : configuration["Logging:RootFolder:Folder"] + $"/{configuration["Service:BaseUrl"]}";

            int timeFlushToDiskInterval = 0;
            int retainedFileCountLimit = 0;
            long fileSizeLimitBytes = 0;

            int.TryParse(configuration["Settings:TimeFlushToDiskInterval"], out timeFlushToDiskInterval);
            int.TryParse(configuration["Settings:RetainedFileCountLimit"], out retainedFileCountLimit);
            long.TryParse(configuration["Settings:FileSizeLimitBytes"], out fileSizeLimitBytes);

            Log.Logger = new LoggerConfiguration()
                .MinimumLevel.Debug()
                .MinimumLevel.Information()
                .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
                .MinimumLevel.Override("Microsoft.EntityFrameworkCore", LogEventLevel.Warning)
                .Enrich.FromLogContext()
                .Enrich.WithCorrelationId()
                .WriteTo.PersistentFile($"{folder}" + "/log.txt",
                                        persistentFileRollingInterval: PersistentFileRollingInterval.Day,
                                        fileSizeLimitBytes: fileSizeLimitBytes != 0 ? fileSizeLimitBytes : 52428800, //Bytes <=> 50 MB: mặc đinh là 50MB
                                        retainedFileCountLimit: retainedFileCountLimit != 0 ? retainedFileCountLimit : null,
                                        preserveLogFilename: true,
                                        rollOnFileSizeLimit: true,
                                        shared: true,
                                        flushToDiskInterval: timeFlushToDiskInterval != 0 ? TimeSpan.FromMilliseconds(timeFlushToDiskInterval) : null,
                                        outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] [{CorrelationId}] {Message:lj}{NewLine}{Exception}"
                                        )
                .CreateLogger();
        }
    }
}
