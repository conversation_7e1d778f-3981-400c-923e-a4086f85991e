using Core.Shared.Constants;
using Core.Shared.ElasticSearch;
using Core.Shared.ElasticSearch.Dto.Invoice02;
using Core.Shared.Extensions;
using Core.Shared.Factory;

using Elasticsearch.Net;

using Microsoft.Extensions.Configuration;

using Nest;

using Serilog;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using VnisCore.Core.MongoDB.IRepository.Invoices.Invoice02;

namespace ReSyncInvoiceToEs.Business
{
    public interface IResyncApproveStatusInvoice02Business
    {
        Task ResyncApproveStatusInvoice02Async(string group);
    }

    public class ResyncApproveStatusInvoice02Business : IResyncApproveStatusInvoice02Business
    {
        private readonly ElasticSearch _elasticSearch;
        private readonly IConfiguration _configuration;
        private readonly IAppFactory _appFactory;
        private readonly IVnisCoreMongoInvoice02Repository _mongoInvoice02Repository;

        public ResyncApproveStatusInvoice02Business(
            ElasticSearch elasticSearch,
            IConfiguration configuration,
            IAppFactory appFactory,
            IVnisCoreMongoInvoice02Repository mongoInvoice02Repository)
        {
            _elasticSearch = elasticSearch;
            _configuration = configuration;
            _appFactory = appFactory;
            _mongoInvoice02Repository = mongoInvoice02Repository;
        }

        public async Task ResyncApproveStatusInvoice02Async(string group)
        {
            try
            {
                var tenantGroupIndexEs = group;
                var client = _elasticSearch.Client("invoice02", tenantGroupIndexEs);

                var createFromDate = @$"{DateTime.Now.Year}{DateTime.Now.Month.ToString("00")}{DateTime.Now.Day.ToString("00")}";
                var createToDate = @$"{DateTime.Now.Year}{DateTime.Now.Month.ToString("00")}{DateTime.Now.Day.ToString("00")}";

                if (string.IsNullOrEmpty(createFromDate) || string.IsNullOrEmpty(createToDate))
                    return;

                var maxResultCount = 1000;
                var maxResultCountSetting = _configuration["Settings:MaxResultCount"];
                if (!string.IsNullOrEmpty(maxResultCountSetting))
                    maxResultCount = int.Parse(maxResultCountSetting);

                var filters = new List<Func<QueryContainerDescriptor<Invoice02HeaderEsDto>, QueryContainer>>();

                if (!string.IsNullOrEmpty(createFromDate))
                    filters.Add(f => f.Range(t => t.Field(f => f.InvoiceDateNumber).GreaterThanOrEquals(int.Parse(createFromDate))));

                if (!string.IsNullOrEmpty(createToDate))
                    filters.Add(f => f.Range(t => t.Field(f => f.InvoiceDateNumber).LessThanOrEquals(int.Parse(createToDate))));

                filters.Add(f => f.Terms(t => t.Field(f => f.ApproveStatus).Terms(1)));
                filters.Add(f => f.Exists(t => t.Field(f => f.VerificationCode)));

                #region code đồng bộ lại trạng thái duyệt ký
                var searchResponse = await client.SearchAsync<Invoice02HeaderEsDto>(s => s
               .Size(maxResultCount)
               .Query(q => q
                   .Bool(b => b
                       .Filter(filters)
                   )));

                var cou = searchResponse.Documents.ToList().Count();
                if (cou > 0)
                {
                    var data = searchResponse.Documents.ToList();
                    var bulkResponse = await client.BulkAsync(b => b
                        .UpdateMany(data, (descriptor, update) => descriptor
                            .Id(update.Id)
                            .Script(s => s
                                .Source($"ctx._source.{nameof(Invoice02HeaderEsDto.ApproveStatus).FirstCharToLowerCase()} = params.{nameof(Invoice02HeaderEsDto.ApproveStatus).FirstCharToLowerCase()};")
                                .Params(p => p
                                    .Add($"{nameof(Invoice02HeaderEsDto.ApproveStatus).FirstCharToLowerCase()}", 2)
                                )
                            )
                        ).Refresh(Refresh.True));

                    await _mongoInvoice02Repository.UpdateApproveStatusAsync(data.Select(x => x.Id).ToList(), (short)ApproveStatus.DaDuyet, (short)SyncElasticSearchStatus.Synced.GetHashCode());

                    Log.Information(@$"RESPONSE UPDATE ES APPROVE SIGN STATUS: {bulkResponse.Errors} - {bulkResponse.DebugInformation} - {bulkResponse.ItemsWithErrors} - {bulkResponse.OriginalException}");
                }

                #endregion
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
                throw;
            }
        }
    }
}
