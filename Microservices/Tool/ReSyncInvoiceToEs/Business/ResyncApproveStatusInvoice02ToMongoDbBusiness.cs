using Core.Shared.Constants;
using Core.Shared.ElasticSearch;
using Core.Shared.ElasticSearch.Dto.Invoice02;
using Core.Shared.Extensions;

using Microsoft.Extensions.Configuration;

using Nest;

using Serilog;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using VnisCore.Core.MongoDB.IRepository.Invoices.Invoice02;

namespace ReSyncInvoiceToEs.Business
{
    public interface IResyncApproveStatusInvoice02ToMongoDbBusiness
    {
        Task ResyncApproveStatusInvoice02ToMongoDBAsync(string group);
    }

    public class ResyncApproveStatusInvoice02ToMongoDbBusiness : IResyncApproveStatusInvoice02ToMongoDbBusiness
    {
        private readonly ElasticSearch _elasticSearch;
        private readonly IConfiguration _configuration;
        private readonly IVnisCoreMongoInvoice02Repository _mongoInvoice02Repository;

        public ResyncApproveStatusInvoice02ToMongoDbBusiness(
            ElasticSearch elasticSearch,
            IConfiguration configuration,
            IVnisCoreMongoInvoice02Repository mongoInvoice02Repository)
        {
            _elasticSearch = elasticSearch;
            _configuration = configuration;
            _mongoInvoice02Repository = mongoInvoice02Repository;
        }

        public async Task ResyncApproveStatusInvoice02ToMongoDBAsync(string group)
        {
            try
            {
                var tenantGroupIndexEs = group;
                var client = _elasticSearch.Client("invoice02", tenantGroupIndexEs);

                var numberPreviousDay = 3;
                var numberPreviousDaySetting = _configuration["Settings:NumberPreviousDay"];
                if (!string.IsNullOrEmpty(numberPreviousDaySetting))
                    numberPreviousDay = short.Parse(numberPreviousDaySetting);

                var today = DateTime.Today;
                var previousDay = today.AddDays(-numberPreviousDay);

                var createToDate = @$"{today.Year}{today.Month.ToString("00")}{today.Day.ToString("00")}";
                var createFromDate = @$"{previousDay.Year}{previousDay.Month.ToString("00")}{previousDay.Day.ToString("00")}";

                if (string.IsNullOrEmpty(createFromDate) || string.IsNullOrEmpty(createToDate))
                    return;

                var maxResultCount = 1000;
                var maxResultCountSetting = _configuration["Settings:MaxResultCount"];
                if (!string.IsNullOrEmpty(maxResultCountSetting))
                    maxResultCount = int.Parse(maxResultCountSetting);

                var filters = new List<Func<QueryContainerDescriptor<Invoice02HeaderEsDto>, QueryContainer>>();

                if (!string.IsNullOrEmpty(createFromDate))
                    filters.Add(f => f.Range(t => t.Field(f => f.InvoiceDateNumber).GreaterThanOrEquals(int.Parse(createFromDate))));

                if (!string.IsNullOrEmpty(createToDate))
                    filters.Add(f => f.Range(t => t.Field(f => f.InvoiceDateNumber).LessThanOrEquals(int.Parse(createToDate))));

                filters.Add(f => f.Terms(t => t.Field(f => f.ApproveStatus).Terms(2)));
                filters.Add(f => !f.Exists(t => t.Field(f => f.VerificationCode)));

                #region code đồng bộ lại trạng thái duyệt ký
                var searchResponse = await client.SearchAsync<Invoice02HeaderEsDto>(s => s
               .Size(maxResultCount)
               .Query(q => q
                   .Bool(b => b
                       .Filter(filters)
                   )));

                var cou = searchResponse.Documents.ToList().Count();
                if (cou > 0)
                {
                    var data = searchResponse.Documents.ToList();

                    var maxSelect = 500;
                    var maxSelectConfig = _configuration["Settings:MaxSelect"];
                    if (!string.IsNullOrEmpty(maxSelectConfig))
                        maxSelect = int.Parse(maxSelectConfig);

                    if (cou > maxSelect)
                    {
                        var numberOfTimes = cou / maxSelect;
                        if (numberOfTimes * maxSelect < cou)
                            numberOfTimes += 1;

                        var idsInvoice = data.Select(x => x.Id).ToList();

                        for (int i = 0; i < numberOfTimes; i++)
                        {
                            var ids = idsInvoice.Skip(i * maxSelect).Take(maxSelect).ToList();

                            var invoice02Mongos = await _mongoInvoice02Repository.GetByIdsAndApproveStatusAsync(ids, (short)ApproveStatus.ChoDuyet);
                            if (invoice02Mongos.Any())
                            {
                                await _mongoInvoice02Repository.UpdateApproveStatusAsync(ids, (short)ApproveStatus.DaDuyet, (short)SyncElasticSearchStatus.Synced.GetHashCode());

                                Log.Information(@$"RESPONSE UPDATE APPROVE SIGN TO MONGO: {string.Join(",", ids)}");
                            }
                        }    
                    }
                    else
                    {
                        var invoice02Mongos = await _mongoInvoice02Repository.GetByIdsAndApproveStatusAsync(data.Select(x => x.Id).ToList(), (short)ApproveStatus.ChoDuyet);
                        if (invoice02Mongos.Any())
                        {
                            await _mongoInvoice02Repository.UpdateApproveStatusAsync(invoice02Mongos.Select(x => x.Id).ToList(), (short)ApproveStatus.DaDuyet, (short)SyncElasticSearchStatus.Synced.GetHashCode());

                            Log.Information(@$"RESPONSE UPDATE APPROVE SIGN TO MONGO: {string.Join(",", invoice02Mongos.Select(x => x.Id).ToList())}");
                        }
                    }
                }

                #endregion
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
                throw;
            }
        }
    }
}
