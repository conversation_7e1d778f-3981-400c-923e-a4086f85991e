using AutoMapper;

using Core.Shared.Constants;
using Core.Shared.ElasticSearch;
using Core.Shared.ElasticSearch.Dto.Invoice01;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.TenantManagement;

using Dapper;

using Microsoft.Extensions.Configuration;

using Nest;

using ReSyncInvoiceToEs.Dtos;

using Serilog;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.MongoDB.IRepository;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;

using static Stimulsoft.Report.StiOptions.Export;

namespace ReSyncInvoiceToEs.Business
{
    public interface IResyncEsInvoice01Business
    {
        Task ResyncInvoice01Async();
    }

    public class ResyncEsInvoice01Business : IResyncEsInvoice01Business
    {
        private readonly ElasticSearch _elasticSearch;
        private readonly IConfiguration _configuration;
        private readonly IAppFactory _appFactory;
        private readonly IVnisCoreMongoInvoice01Repository _mongoInvoice01Repository;

        public ResyncEsInvoice01Business(
            ElasticSearch elasticSearch,
            IConfiguration configuration,
            IAppFactory appFactory,
            IVnisCoreMongoInvoice01Repository mongoInvoice01Repository)
        {
            _elasticSearch = elasticSearch;
            _configuration = configuration;
            _appFactory = appFactory;
            _mongoInvoice01Repository = mongoInvoice01Repository;
        }

        public async Task ResyncInvoice01Async()
        {
            try
            {
                var taxCode = _configuration["Settings:TaxCode"];
                if (string.IsNullOrEmpty(taxCode))
                    return;

                var tenantGroupSetting = _configuration["Settings:TenantGroupsPrivate"];

                var groups = !string.IsNullOrEmpty(tenantGroupSetting) ? tenantGroupSetting.Split(",").ToList() : new List<string>();
                if (!groups.Any())
                {
                    Log.Error("Chưa có cấu hình TenantGroupsPrivate");
                    return;
                }

                var vnisTenant = await GetTenantInfoAsync(taxCode);
                if (vnisTenant == null)
                    return;

                var group = vnisTenant.Group;

                var tenantGroupIndexEs = "group-x";
                var tenantGroup = group.ToString().Replace(",", ".");
                if (groups.Contains(tenantGroup))
                    tenantGroupIndexEs = $"group-{tenantGroup}";

                var client = _elasticSearch.Client("invoice01", tenantGroupIndexEs);

                var createFromDate = _configuration["Settings:CreateFromDate"];
                var createToDate = _configuration["Settings:CreateToDate"];
                var advancedSearchValue = _configuration["Settings:AdvancedSearchValue"];
                var advancedSearchName = _configuration["Settings:AdvancedSearchName"];

                if (string.IsNullOrEmpty(createFromDate) || string.IsNullOrEmpty(createToDate))
                    return;

                var maxResultCount = 10000;
                var maxResultCountSetting = _configuration["Settings:MaxResultCount"];
                if (!string.IsNullOrEmpty(maxResultCountSetting))
                    maxResultCount = int.Parse(maxResultCountSetting);

                var filters = new List<Func<QueryContainerDescriptor<Invoice01HeaderEsDto>, QueryContainer>>();

                if (!string.IsNullOrEmpty(createFromDate))
                    filters.Add(f => f.Range(t => t.Field(f => f.InvoiceDateNumber).GreaterThanOrEquals(int.Parse(createFromDate))));

                if (!string.IsNullOrEmpty(createToDate))
                    filters.Add(f => f.Range(t => t.Field(f => f.InvoiceDateNumber).LessThanOrEquals(int.Parse(createToDate))));

                if (!string.IsNullOrEmpty(advancedSearchName) && !string.IsNullOrEmpty(advancedSearchValue))
                {
                    filters.Add(f => f.MatchPhrase(t => t.Field(f => f.InvoiceHeaderExtras.Select(x => x.FieldName)).Query(advancedSearchName.ToLower())) &&
                    f.MatchPhrase(t => t.Field(f => f.InvoiceHeaderExtras.Select(x => x.FieldValue)).Query(advancedSearchValue.ToLower())));
                }

                //filters.Add(f => f.Terms(t => t.Field(f => f.SignStatus).Terms(3)));
                filters.Add(f => !f.Exists(t => t.Field(f => f.VerificationCode)));

                #region code đồng bộ lại trạng thái ký lên ES
                //var searchResponse = await client.SearchAsync<Invoice01HeaderEsDto>(s => s
                //.Size(maxResultCount)
                //.Query(q => q
                //    .Bool(b => b
                //        .Must(
                //            bm => bm.MatchPhrase(m => m.Field(f => f.TenantId).Query(vnisTenant.Id.ToString().ToLower()))
                //        )
                //        .Filter(filters)
                //    ))
                //.Sort(s => s.Descending(f => f.Number))
                //);

                //var cou = searchResponse.Documents.ToList().Count();
                //if (cou > 0)
                //{
                //    var data = searchResponse.Documents.ToList();
                //    //var ids = data.Select(x => x.Id).ToList();

                //    var conn = _appFactory.VnisCoreOracle.Connection;
                //    conn.Open();
                //    conn.BeginTransaction();

                //    var query = $@"SELECT
                //                    ""Id"",
                //                ""Number"",
                //                ""SignStatus"",
                //                ""SellerSignedTime"",
                //                ""SellerSignedId"",
                //                ""SellerFullNameSigned""
                //                FROM
                //                    ""Invoice01Header""
                //                WHERE ""Id"" IN ({string.Join(", ", data.Select(x => $@"'{x.Id}'"))}) AND  ""SignStatus"" = 5 ";

                //    var invoiceHeader = (await conn.QueryAsync<Invoice01HeaderEntity>(query.ToString())).ToList();

                //    if (invoiceHeader.Any())
                //    {
                //        foreach (var item in invoiceHeader)
                //        {
                //            var res = await client.UpdateAsync<object>(item.Id, u => u
                //                    .Script(s => s
                //                        .Source($"ctx._source.{nameof(Invoice01HeaderEsDto.SignStatus).FirstCharToLowerCase()} = params.{nameof(Invoice01HeaderEsDto.SignStatus).FirstCharToLowerCase()}")
                //                        .Params(p => p
                //                            .Add($"{nameof(Invoice01HeaderEsDto.SignStatus).FirstCharToLowerCase()}", 5)
                //                        )
                //                    )
                //                );

                //            var invoice01Mongo = await _mongoInvoice01Repository.GetById(item.Id);
                //            if (invoice01Mongo != null)
                //            {
                //                invoice01Mongo.SignStatus = 5;
                //                invoice01Mongo.SellerSignedTime = item.SellerSignedTime;
                //                invoice01Mongo.SellerSignedId = item.SellerSignedId;
                //                invoice01Mongo.SellerFullNameSigned = item.SellerFullNameSigned;

                //                await _mongoInvoice01Repository.UpdateAsync(invoice01Mongo);
                //            }

                //            Log.Information(@$"UPDATE SIGN ES RESPONSE: Id: {item.Id} - " + res.IsValid.ToString() + "-" + res.Result + "-" + res.DebugInformation);
                //        }
                //    }

                //    conn.Close();
                //}

                #endregion

                #region code đồng bộ lại trạng thái mã CQT
                var searchResponse = await client.SearchAsync<Invoice01HeaderEsDto>(s => s
               .Size(maxResultCount)
               .Query(q => q
                   .Bool(b => b
                       .Must(
                           bm => bm.MatchPhrase(m => m.Field(f => f.TenantId).Query(vnisTenant.Id.ToString().ToLower()))
                       )
                       .Filter(filters)
                   ))
               .Sort(s => s.Descending(f => f.Number))
               );

                var cou = searchResponse.Documents.ToList().Count();
                if (cou > 0)
                {
                    var data = searchResponse.Documents.ToList();

                    var conn = _appFactory.VnisCoreOracle.Connection;
                    conn.Open();
                    conn.BeginTransaction();

                    var query = $@"SELECT
                                        ""Id"",
                                    ""VerificationCode""
                                    FROM
                                        ""Invoice01Header""
                                    WHERE ""Id"" IN ({string.Join(", ", data.Select(x => $@"'{x.Id}'"))}) AND  ""VerificationCode"" IS NOT NULL AND ""StatusTvan"" = 3 ";

                    var invoiceHeader = (await conn.QueryAsync<Invoice01HeaderEntity>(query.ToString())).ToList();

                    if (invoiceHeader.Any())
                    {
                        foreach (var item in invoiceHeader.ToList())
                        {
                            var res = await client.UpdateAsync<object>(item.Id, u => u
                                .Script(s => s
                                    .Source($"ctx._source.{nameof(Invoice01HeaderEsDto.VerificationCode).FirstCharToLowerCase()} = params.{nameof(Invoice01HeaderEsDto.VerificationCode).FirstCharToLowerCase()};")
                                    .Params(p => p
                                        .Add($"{nameof(Invoice01HeaderEsDto.VerificationCode).FirstCharToLowerCase()}", item.VerificationCode)
                                    )
                                )
                            );

                            var invoice01Mongos = await _mongoInvoice01Repository.GetById(item.Id);
                            if (invoice01Mongos != null)
                            {
                                invoice01Mongos.VerificationCode = item.VerificationCode;
                                invoice01Mongos.IsSyncVerificationCodeTocore = 1;

                                await _mongoInvoice01Repository.UpdateAsync(invoice01Mongos);
                            }

                            Log.Information(@$"UPDATE SIGN ES RESPONSE: Id: {item.Id} - " + res.IsValid.ToString() + "-" + res.Result + "-" + res.DebugInformation);
                        }
                    }

                    conn.Close();
                }

                #endregion

                #region old
                //var numberOfTimes = (c / maxResultCount) + 1;

                //var conn = _appFactory.VnisCoreOracle.Connection;
                //conn.Open();
                //conn.BeginTransaction();

                //for (int i = 0; i < numberOfTimes; i++)
                //{
                //    var skipCount = maxResultCount * i;
                //    var searchResponse = await client.SearchAsync<Invoice01HeaderEsDto>(s => s
                //    .Skip(skipCount)
                //   .Size(maxResultCount)
                //   .Query(q => q
                //       .Bool(b => b
                //           .Must(
                //               bm => bm.MatchPhrase(m => m.Field(f => f.TenantId).Query(vnisTenant.Id.ToString().ToLower()))
                //           )
                //           .Filter(filters)
                //       ))
                //   .Sort(s => s.Descending(f => f.Number))
                //   );

                //    if (!searchResponse.IsValid)
                //        return;

                //    var data = searchResponse.Documents.ToList();
                //    if (!data.Any())
                //        return;

                //    try
                //    {
                //        var query = $@"SELECT
                //             ""Id"",
                //                ""Number""
                //            FROM
                //             ""Invoice01Header""
                //            WHERE ""Id"" IN ({string.Join(", ", data.Select(x => $@"'{x.Id}'"))}) ";

                //        var invoiceHeader = (await conn.QueryAsync<Invoice01HeaderEntity>(query.ToString())).ToList();
                //        if (invoiceHeader.Any())
                //        {
                //            var excepts = (data.Select(x => x.Id).ToList()).Except(invoiceHeader.Select(x => x.Id).ToList()).ToList();
                //            if (excepts.Any())
                //            {
                //                var resss = await client.DeleteManyAsync<Invoice01HeaderEsDto>(excepts.Select(x => new Invoice01HeaderEsDto { Id = x }));
                //                Log.Information($@"*** Invoice Info: Id: {string.Join(", ", excepts.Select(x => $@"'{x}'"))}");
                //            }    

                //        }

                //    }
                //    catch (Exception ex)
                //    {
                //        Log.Error(ex, ex.Message);
                //        throw;
                //    }
                //    //}
                //}
                //conn.Close();
                #endregion

                Log.Information("Done");
            }
            catch (Exception ex)
            {

                throw;
            }
        }

        private async Task<Tenant> GetTenantInfoAsync(string taxCode)
        {
            var config = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<VnisTenantDto, Tenant>().ReverseMap();
            });

            var mapper = new Mapper(config);

            var sql = @$"SELECT * FROM ""VnisTenants"" WHERE ""TaxCode"" = '{taxCode}' AND ""IsDeleted"" = 0 FETCH FIRST 1 ROWS ONLY";

            var tenantDto = await _appFactory.AuthDatabase.Connection.QueryFirstOrDefaultAsync<VnisTenantDto>(@$"SELECT * FROM ""VnisTenants"" WHERE ""TaxCode"" = '{taxCode}' AND ""IsDeleted"" = 0 FETCH FIRST 1 ROWS ONLY");
            if (tenantDto == null)
                return null;

            return mapper.Map<VnisTenantDto, Tenant>(tenantDto);
        }
    }
}
