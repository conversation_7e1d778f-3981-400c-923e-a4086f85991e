using Core.Shared.Constants;
using Core.Shared.ElasticSearch;
using Core.Shared.ElasticSearch.Dto.Invoice02;
using Core.Shared.Extensions;
using Core.Shared.Factory;

using Dapper;

using Microsoft.Extensions.Configuration;

using Nest;

using Serilog;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using VnisCore.Core.MongoDB.IRepository.Invoices.Invoice02;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;

namespace ReSyncInvoiceToEs.Business
{
    public interface IResyncIsDeclaredInvoice02ToEsBusiness
    {
        Task ResyncIsDeclaredInvoice02ToEsAsync(string group);
    }


    public class ResyncIsDeclaredInvoice02ToEsBusiness : IResyncIsDeclaredInvoice02ToEsBusiness
    {
        private readonly ElasticSearch _elasticSearch;
        private readonly IConfiguration _configuration;
        private readonly IVnisCoreMongoInvoice02Repository _mongoInvoice02Repository;
        private readonly IAppFactory _appFactory;

        public ResyncIsDeclaredInvoice02ToEsBusiness(
            ElasticSearch elasticSearch,
            IConfiguration configuration,
            IVnisCoreMongoInvoice02Repository mongoInvoice02Repository,
            IAppFactory appFactory)
        {
            _elasticSearch = elasticSearch;
            _configuration = configuration;
            _mongoInvoice02Repository = mongoInvoice02Repository;
            _appFactory = appFactory;
        }

        public async Task ResyncIsDeclaredInvoice02ToEsAsync(string group)
        {
            try
            {
                var tenantGroupIndexEs = group;
                var client = _elasticSearch.Client("invoice02", tenantGroupIndexEs);

                var numberPreviousDay = 3;
                var numberPreviousDaySetting = _configuration["Settings:NumberPreviousDay"];
                if (!string.IsNullOrEmpty(numberPreviousDaySetting))
                    numberPreviousDay = short.Parse(numberPreviousDaySetting);

                var today = DateTime.Today;
                var previousDay = today.AddDays(-numberPreviousDay);

                var createToDate = @$"{today.Year}{today.Month.ToString("00")}{today.Day.ToString("00")}";
                var createFromDate = @$"{previousDay.Year}{previousDay.Month.ToString("00")}{previousDay.Day.ToString("00")}";

                if (string.IsNullOrEmpty(createFromDate) || string.IsNullOrEmpty(createToDate))
                    return;

                var maxResultCount = 1000;
                var maxResultCountSetting = _configuration["Settings:MaxResultCount"];
                if (!string.IsNullOrEmpty(maxResultCountSetting))
                    maxResultCount = int.Parse(maxResultCountSetting);

                var filters = new List<Func<QueryContainerDescriptor<Invoice02HeaderEsDto>, QueryContainer>>();
                var multiMatch = new List<Func<MultiMatchQueryDescriptor<Invoice02HeaderEsDto>, IMultiMatchQuery>>();

                if (!string.IsNullOrEmpty(createFromDate))
                    filters.Add(f => f.Range(t => t.Field(f => f.InvoiceDateNumber).GreaterThanOrEquals(int.Parse(createFromDate))));

                if (!string.IsNullOrEmpty(createToDate))
                    filters.Add(f => f.Range(t => t.Field(f => f.InvoiceDateNumber).LessThanOrEquals(int.Parse(createToDate))));

                filters.Add(f => f.Terms(t => t.Field(f => f.SignStatus).Terms((short)SignStatus.ChoKy)));
                filters.Add(f => f.Terms(t => t.Field(f => f.IsDeclared).Terms(true)));

                multiMatch.Add(m => m.Fields(f => f
                   .Field(p => p.SerialNo)
                   )
                    .Query("K23")
                    .Type(TextQueryType.BoolPrefix)
                    .Operator(Operator.And)
                   );

                #region code đồng bộ lại trạng thái mã CQT
                var searchResponse = await client.SearchAsync<Invoice02HeaderEsDto>(s => s
               .Size(maxResultCount)
               .Query(q => q
                   .Bool(b => b
                         .Must(bm => bm.MultiMatch(multiMatch.FirstOrDefault()))
                        .Filter(filters)
                   ))
               );

                var cou = searchResponse.Documents.ToList().Count();
                if (cou > 0)
                {
                    var data = searchResponse.Documents.ToList();

                    var conn = _appFactory.VnisCoreOracle.Connection;
                    conn.Open();
                    conn.BeginTransaction();

                    var query = $@"SELECT
                                        ""Id"",
                                    ""SignStatus""
                                    FROM
                                        ""Invoice02Header""
                                    WHERE ""Id"" IN ({string.Join(", ", data.Select(x => $@"'{x.Id}'"))})";

                    var invoiceHeader = (await conn.QueryAsync<Invoice02HeaderEntity>(query.ToString())).ToList();

                    if (invoiceHeader.Any())
                    {
                        foreach (var item in invoiceHeader.ToList())
                        {
                            var res = await client.UpdateAsync<object>(item.Id, u => u
                                .Script(s => s
                                    .Source($"ctx._source.{nameof(Invoice02HeaderEsDto.SignStatus).FirstCharToLowerCase()} = params.{nameof(Invoice02HeaderEsDto.SignStatus).FirstCharToLowerCase()};" 
                                    )
                                    .Params(p => p
                                        .Add($"{nameof(Invoice02HeaderEsDto.SignStatus).FirstCharToLowerCase()}", item.SignStatus)
                                    )
                                )
                            );

                            var invoiceMongos = await _mongoInvoice02Repository.GetById(item.Id);
                            if (invoiceMongos != null)
                            {
                                invoiceMongos.SignStatus = item.SignStatus;
                                invoiceMongos.IsSyncedToElasticSearch = 1;

                                await _mongoInvoice02Repository.UpdateAsync(invoiceMongos);
                            }

                            Log.Information(@$"UPDATE IsDeclared ES RESPONSE: Id: {item.Id} - " + res.IsValid.ToString() + "-" + res.Result + "-" + res.DebugInformation);
                        }
                    }

                    conn.Close();
                }

                #endregion

                Log.Information("Done IsDecrared");
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
            }
        }
    }
}
