using Core.Shared.Constants;
using Core.Shared.ElasticSearch;
using Core.Shared.ElasticSearch.Dto.Invoice01;
using Core.Shared.Extensions;
using Core.Shared.Factory;

using Dapper;

using Microsoft.Extensions.Configuration;

using Nest;

using Serilog;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using VnisCore.Core.MongoDB.IRepository;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;

namespace ReSyncInvoiceToEs.Business
{
    public interface IResyncVerificationCodeInvoice01Business
    {
        Task ResyncVerificationAsync(string group);
    }

    public class ResyncVerificationCodeInvoice01Business : IResyncVerificationCodeInvoice01Business
    {
        private readonly ElasticSearch _elasticSearch;
        private readonly IConfiguration _configuration;
        private readonly IVnisCoreMongoInvoice01Repository _mongoInvoice01Repository;
        private readonly IAppFactory _appFactory;

        public ResyncVerificationCodeInvoice01Business(
            ElasticSearch elasticSearch,
            IConfiguration configuration,
            IVnisCoreMongoInvoice01Repository mongoInvoice01Repository,
             IAppFactory appFactory)
        {
            _elasticSearch = elasticSearch;
            _configuration = configuration;
            _mongoInvoice01Repository = mongoInvoice01Repository;
            _appFactory = appFactory;
        }

        public async Task ResyncVerificationAsync(string group)
        {
            try
            {
                var tenantGroupIndexEs = group;
                var client = _elasticSearch.Client("invoice01", tenantGroupIndexEs);

                var numberPreviousDay = 3;
                var numberPreviousDaySetting = _configuration["Settings:NumberPreviousDay"];
                if (!string.IsNullOrEmpty(numberPreviousDaySetting))
                    numberPreviousDay = short.Parse(numberPreviousDaySetting);

                var today = DateTime.Today;
                var previousDay = today.AddDays(-numberPreviousDay);

                var createToDate = @$"{today.Year}{today.Month.ToString("00")}{today.Day.ToString("00")}";
                var createFromDate = @$"{previousDay.Year}{previousDay.Month.ToString("00")}{previousDay.Day.ToString("00")}";

                if (string.IsNullOrEmpty(createFromDate) || string.IsNullOrEmpty(createToDate))
                    return;

                var maxResultCount = 1000;
                var maxResultCountSetting = _configuration["Settings:MaxResultCount"];
                if (!string.IsNullOrEmpty(maxResultCountSetting))
                    maxResultCount = int.Parse(maxResultCountSetting);

                var filters = new List<Func<QueryContainerDescriptor<Invoice01HeaderEsDto>, QueryContainer>>();
                var multiMatch = new List<Func<MultiMatchQueryDescriptor<Invoice01HeaderEsDto>, IMultiMatchQuery>>();

                if (!string.IsNullOrEmpty(createFromDate))
                    filters.Add(f => f.Range(t => t.Field(f => f.InvoiceDateNumber).GreaterThanOrEquals(int.Parse(createFromDate))));

                if (!string.IsNullOrEmpty(createToDate))
                    filters.Add(f => f.Range(t => t.Field(f => f.InvoiceDateNumber).LessThanOrEquals(int.Parse(createToDate))));

                filters.Add(f => f.Terms(t => t.Field(f => f.SignStatus).Terms((short)SignStatus.DaKy)));
                filters.Add(f => !f.Exists(t => t.Field(f => f.VerificationCode)));

                multiMatch.Add(m => m.Fields(f => f
                   .Field(p => p.SerialNo)
                   )
                    .Query("C23")
                    .Type(TextQueryType.BoolPrefix)
                    .Operator(Operator.And)
                   );

                #region code đồng bộ lại trạng thái mã CQT
                var searchResponse = await client.SearchAsync<Invoice01HeaderEsDto>(s => s
               .Size(maxResultCount)
               .Query(q => q
                   .Bool(b => b
                         .Must(bm => bm.MultiMatch(multiMatch.FirstOrDefault()))
                        .Filter(filters)
                   ))
               );

                var cou = searchResponse.Documents.ToList().Count();
                if (cou > 0)
                {
                    var data = searchResponse.Documents.ToList();

                    var conn = _appFactory.VnisCoreOracle.Connection;
                    conn.Open();
                    conn.BeginTransaction();

                    var query = $@"SELECT
                                        ""Id"",
                                    ""VerificationCode"",
                                    ""ApproveStatus""
                                    FROM
                                        ""Invoice01Header""
                                    WHERE ""Id"" IN ({string.Join(", ", data.Select(x => $@"'{x.Id}'"))}) AND ""StatusTvan"" = 3 AND ""VerificationCode"" IS NOT NULL";

                    var invoiceHeader = (await conn.QueryAsync<Invoice01HeaderEntity>(query.ToString())).ToList();

                    if (invoiceHeader.Any())
                    {
                        foreach (var item in invoiceHeader.ToList())
                        {
                            var res = await client.UpdateAsync<object>(item.Id, u => u
                                .Script(s => s
                                    .Source($"ctx._source.{nameof(Invoice01HeaderEsDto.VerificationCode).FirstCharToLowerCase()} = params.{nameof(Invoice01HeaderEsDto.VerificationCode).FirstCharToLowerCase()};" +
                                            $"ctx._source.{nameof(Invoice01HeaderEsDto.ApproveStatus).FirstCharToLowerCase()} = params.{nameof(Invoice01HeaderEsDto.ApproveStatus).FirstCharToLowerCase()};"
                                    )
                                    .Params(p => p
                                        .Add($"{nameof(Invoice01HeaderEsDto.VerificationCode).FirstCharToLowerCase()}", item.VerificationCode)
                                        .Add($"{nameof(Invoice01HeaderEsDto.ApproveStatus).FirstCharToLowerCase()}", item.ApproveStatus)
                                    )
                                )
                            );

                            var invoice01Mongos = await _mongoInvoice01Repository.GetById(item.Id);
                            if (invoice01Mongos != null)
                            {
                                invoice01Mongos.VerificationCode = item.VerificationCode;
                                invoice01Mongos.IsSyncVerificationCodeTocore = 1;
                                invoice01Mongos.ApproveStatus = item.ApproveStatus;

                                await _mongoInvoice01Repository.UpdateAsync(invoice01Mongos);
                            }

                            Log.Information(@$"UPDATE VERIFICATIONCODE ES RESPONSE: Id: {item.Id} - " + res.IsValid.ToString() + "-" + res.Result + "-" + res.DebugInformation);
                        }
                    }

                    conn.Close();
                }

                #endregion

                Log.Information("Done");
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
            }
        }
    }
}
