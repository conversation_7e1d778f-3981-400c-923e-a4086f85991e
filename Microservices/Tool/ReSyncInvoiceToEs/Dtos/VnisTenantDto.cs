using System;
using System.Collections.Generic;

namespace ReSyncInvoiceToEs.Dtos
{
    public class VnisTenantDto
    {
        public Guid TenantId { get; set; }
        public string TenantCode { get; set; }
        public Guid Id { get; set; }
        public Guid? ParentId { get; set; }
        public string GroupCode { get; set; }
        public decimal Group { get; set; }
        public string Name { get; set; }
        public string Code { get; set; }
        public string Value { get; set; }
        public string ProviderName { get; set; }
        public string ProviderKey { get; set; }
        public string Options { get; set; }
        public int Type { get; set; }
        public string Description { get; set; }
        public string TaxCode { get; set; }
        public bool IsReadOnly { get; set; }

        public Dictionary<string, string> ExtraProperties { get; set; }
    }
}
