using AutoMapper;

using Core.Dto.Shared.Invoices.Invoice01;
using Core.Dto.Shared.Invoices.Invoice02;

using VnisCore.Core.MongoDB.Entities.Invoices.Invoice01;
using VnisCore.Core.MongoDB.Entities.Invoices.Invoice02;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;

namespace ReSyncInvoiceToEs
{
    public class ToolSyncOracleToMongoDbAutoMapperProfile : Profile
    {
        public ToolSyncOracleToMongoDbAutoMapperProfile()
        {
            CreateMap<Invoice01HeaderEntity, MongoInvoice01Entity>().ReverseMap();
            CreateMap<Invoice01DetailEntity, Invoice01DetailDto>().ReverseMap();
            CreateMap<Invoice01TaxBreakdownEntity, Invoice01TaxBreakdownDto>().ReverseMap();
            CreateMap<Invoice01ReferenceEntity, Invoice01ReferenceDto>().ReverseMap();
            CreateMap<Invoice01ReferenceOldDecreeEntity, Invoice01ReferenceOldDto>().ReverseMap();

            CreateMap<Invoice02HeaderEntity, MongoInvoice02Entity>().ReverseMap();
            CreateMap<Invoice02DetailEntity, Invoice02DetailDto>().ReverseMap();
            CreateMap<Invoice02ReferenceEntity, Invoice02ReferenceDto>().ReverseMap();
            CreateMap<Invoice02ReferenceOldDecreeEntity, Invoice02ReferenceOldDto>().ReverseMap();
           
        }
    }
}