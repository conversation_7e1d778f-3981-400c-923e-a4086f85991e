using System.Threading;
using System.Threading.Tasks;
using Core;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Serilog;

namespace ReSyncInvoiceToEs
{
    public class ToolSyncOracleToMongoDbHostedService : BackgroundService
    {
        protected override Task ExecuteAsync(CancellationToken stoppingToken)
        {
            using (var application = AbpApplicationFactory.Create<ToolSyncOracleToMongoDbModule>(options =>
            {
                options.UseAutofac();
                options.Services.AddLogging(c => c.AddSerilog());
            }))
            {
                application.Initialize();
            }

            return Task.CompletedTask;
        }
    }
}
