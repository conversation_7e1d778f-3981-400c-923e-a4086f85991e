using Core;
using Core.Application;
using Core.Autofac;
using Core.AutoMapper;
using Core.BackgroundWorkers;
using Core.Caching;
using Core.Caching.StackExchangeRedis;
using Core.Dapper;
using Core.Dto.Shared;
using Core.EntityFrameworkCore.Oracle;
using Core.Minify;
using Core.Modularity;
using Core.Shared;
using Core.Shared.DapperSqlTypeHandler;
using Core.Shared.ElasticSearch;
using Core.Shared.Factory;

using Dapper;

using MediatR;
using MediatR.Pipeline;

using Microsoft.Extensions.DependencyInjection;

using ReSyncInvoiceToEs.Business;
using ReSyncInvoiceToEs.Workers;

using System;
using System.Collections.Generic;

using VnisCore.Core.MongoDB;
using VnisCore.Core.MongoDB.IRepository;
using VnisCore.Core.MongoDB.Repository;
using VnisCore.Core.Oracle.Application.Contracts;
using VnisCore.Core.Oracle.Domain;
using VnisCore.Core.Oracle.EntityFrameworkCore.EntityFrameworkCore;

namespace ReSyncInvoiceToEs
{
    [DependsOn(
        typeof(AbpBackgroundWorkersModule),
        typeof(AbpAutoMapperModule),
        typeof(AbpDapperModule),
        typeof(VnisCoreMongoDbModule),
        typeof(AbpAutofacModule),                            
        typeof(AbpCachingModule),
        typeof(AbpEntityFrameworkCoreOracleModule),
        typeof(VnisCoreOracleEntityFrameworkCoreModule),
        typeof(AbpCachingStackExchangeRedisModule),
        typeof(AbpDddApplicationModule),
        typeof(VnisCoreOracleDomainModule),
        typeof(VnisCoreOracleModuleContractsModule),
        typeof(SharedModule),
        typeof(SharedDtoModule),
        typeof(AbpMinifyModule)
     )]
    public class ToolSyncOracleToMongoDbModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            SqlMapper.RemoveTypeMap(typeof(Guid));
            SqlMapper.RemoveTypeMap(typeof(Guid?));
            SqlMapper.AddTypeHandler(typeof(Guid), new GuidTypeHandler());
            SqlMapper.AddTypeHandler(typeof(Dictionary<string, string>), new DictionaryTypeHandler());

            //Services

            //Repositories
            context.Services.AddScoped<IVnisCoreMongoInvoice01Repository, VnisCoreMongoInvoice01Repository>();
            context.Services.AddScoped<IVnisCoreMongoInvoice01BatchIdRepository, VnisCoreMongoInvoice01BatchIdRepository>();

            context.Services.AddScoped<ElasticSearch, ElasticSearch>();
            // business
            context.Services.AddScoped<IResyncEsInvoice01Business, ResyncEsInvoice01Business>();
            context.Services.AddScoped<IResyncApproveStatusInvoice02Business, ResyncApproveStatusInvoice02Business>();
            context.Services.AddScoped<IResyncApproveStatusInvoice02ToMongoDbBusiness, ResyncApproveStatusInvoice02ToMongoDbBusiness>();
            context.Services.AddScoped<IResyncApproveStatusInvoice01ToMongoDbBusiness, ResyncApproveStatusInvoice01ToMongoDbBusiness>();
            context.Services.AddScoped<IResyncVerificationCodeInvoice01Business, ResyncVerificationCodeInvoice01Business>();
            context.Services.AddScoped<IResyncIsDeclaredInvoice02ToEsBusiness, ResyncIsDeclaredInvoice02ToEsBusiness>();

            context.Services.AddAutoMapperObjectMapper<ToolSyncOracleToMongoDbModule>();
            Configure<AbpAutoMapperOptions>(options =>
            {
                options.AddMaps<ToolSyncOracleToMongoDbAutoMapperProfile>(validate: false);
            });

            context.Services.AddTransient(typeof(IAppFactory), typeof(AppFactory));
            context.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(RequestPreProcessorBehavior<,>));
            context.Services.AddMongoDbContext<VnisCoreMongoDbContext>();
        }

        public override void OnApplicationInitialization(ApplicationInitializationContext context)
        {
            #region TEST
            //context.AddBackgroundWorker<ResyncInvoice01ToEsWorker>();
            //context.AddBackgroundWorker<ResyncApproveStatusInvoice02GroupXWorker>();
            //context.AddBackgroundWorker<ResyncApproveStatusInvoice02Group0Worker>();
            //context.AddBackgroundWorker<ResyncApproveStatusInvoice02ToMongoGroup0Worker>();

            // update trạng chờ đồng bộ trạng thái ký về core
            //context.AddBackgroundWorker<ResyncInvoice01SignStatusMongoToOracleWorker>();
            #endregion


            #region LIVE
            context.AddBackgroundWorker<ResyncVerificationCodeInvoice01GroupXWorker>();
            context.AddBackgroundWorker<ResyncApproveStatusInvoice01ToMongoGroup3XWorker>();
            context.AddBackgroundWorker<ResyncApproveStatusInvoice02ToMongoGroupXWorker>();
            context.AddBackgroundWorker<ResyncApproveStatusInvoice02ToEsGroupXWorker>();
            #endregion

        }
    }
}
