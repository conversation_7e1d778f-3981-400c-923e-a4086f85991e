using Core.BackgroundWorkers;
using Core.Threading;

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

using ReSyncInvoiceToEs.Business;

using Serilog;

using System.Threading.Tasks;

namespace ReSyncInvoiceToEs.Workers
{
    public class ResyncApproveStatusInvoice01ToMongoGroup3XWorker : AsyncPeriodicBackgroundWorkerBase
    {
        private readonly IConfiguration _configuration;
        public ResyncApproveStatusInvoice01ToMongoGroup3XWorker(
            IConfiguration configuration,
            AbpAsyncTimer timer,
            IServiceScopeFactory serviceScopeFactory) : base(timer, serviceScopeFactory)
        {
            _configuration = configuration;

            timer.Period = 1000; //thời gian giữa cách lần chạy

            if (int.TryParse(configuration.GetSection("TimePeriod").Value, out int period))
                timer.Period = period * 1000;
        }

        protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
        {
            var group = _configuration.GetSection("Settings:Group").Value;
            if (string.IsNullOrEmpty(group))
            {
                Log.Error("Chưa có cấu hình Group");
                return;
            }

            try
            {
                short.TryParse(_configuration["Settings:IsEnableResyncApproveStatusInvoice01ToMongoGroup3XWorker"], out var isEnableResyncApproveStatusInvoice01ToMongoGroup3XWorker);
                if (isEnableResyncApproveStatusInvoice01ToMongoGroup3XWorker > 0)
                {
                    await workerContext
                   .ServiceProvider
                   .GetService<IResyncApproveStatusInvoice01ToMongoDbBusiness>()
                   .ResyncApproveStatusInvoice01ToMongoDBAsync(group);
                }
            }
            catch (System.Exception ex)
            {
                Log.Error(ex, ex.Message, ex.StackTrace);
            }
        }
    }
}
