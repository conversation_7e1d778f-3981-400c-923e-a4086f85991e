using Core.BackgroundWorkers;
using Core.Threading;

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

using ReSyncInvoiceToEs.Business;

using Serilog;

using System.Threading.Tasks;

namespace ReSyncInvoiceToEs.Workers
{
    public class ResyncApproveStatusInvoice02ToEsGroupXWorker : AsyncPeriodicBackgroundWorkerBase
    {
        private readonly IConfiguration _configuration;

        public ResyncApproveStatusInvoice02ToEsGroupXWorker(
            AbpAsyncTimer timer, 
            IServiceScopeFactory serviceScopeFactory,
             IConfiguration configuration) : base(timer, serviceScopeFactory)
        {
            timer.Period = 1000; //thời gian gi<PERSON>a cách lần chạy
            _configuration = configuration;
            if (int.TryParse(configuration.GetSection("Settings:TimePeriodResyncIsDecraledInvoice02").Value, out int period))
                timer.Period = period * 1000;
        }

        protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
        {
            try
            {
                short.TryParse(_configuration["Settings:IsEnableResyncIsDecraledInvoice02ToEsGroupXWorker"], out var isEnableResyncIsDecraledInvoice02ToEsGroupXWorker);
                if (isEnableResyncIsDecraledInvoice02ToEsGroupXWorker > 0)
                {
                    await workerContext
                    .ServiceProvider
                    .GetService<IResyncIsDeclaredInvoice02ToEsBusiness>()
                    .ResyncIsDeclaredInvoice02ToEsAsync("group-x");
                }
            }
            catch (System.Exception ex)
            {
                Log.Error(ex, ex.Message, ex.StackTrace);
            }
        }
    }
}
