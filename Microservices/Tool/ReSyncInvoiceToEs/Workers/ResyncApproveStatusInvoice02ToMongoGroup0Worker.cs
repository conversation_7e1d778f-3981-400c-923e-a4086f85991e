using Core.BackgroundWorkers;
using Core.Threading;

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

using ReSyncInvoiceToEs.Business;

using Serilog;

using System.Threading.Tasks;

namespace ReSyncInvoiceToEs.Workers
{
    public class ResyncApproveStatusInvoice02ToMongoGroup0Worker : AsyncPeriodicBackgroundWorkerBase
    {
        public ResyncApproveStatusInvoice02ToMongoGroup0Worker(
            IConfiguration configuration,
            AbpAsyncTimer timer,
            IServiceScopeFactory serviceScopeFactory) : base(timer, serviceScopeFactory)
        {
            timer.Period = 1000; //thời gian gi<PERSON>a cách lần chạy

            if (int.TryParse(configuration.GetSection("TimePeriod").Value, out int period))
                timer.Period = period * 1000;
        }

        protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
        {
            try
            {
                await workerContext
                    .ServiceProvider
                    .GetService<IResyncApproveStatusInvoice02ToMongoDbBusiness>()
                    .ResyncApproveStatusInvoice02ToMongoDBAsync("group-0");
            }
            catch (System.Exception ex)
            {
                Log.Error(ex, ex.Message, ex.StackTrace);
            }
        }
    }
}
