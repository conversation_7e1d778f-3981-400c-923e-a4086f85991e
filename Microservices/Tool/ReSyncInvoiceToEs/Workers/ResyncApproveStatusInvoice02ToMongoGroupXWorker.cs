using Core.BackgroundWorkers;
using Core.Threading;

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

using ReSyncInvoiceToEs.Business;

using Serilog;

using System.Threading.Tasks;

namespace ReSyncInvoiceToEs.Workers
{
    public class ResyncApproveStatusInvoice02ToMongoGroupXWorker : AsyncPeriodicBackgroundWorkerBase
    {
        private readonly IConfiguration _configuration;

        public ResyncApproveStatusInvoice02ToMongoGroupXWorker(
            IConfiguration configuration,
            AbpAsyncTimer timer,
            IServiceScopeFactory serviceScopeFactory) : base(timer, serviceScopeFactory)
        {
            _configuration = configuration;
            timer.Period = 1000; //thời gian giữa cách lần chạy

            if (int.TryParse(configuration.GetSection("TimePeriod").Value, out int period))
                timer.Period = period * 1000;
        }

        protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
        {
            try
            {
                short.TryParse(_configuration["Settings:IsEnableResyncApproveStatusInvoice02ToMongoGroupXWorker"], out var isEnableResyncApproveStatusInvoice02ToMongoGroupXWorker);
                if (isEnableResyncApproveStatusInvoice02ToMongoGroupXWorker > 0)
                {
                    await workerContext
                    .ServiceProvider
                    .GetService<IResyncApproveStatusInvoice02ToMongoDbBusiness>()
                    .ResyncApproveStatusInvoice02ToMongoDBAsync("group-x");
                }    
            }
            catch (System.Exception ex)
            {
                Log.Error(ex, ex.Message, ex.StackTrace);
            }
        }
    }
}
