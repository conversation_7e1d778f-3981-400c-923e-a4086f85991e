using Core.BackgroundWorkers;
using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.Threading;

using Dapper;

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

using System.Linq;
using System.Threading.Tasks;

using VnisCore.Core.MongoDB.IRepository;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;

namespace ReSyncInvoiceToEs.Workers
{
    public class ResyncInvoice01SignStatusMongoToOracleWorker : AsyncPeriodicBackgroundWorkerBase
    {
        private readonly IAppFactory _appFactory;
        private readonly IVnisCoreMongoInvoice01Repository _mongoInvoice01Repository;

        public ResyncInvoice01SignStatusMongoToOracleWorker(
            AbpAsyncTimer timer,
            IConfiguration configuration,
            IServiceScopeFactory serviceScopeFactory,
            IAppFactory appFactory,
            IVnisCoreMongoInvoice01Repository mongoInvoice01Repository) : base(timer, serviceScopeFactory)
        {
            timer.Period = 1; //thời gian gi<PERSON>a cách lần chạy
            _appFactory = appFactory;
            _mongoInvoice01Repository = mongoInvoice01Repository;

            if (int.TryParse(configuration.GetSection("TimePeriod").Value, out int period))
                timer.Period = period * 1;
        }

        protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
        {
            var conn = _appFactory.VnisCoreOracle.Connection;
            conn.Open();
            conn.BeginTransaction();

            var query = $@"SELECT
	                        ""Id"" 
                        FROM

                            ""Invoice01Header"" ih
                        WHERE

                            ""SellerTaxCode"" = '0100106225'

                            AND ""SignStatus"" != {SignStatus.DaKy.GetHashCode()}

                            AND ""InvoiceDate"" >= TIMESTAMP '2023-07-17 00:00:00.000000'

                            AND ""InvoiceDate"" < TIMESTAMP '2023-07-18 00:00:00.000000' ";

            var invoicesHeader = (await conn.QueryAsync<Invoice01HeaderEntity>(query.ToString())).ToList();
            if (invoicesHeader.Any())
            {
                var count = invoicesHeader.Count();
                if (count > 200)
                {
                    var numberOfTimes = count / 200;
                    if (count > numberOfTimes * 200)
                        numberOfTimes += 1;

                    for(int i = 0; i < numberOfTimes; i++)
                    {
                        var takes = invoicesHeader.Skip(i * 200).Take(200).ToList();

                        var invoice01Mongos = await _mongoInvoice01Repository.GetByIdsAsync(takes.Select(x => x.Id).ToList());
                        if (invoice01Mongos.Any())
                        {
                            try
                            {
                                await _mongoInvoice01Repository.UpdateManySyncSignedToCoreAsync(invoice01Mongos.Select(x => x.Id).ToList(), SyncSignedToCoreStatus.UnProcess.GetHashCode());
                            }
                            catch (System.Exception)
                            {

                                throw;
                            }
                        }
                    }    
                } 
            }    

            conn.Close();
        }
    }
}
