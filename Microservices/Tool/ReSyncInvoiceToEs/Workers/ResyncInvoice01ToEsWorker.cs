using Core.BackgroundWorkers;
using Core.Threading;

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

using ReSyncInvoiceToEs.Business;

using Serilog;

using System.Threading.Tasks;

namespace ReSyncInvoiceToEs.Workers
{
    public class ResyncInvoice01ToEsWorker : AsyncPeriodicBackgroundWorkerBase
    {
        public ResyncInvoice01ToEsWorker(AbpAsyncTimer timer,
            IConfiguration configuration,
            IServiceScopeFactory serviceScopeFactory) : base(timer, serviceScopeFactory)
        {
            timer.Period = 1000; //thời gian gi<PERSON>a cách lần chạy

            if (int.TryParse(configuration.GetSection("TimePeriod").Value, out int period))
                timer.Period = period * 1000;
        }

        protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
        {
            try
            {
                await workerContext
                    .ServiceProvider
                    .GetService<IResyncEsInvoice01Business>()
                    .ResyncInvoice01Async();
            }
            catch (System.Exception ex)
            {
                Log.Error(ex, ex.Message, ex.StackTrace);
            }
        }
    }
}
