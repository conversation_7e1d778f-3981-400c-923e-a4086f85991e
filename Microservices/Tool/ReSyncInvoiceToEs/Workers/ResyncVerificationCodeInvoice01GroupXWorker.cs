using Core.BackgroundWorkers;
using Core.Threading;

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

using ReSyncInvoiceToEs.Business;

using Serilog;

using System.Threading.Tasks;

namespace ReSyncInvoiceToEs.Workers
{
    public class ResyncVerificationCodeInvoice01GroupXWorker : AsyncPeriodicBackgroundWorkerBase
    {
        private readonly IConfiguration _configuration;

        public ResyncVerificationCodeInvoice01GroupXWorker(
            IConfiguration configuration,
            AbpAsyncTimer timer,
            IServiceScopeFactory serviceScopeFactory) : base(timer, serviceScopeFactory)
        {
            timer.Period = 1000; //thời gian gi<PERSON>a cách lần chạy
            _configuration = configuration;
            if (int.TryParse(configuration.GetSection("Settings:TimePeriodResyncVerificationCodeInvoice01").Value, out int period))
                timer.Period = period * 1000;
        }

        protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
        {
            try
            {
                short.TryParse(_configuration["Settings:IsEnableResyncVerificationCodeInvoice01GroupXWorker"], out var isEnableResyncVerificationCodeInvoice01GroupXWorker);
                if (isEnableResyncVerificationCodeInvoice01GroupXWorker > 0)
                {
                    await workerContext
                    .ServiceProvider
                    .GetService<IResyncVerificationCodeInvoice01Business>()
                    .ResyncVerificationAsync("group-x");
                }
            }
            catch (System.Exception ex)
            {
                Log.Error(ex, ex.Message, ex.StackTrace);
            }
        }
    }
}
