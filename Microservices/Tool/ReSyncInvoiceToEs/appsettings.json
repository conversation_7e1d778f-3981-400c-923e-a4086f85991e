{
  "ConnectionStrings": {
    "Default": "Data Source='(DESCRIPTION= (ADDRESS=(PROTOCOL=tcp)(HOST=**************)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=pdbvnis)))';User Id=einvoiceauth50staging;Password=Vnis@12A",
    "VnisCoreOracle": "Data Source='(DESCRIPTION= (ADDRESS=(PROTOCOL=tcp)(HOST=**************)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=pdbvnis)))';User Id=einvoice50staging;Password=Vnis@12A",
    "VnisCoreMongoDbAuditLogging": "*************************************************************************************************",
    "VnisCoreMongoDb": "***************************************************************************************"
  },
  //"ConnectionStrings": {
  //  "Default": "Data Source='(DESCRIPTION= (ADDRESS=(PROTOCOL=tcp)(HOST=************)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=VNISDB)))';User Id=einvoiceauth;Password=Vnis@12A",
  //  "VnisCoreOracle": "Data Source='(DESCRIPTION= (ADDRESS=(PROTOCOL=tcp)(HOST=************)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=VNISDB)))';User Id=einvoicecore50;Password=Vnis@12A",
  //  "VnisCoreMongoDbAuditLogging": "***************************************************************************************************************************************************",
  //  "VnisCoreMongoDb": "*******************************************************************************************************************************"
  //},
  "Service": {
    "Name": "ReSyncInvoiceToEs",
    "Title": "ReSyncInvoiceToEs",
    "BaseUrl": "resync-invoice-to-es",
    "AuthApiName": "ReSyncInvoiceToEs"
  },
  "TimePeriod": 1,
  "Settings": {
    "TimePeriodResyncIsDecraledInvoice02": 1,
    "TimePeriodResyncVerificationCodeInvoice01": 300, // 300 <=> 5 minutes
    "TenantGroupsPrivate": "1.1,1.2,1.3",
    "CreateFromDate": "20230601",
    "CreateToDate": "20230701",
    "MaxResultCount": 1000,
    "TaxCode": "0102182292-999",
    "AdvancedSearchValue": "",
    "AdvancedSearchName": "",
    "MaxSelect": 100,
    "Group": "group-x",
    "NumberPreviousDay": 15,
    "IsEnableResyncVerificationCodeInvoice01GroupXWorker": 0,
    "IsEnableResyncApproveStatusInvoice01ToMongoGroup3XWorker": 0,
    "IsEnableResyncApproveStatusInvoice02ToMongoGroupXWorker": 0,
    "IsEnableResyncIsDecraledInvoice02ToEsGroupXWorker": 1
  },
  "Elasticsearch": {
    // "Urls": "http://10.10.200.25:9200"
    "Urls": "http://**************:9200"
  },
  "Logging": {
    "RootFolder": {
      "Folder": "D:\\Logs"
    }
  }
}