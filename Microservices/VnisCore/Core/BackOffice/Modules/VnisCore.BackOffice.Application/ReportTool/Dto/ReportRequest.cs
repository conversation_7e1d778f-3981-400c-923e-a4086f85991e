using Core.Shared.Constants;
using System.ComponentModel.DataAnnotations;

namespace VnisCore.BackOffice.Application.ReportTool.Dto
{
    public class ReportRequest
    {
        [Required(ErrorMessage = "Chưa chọn loại báo cáo")]
        [EnumDataType(typeof(ReportType), ErrorMessage = "Loại báo cáo không đúng")]
        public ReportType Type { get; set; }

        /// <summary>
        /// Số bảng tổng hợp bắt đầu (tháng báo cáo)
        /// </summary>
        [Required(ErrorMessage = "Số bảng tổng hợp bắt đầu không được để trống")]
        public int StartIndex { get; set; }

        /// <summary>
        /// Số bảng tổng hợp bắt đầu (tháng báo cáo)
        /// </summary>
        [Required(ErrorMessage = "Số bảng tổng hợp kết thúc không được để trống")]
        public int FinishIndex { get; set; } 

        /// <summary>
        /// Năm báo cáo
        /// </summary>
        public short ReportYear { get; set; }

        /// <summary>
        /// Tháng báo cáo
        /// </summary>
        [MinLength(0, ErrorMessage ="Tháng báo cáo phải nằm trong khoảng tử 1 đến 12")]
        [MaxLength(12, ErrorMessage = "Tháng báo cáo phải nằm trong khoảng tử 1 đến 12")]
        public short ReportMonth { get; set; }

        /// <summary>
        /// Quý báo cáo
        /// </summary>
        [MinLength(0, ErrorMessage = "Quý báo cáo phải nằm trong khoảng tử 1 đến 4")]
        [MaxLength(4, ErrorMessage = "Quý báo cáo phải nằm trong khoảng tử 1 đến 4")]
        public short ReportQuarter { get; set; }

        /// <summary>
        /// Danh sách hóa đơn thuộc kỳ báo cáo
        /// </summary>
        public string InvoiceIds { get; set; }

        /// <summary>
        /// Loại tiền tệ
        /// </summary>
        public string CurrencyUnit { get; set; }

        /// <summary>
        /// lần đầu báo cáo trong kỳ
        /// </summary>
        public bool IsFirstTimeInPeriod { get; set; }

        /// <summary>
        /// lần bổ sung
        /// </summary>
        public int AdditionalTimes { get; set; }

    }
}
