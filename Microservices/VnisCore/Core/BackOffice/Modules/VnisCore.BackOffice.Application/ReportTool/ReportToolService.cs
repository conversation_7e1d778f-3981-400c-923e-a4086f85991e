using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Microsoft.AspNetCore.Authorization;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using VnisCore.BackOffice.Application.ReportTool.Dto;
using VnisCore.BackOffice.Application.ReportTool.Repository;
using VnisCore.Core.Oracle.Domain.Entities.Reports;

namespace VnisCore.BackOffice.Application.ReportTool
{
    public class ReportToolService : VnisCoreBackOfficeApplicationAppService
    {
        private readonly IAppFactory _appFactory;
        private readonly ITaxReport01HeaderRepository _taxReport01HeaderRepository;
        private readonly ITaxReport01DetailMappingRepository _taxReport01DetailMappingRepository;

        public ReportToolService(IAppFactory appFactory,
            ITaxReport01HeaderRepository taxReport01HeaderRepository,
            ITaxReport01DetailMappingRepository taxReport01DetailMappingRepository)
        {
            _appFactory = appFactory;
            _taxReport01HeaderRepository = taxReport01HeaderRepository;
            _taxReport01DetailMappingRepository = taxReport01DetailMappingRepository;
        }

        /// <summary>
        /// Giả lập tạo báo cáo
        /// </summary>
        /// <returns></returns>
        [Authorize]
        public async Task<ReportResultDto> CreateTaxReport(ReportRequest input)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var currentUser = _appFactory.CurrentUser;
            
            // Xác định giá trị Tháng/Quý báo cáo
            var typeValue = input.Type == ReportType.Quarter ? input.ReportQuarter : input.ReportMonth;

            var (fromDate, toDate) = GetRangeOfTime(input.Type, typeValue, input.ReportYear);

            var taxHeader = new TaxReport01HeaderEntity()
            {
                AdditionalTimes = input.AdditionalTimes,
                UpdateTimes = 0,
                TenantId = tenantId,
                FromDate = fromDate,
                ToDate = toDate,
                FullNameCreator = currentUser.UserName?.Replace("'", "''"),
                ReportDate = DateTime.Now,
                ReportMonth = (short)(input.Type == ReportType.Month ? input.ReportMonth : 0),
                ReportQuarter = (short)(input.Type == ReportType.Quarter ? input.ReportQuarter : 0),
                ReportYear = (short)input.ReportYear,
                IsFirstTimeInPeriod = input.IsFirstTimeInPeriod,
                SignStatus = (short)SignStatus.ChoKy,
                CurrencyUnit = input.CurrencyUnit,
            };

            taxHeader = await _taxReport01HeaderRepository.InsertAsync(taxHeader);

            List<TaxReport01DetailMappingEntity> detailMappings = new List<TaxReport01DetailMappingEntity>();
            for (int index = input.StartIndex; index <= input.FinishIndex; index++)
            {
                var detailMapping = new TaxReport01DetailMappingEntity()
                {
                    Index = index,
                    TaxReportHeaderId = taxHeader.Id,
                    TenantId = tenantId,
                    StatusTvan = 0,
                    SignStatus = 3,
                    InvoiceType = (short)VnisType._01GTKT.GetHashCode(),
                    InvoiceIds = input.InvoiceIds,
                };
                detailMappings.Add(detailMapping);
            }
            await _taxReport01DetailMappingRepository.InsertManyAsync(detailMappings);

            return new ReportResultDto()
            {
                Message = "Thêm báo cáo thành công",
                ReportId = taxHeader.Id,
            };
        }

        /// <summary>
        /// Lấy ngày bắt đầu, kết thúc báo cáo
        /// </summary>
        /// <param name="type"></param>
        /// <param name="index"></param>
        /// <param name="year"></param>
        /// <returns></returns>
        private KeyValuePair<DateTime, DateTime> GetRangeOfTime(ReportType type, int index, int year)
        {
            var from = DateTime.Now;
            var to = DateTime.Now;

            if (type == ReportType.Month)
            {
                from = new DateTime(year, index, 1);
                to = from.AddMonths(1).AddDays(-1);
            }

            if (type == ReportType.Quarter)
            {
                var range = DateTimeExtension.GetRangeOfQuater(index, year);
                from = range.Key;
                to = range.Value;
            }

            return new KeyValuePair<DateTime, DateTime>(from, to);
        }
    }
}
