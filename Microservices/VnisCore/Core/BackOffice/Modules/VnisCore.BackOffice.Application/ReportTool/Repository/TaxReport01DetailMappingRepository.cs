using Core.Domain.Repositories.EntityFrameworkCore;
using Core.EntityFrameworkCore;
using VnisCore.Core.Oracle.Domain.Entities.Reports;
using VnisCore.Core.Oracle.EntityFrameworkCore.EntityFrameworkCore;

namespace VnisCore.BackOffice.Application.ReportTool.Repository
{
    public class TaxReport01DetailMappingRepository : EfCoreRepository<VnisCoreOracleDbContext, TaxReport01DetailMappingEntity, long>, ITaxReport01DetailMappingRepository
    {
        public TaxReport01DetailMappingRepository(IDbContextProvider<VnisCoreOracleDbContext> dbContextProvider)
        : base(dbContextProvider)
        {

        }
    }
}
