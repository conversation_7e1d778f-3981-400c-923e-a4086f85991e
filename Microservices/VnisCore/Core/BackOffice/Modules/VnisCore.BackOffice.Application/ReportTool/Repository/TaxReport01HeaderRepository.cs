using Core.Domain.Repositories.EntityFrameworkCore;
using Core.EntityFrameworkCore;
using VnisCore.Core.Oracle.Domain.Entities.Reports;
using VnisCore.Core.Oracle.EntityFrameworkCore.EntityFrameworkCore;

namespace VnisCore.BackOffice.Application.ReportTool.Repository
{
    public class TaxReport01HeaderRepository : EfCoreRepository<VnisCoreOracleDbContext, TaxReport01HeaderEntity, long>, ITaxReport01HeaderRepository
    {
        public TaxReport01HeaderRepository(IDbContextProvider<VnisCoreOracleDbContext> dbContextProvider)
        : base(dbContextProvider)
        {

        }
    }
}
