using Core;
using Core.Caching;
using Core.SettingManagement;
using Core.Shared.Cached;
using Core.Shared.Cached.Dtos;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Services;
using Dapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VnisCore.BackOffice.Application.TvanTool.Dto;

namespace VnisCore.BackOffice.Application.TvanTool
{
    public class TvanToolService : VnisCoreBackOfficeApplicationAppService
    {
        private const string APIKEY = "XApiKey";
        private readonly IAppFactory _appFactory;
        private readonly IConfiguration _configuration;

        private readonly IRedisCacheService _redisCacheService;
        private readonly ICacheSupportsMultipleItems _cacheSupportsMultipleItems;
        private readonly ITenantCacheBusiness _tenantCacheBusiness;
        private readonly ISettingRepository _settingRepository;
        private readonly ISettingWorkerService _settingWorkerService;

        public TvanToolService(
            IRedisCacheService redisCacheService,
            IAppFactory appFactory,
            IConfiguration configuration,
            ICacheSupportsMultipleItems cacheSupportsMultipleItems,
            ITenantCacheBusiness tenantCacheBusiness,
            ISettingRepository settingRepository,
            ISettingWorkerService settingWorkerService)
        {
            _appFactory = appFactory;
            _configuration = configuration;
            _redisCacheService = redisCacheService;
            _cacheSupportsMultipleItems = cacheSupportsMultipleItems;
            _tenantCacheBusiness = tenantCacheBusiness;
            _settingRepository = settingRepository;
            _settingWorkerService = settingWorkerService;
        }

        [Authorize]
        [HttpPost(Utilities.ApiUrlBase + "ShowEnableAllSendTvan")]
        public async Task<EnableSendTvanResponse> ShowEnableAllSendTvan()
        {
            ValidateApiKey();

            var cacheTenants = await _tenantCacheBusiness.GetAllTenantAsync();
            List<string> taxCodes = new List<string>();
            foreach (var tenant in cacheTenants)
            {
                var value = await _settingWorkerService.GetByCodeAsync(tenant.Id, SettingKey.ConfigSendTvan.ToString(), tenant.TaxCode);

                if (value == 1)
                {
                    taxCodes.Add(tenant.TaxCode);
                }
            }

            return new EnableSendTvanResponse()
            {
                Message = "Danh sách MST gửi Tvan",
                TaxCodes = taxCodes
            };
        }

        [Authorize]
        [HttpPost(Utilities.ApiUrlBase + "ShowDisableAllSendTvan")]
        public async Task<EnableSendTvanResponse> ShowDisableAllSendTvan()
        {
            ValidateApiKey();

            var cacheTenants = await _tenantCacheBusiness.GetAllTenantAsync();
            List<string> taxCodes = new List<string>();
            foreach (var tenant in cacheTenants)
            {
                var value = await _settingWorkerService.GetByCodeAsync(tenant.Id, SettingKey.ConfigSendTvan.ToString(), tenant.TaxCode);

                if (value == 0)
                {
                    taxCodes.Add(tenant.TaxCode);
                }
            }

            return new EnableSendTvanResponse()
            {
                Message = "Danh sách MST không gửi Tvan",
                TaxCodes = taxCodes
            };
        }

        [Authorize]
        [HttpPost(Utilities.ApiUrlBase + "EnableAllSendTvan")]
        public async Task<EnableSendTvanResponse> EnableAllSendTvan(EnableAllSendToTvanRequest request)
        {
            ValidateApiKey();

            var cacheTenants = await _tenantCacheBusiness.GetAllTenantAsync();

            var ignoreTenants = new List<TenantCaching>();
            // Loại bỏ key không update
            if (!request.IgnogreTaxCodes.IsNullOrEmpty())
            {
                ignoreTenants = cacheTenants.
                        WhereIf(!request.IgnogreTaxCodes.IsNullOrEmpty(), x => request.IgnogreTaxCodes.Contains(x.TaxCode)).
                        ToList();
            }

            // Update gia tri
            await UpdateSetting(
                    SettingKey.ConfigSendTvan.ToString(),
                    "1",
                    ignoreTenants.Select(x => x.Id).ToList()
                );

            // Remove key
            foreach (var tenant in cacheTenants)
            {
                string key = CacheKeyBuilder.BuildCacheKeyWithPath(
                        CacheKeyPath.t,
                        tenant.TaxCode,
                        CacheKeyPath.Setting,
                        CacheKeyPath.Tvan,
                        SettingKey.ConfigSendTvan.ToString()
                    );

                _redisCacheService.Remove(key);
            }

            return new EnableSendTvanResponse()
            {
                Message = "Cập nhật BẬT tất cả gửi TVAN thành công"
            };
        }

        [Authorize]
        [HttpPost(Utilities.ApiUrlBase + "DisableAllSendTvan")]
        public async Task<EnableSendTvanResponse> DisableAllSendTvan(EnableAllSendToTvanRequest request)
        {
            ValidateApiKey();

            var cacheTenants = await _tenantCacheBusiness.GetAllTenantAsync();

            var ignoreTenants = new List<TenantCaching>();
            // Loại bỏ key không update
            if (!request.IgnogreTaxCodes.IsNullOrEmpty())
            {
                ignoreTenants = cacheTenants.
                        WhereIf(!request.IgnogreTaxCodes.IsNullOrEmpty(), x => request.IgnogreTaxCodes.Contains(x.TaxCode)).
                        ToList();
            }

            // Update gia tri
            await UpdateSetting(
                    SettingKey.ConfigSendTvan.ToString(),
                    "0",
                    ignoreTenants.Select(x => x.Id).ToList()
                );

            // Key riêng
            foreach (var tenant in cacheTenants)
            {
                string key = CacheKeyBuilder.BuildCacheKeyWithPath(
                        CacheKeyPath.t,
                        tenant.TaxCode,
                        CacheKeyPath.Setting,
                        CacheKeyPath.Tvan,
                        SettingKey.ConfigSendTvan.ToString()
                    );

                _redisCacheService.Remove(key);
            }

            return new EnableSendTvanResponse()
            {
                Message = "Cập nhật TẮT tất cả gửi TVAN thành công"
            };
        }

        [Authorize]
        [HttpPost(Utilities.ApiUrlBase + "EnableSendTvan")]
        public async Task<EnableSendTvanResponse> EnableSendTvan(EnableSendTvanRequest request)
        {
            ValidateApiKey();

            var cacheTenants = await _tenantCacheBusiness.GetAllTenantAsync();

            List<TenantCaching> tenants = new List<TenantCaching>();    
            foreach (var taxCodeRequest in request.TaxCodes)
            {
                if(!cacheTenants.Where(x=>x.TaxCode == taxCodeRequest).Any())
                {
                    throw new UserFriendlyException($"Mã số thuế {taxCodeRequest} không tồn tại trên hệ thống");
                }
                var tenant = cacheTenants.Where(x => x.TaxCode == taxCodeRequest).FirstOrDefault();
                tenants.Add(tenant);
            }

            foreach (var tenant in tenants)
            {
                await UpdateSetting(
                    SettingKey.ConfigSendTvan.ToString(),
                    "1",
                    tenant.Id
                );

                // Remove key
                string key = CacheKeyBuilder.BuildCacheKeyWithPath(
                        CacheKeyPath.t, 
                        tenant.TaxCode, 
                        CacheKeyPath.Setting, 
                        CacheKeyPath.Tvan, 
                        SettingKey.ConfigSendTvan.ToString()
                    );

                _redisCacheService.Remove(key);
            }

            return new EnableSendTvanResponse()
            {
                Message = "Cập nhật BẬT gửi Tvan thành công",
                TaxCodes = request.TaxCodes
            };
        }

        [Authorize]
        [HttpPost(Utilities.ApiUrlBase + "DisableSendTvan")]
        public async Task<EnableSendTvanResponse> DisableSendTvan(DisableSendTvanRequest request)
        {
            ValidateApiKey();

            var cacheTenants = await _tenantCacheBusiness.GetAllTenantAsync();

            List<TenantCaching> tenants = new List<TenantCaching>();
            foreach (var taxCodeRequest in request.TaxCodes)
            {
                if (!cacheTenants.Where(x => x.TaxCode == taxCodeRequest).Any())
                {
                    throw new UserFriendlyException($"Mã số thuế {taxCodeRequest} không tồn tại trên hệ thống");
                }
                var tenant = cacheTenants.Where(x => x.TaxCode == taxCodeRequest).FirstOrDefault();
                tenants.Add(tenant);
            }

            foreach (var tenant in tenants)
            {
                await UpdateSetting(
                    SettingKey.ConfigSendTvan.ToString(),
                    "0",
                    tenant.Id
                );

                // Remove key
                string key = CacheKeyBuilder.BuildCacheKeyWithPath(
                        CacheKeyPath.t,
                        tenant.TaxCode,
                        CacheKeyPath.Setting,
                        CacheKeyPath.Tvan,
                        SettingKey.ConfigSendTvan.ToString()
                    );

                _redisCacheService.Remove(key);
            }

            return new EnableSendTvanResponse()
            {
                Message = "Cập nhật TẮT gửi Tvan thành công",
                TaxCodes = request.TaxCodes
            };
        }

        private async Task UpdateSetting(string code, string value, Guid tenantId)
        {
            try
            {
                var setting = await _settingRepository.GetByCodeAndTenantIdAsync(code, tenantId);

                if (setting == null)
                {
                    // Ko tồn tại

                    var defaultSetting = await _settingRepository.GetByCodeAndTenantIdAsync(code, Guid.Empty);
                    // Insert
                    var insertSql = $@"
                        INSERT INTO INV_MASS_V5_AUTH_STG.""VnisSettings""
                        (""Id"", ""ParentId"", ""GroupCode"", ""Name"", ""Code"", ""Value"", ""TenantId"", ""Options"", ""Type"", ""Description"", ""IsReadOnly"", ""CreationTime"", ""IsDeleted"")
                        VALUES(sys_guid(), :ParentId, :GroupCode, :Name, :Code, :Value, :TenantId, :Options, :Type, :Description, :IsReadOnly, :CreationTime, :IsDeleted)";

                    await _appFactory.AuthDatabase.Connection.ExecuteAsync(insertSql.ToString(),
                        new
                        {
                            ParentId = defaultSetting.ParentId,
                            GroupCode = defaultSetting.GroupCode,
                            Name = defaultSetting.Name,
                            Code = defaultSetting.Code,
                            Value = value,
                            TenantId = OracleExtension.ConvertGuidToRaw(tenantId),
                            Options = defaultSetting.Options,
                            Type = defaultSetting.Type,
                            Description = defaultSetting.Description,
                            IsReadOnly = defaultSetting.IsReadOnly == false ? 0 : 1,
                            CreationTime = defaultSetting.CreationTime,
                            IsDeleted = defaultSetting.IsDeleted == false ? 0 : 1,
                        });

                }
                else
                {
                    StringBuilder updateSql = new StringBuilder($@"
                                UPDATE ""VnisSettings"" 
                                SET ""Value"" = :Value
                                WHERE ""Code"" =:Code AND ""TenantId"" = :TenantId ");

                    await _appFactory.AuthDatabase.Connection.ExecuteAsync(updateSql.ToString(),
                        new
                        {
                            Code = code,
                            Value = value,
                            TenantId = OracleExtension.ConvertGuidToRaw(tenantId)
                        });
                }
            }
            catch (Exception e)
            {

                throw;
            }

        }

        private async Task UpdateSetting(string code, string value, List<Guid> ignoreTenantIds)
        {
            StringBuilder sql = new StringBuilder($@"
                                UPDATE ""VnisSettings"" 
                                SET ""Value"" = :Value
                                WHERE ""Code"" =:Code ");
            if (!ignoreTenantIds.IsNullOrEmpty())
            {
                sql.AppendLine($@"AND ""TenantId"" NOT IN :TenantIds ");
            }
            await _appFactory.AuthDatabase.Connection.ExecuteAsync(sql.ToString(),
                new
                {
                    Code = code,
                    Value = value,
                    TenantIds = ignoreTenantIds.Select(x => OracleExtension.ConvertGuidToRaw(x)).ToArray()
                });
        }

        private void ValidateApiKey()
        {
            var httpContextAccessor = _appFactory.GetServiceDependency<IHttpContextAccessor>();
            if (!httpContextAccessor.HttpContext.Request.Headers.TryGetValue(APIKEY, out var extractedApiKey))
                throw new UserFriendlyException("Không tìm thấy API KEY");

            var apiKey = _configuration[$"{APIKEY}"];
            if (apiKey.IsNullOrEmpty())
                throw new UserFriendlyException("Chưa có cấu hình API KEY");

            if (!apiKey.Equals(extractedApiKey))
                throw new UserFriendlyException("API KEY không đúng");
        }
    }
}
