using Core.Domain.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;

namespace VnisCore.Catalog.Infrastructure.IRepository
{
    public interface ICurrencyRepository : IRepository<CurrencyEntity, long>
    {
        /// <summary>
        /// L<PERSON>y danh sách theo Ids
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        Task<List<CurrencyEntity>> GetByIdsAsync(List<long> ids, Guid tenantId);

        /// <summary>
        /// Lấy theo Id
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        Task<CurrencyEntity> GetByIdAsync(long id, Guid tenantId);
    }
}
