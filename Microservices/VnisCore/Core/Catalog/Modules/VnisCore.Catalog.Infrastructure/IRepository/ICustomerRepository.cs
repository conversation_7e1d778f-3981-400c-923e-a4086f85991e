using Core.Domain.Repositories;

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;

namespace VnisCore.Catalog.Infrastructure.IRepository
{
    public interface ICustomerRepository : IRepository<CustomerEntity, long>
    { 
        /// <summary>
        /// L<PERSON>y danh sách theo Ids
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        Task<List<CustomerEntity>> GetByIdsAsync(List<long> ids, Guid tenantId);

        /// <summary>
        /// L<PERSON>y theo Id
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        Task<CustomerEntity> GetByIdAsync(long id, Guid tenantId);
    }
}
