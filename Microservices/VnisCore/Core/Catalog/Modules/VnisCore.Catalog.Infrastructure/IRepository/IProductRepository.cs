using Core.Domain.Repositories;

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;

namespace VnisCore.Catalog.Infrastructure.IRepository
{
    public interface IProductRepository : IRepository<ProductEntity, long>
    { 
        /// <summary>
        /// L<PERSON><PERSON> danh sách theo Ids
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        Task<List<ProductEntity>> GetByIdsAsync(List<long> ids, Guid tenantId);

        /// <summary>
        /// Lấy theo Id
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        Task<ProductEntity> GetByIdAsync(long id, Guid tenantId);
    }
}
