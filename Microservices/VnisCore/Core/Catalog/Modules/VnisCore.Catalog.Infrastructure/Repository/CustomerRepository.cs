using Core.Domain.Repositories.EntityFrameworkCore;
using Core.EntityFrameworkCore;
using Nest;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.Core.Oracle.EntityFrameworkCore.EntityFrameworkCore;

using VnisCore.Catalog.Infrastructure.IRepository;

namespace VnisCore.Catalog.Infrastructure.Repository
{
    public class CustomerRepository : EfCoreRepository<VnisCoreOracleDbContext, CustomerEntity, long>, ICustomerRepository
    {
        public CustomerRepository(IDbContextProvider<VnisCoreOracleDbContext> dbContextProvider) : base(dbContextProvider)
        {
        }

        public async Task<CustomerEntity> GetByIdAsync(long id, Guid tenantId)
        {
            var dbContext = await GetDbContextAsync();

            var result = dbContext
                        .Set<CustomerEntity>()
                        .Where(x => x.Id == id && x.TenantId == tenantId && x.IsDeleted == false)
                        .FirstOrDefault();

            return result;
        }

        public async Task<List<CustomerEntity>> GetByIdsAsync(List<long> ids, Guid tenantId)
        {
            var dbContext = await GetDbContextAsync();

            var result = dbContext
                        .Set<CustomerEntity>()
                        .Where(x => ids.Contains(x.Id) && x.TenantId == tenantId && x.IsDeleted == false)
                        .ToList();

            return result;
        }
    }
}
