using Core.Domain.Repositories.EntityFrameworkCore;
using Core.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VnisCore.Catalog.Infrastructure.IRepository;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.Core.Oracle.EntityFrameworkCore.EntityFrameworkCore;

namespace VnisCore.Catalog.Infrastructure.Repository
{
    public class ProductTypeRepository : EfCoreRepository<VnisCoreOracleDbContext, ProductTypeEntity, long>, IProductTypeRepository
    {
        public ProductTypeRepository(IDbContextProvider<VnisCoreOracleDbContext> dbContextProvider) : base(dbContextProvider)
        {
        }

        public async Task<ProductTypeEntity> GetByIdAsync(long id, Guid tenantId)
        {
            var dbContext = await GetDbContextAsync();

            var result = dbContext
                        .Set<ProductTypeEntity>()
                        .Where(x => x.Id == id && x.TenantId == tenantId && x.IsDeleted == false)
                        .FirstOrDefault();

            return result;
        }

        public async Task<List<ProductTypeEntity>> GetByIdsAsync(List<long> ids, Guid tenantId)
        {
            var dbContext = await GetDbContextAsync();

            var result = dbContext
                        .Set<ProductTypeEntity>()
                        .Where(x => ids.Contains(x.Id) && x.TenantId == tenantId && x.IsDeleted == false)
                        .ToList();

            return result;
        }
    }
}
