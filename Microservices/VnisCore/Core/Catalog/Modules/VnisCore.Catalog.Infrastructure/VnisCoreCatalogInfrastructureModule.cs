using System.Reflection;
using MediatR;
using MediatR.Pipeline;
using Microsoft.Extensions.DependencyInjection;
using Core.Application;
using Core.AutoMapper;
using Core.Modularity;
using Core.Shared.Factory;
using Dapper;
using System;
using Core.Shared.DapperSqlTypeHandler;
using System.Collections.Generic;
using VnisCore.Core.Oracle.Domain;
using VnisCore.Core.Oracle.Application.Contracts;
using VnisCore.Catalog.Infrastructure.IRepository;
using VnisCore.Catalog.Infrastructure;
using VnisCore.Catalog.Infrastructure.Repository;

namespace VnisCore.PILetter.Infrastructure
{
    [DependsOn(
        typeof(AbpDddApplicationModule),
        typeof(VnisCoreOracleDomainModule),
        typeof(VnisCoreOracleModuleContractsModule),
        typeof(AbpAutoMapperModule)
    )]
    public class VnisCoreCatalogInfrastructureModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            SqlMapper.RemoveTypeMap(typeof(Guid));
            SqlMapper.RemoveTypeMap(typeof(Guid?));
            SqlMapper.AddTypeHandler(typeof(Guid), new GuidTypeHandler());
            SqlMapper.AddTypeHandler(typeof(Dictionary<string, string>), new DictionaryTypeHandler());

            context.Services.AddAutoMapperObjectMapper<VnisCoreCatalogInfrastructureModule>();
            Configure<AbpAutoMapperOptions>(options =>
            {
                options.AddProfile<VnisCoreCatalogInfrastructureAutoMapperProfile>(validate: false);
            });
            context.Services.AddTransient(typeof(IAppFactory), typeof(AppFactory));
            context.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(RequestPreProcessorBehavior<,>));
            context.Services.AddMediatR(typeof(VnisCoreCatalogInfrastructureModule).GetTypeInfo().Assembly);

            #region Thêm các phụ thuộc repo, service
            context.Services.AddScoped<ICurrencyRepository, CurrencyRepository>();
            context.Services.AddScoped<ICustomerRepository, CustomerRepository>();
            context.Services.AddScoped<IEmailTemplateRepository, EmailTemplateRepository>();
            context.Services.AddScoped<IGroupCustomerRepository, GroupCustomerRepository>();
            context.Services.AddScoped<IProductRepository, ProductRepository>();
            context.Services.AddScoped<IProductTypeRepository, ProductTypeRepository>();
            context.Services.AddScoped<ITaxRepository, TaxRepository>();
            context.Services.AddScoped<IUnitRepository, UnitRepository>();
            #endregion
        }
    }
}