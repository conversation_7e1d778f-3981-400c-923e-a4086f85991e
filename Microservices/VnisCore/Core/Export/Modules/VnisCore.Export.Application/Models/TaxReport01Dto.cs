using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using Core.Application.Dtos;
using Core.Shared.Attributes;
using Core.Shared.Constants;
using Core.Shared.Invoice.Interfaces;

namespace VnisCore.Export.Application.Models
{
    public class TaxReport01Dto : EntityDto<long>
    {
        public new long Id { get; set; }

        [Required(ErrorMessage = "Chưa chọn loại báo cáo")]
        [EnumDataType(typeof(ReportType), ErrorMessage = "Loại báo cáo không đúng")]
        public ReportType Type { get; set; }

        [Required(ErrorMessage = "Chưa chọn kỳ báo cáo")]
        public int Index { get; set; } //Kỳ báo cáo

        [Required(ErrorMessage = "Chưa chọn năm báo cáo")]
        public int Year { get; set; } // Năm báo cáo

        public DateTime ReportDate { get; set; } // Ngày lập báo cáo, người dùng tự chọn
        public DateTime FromDate { get; set; } // Ngày đầu tiên của kỳ báo cáo
        public DateTime ToDate { get; set; } // Ngày cuối cùng của kỳ báo cáo
        public short ReportYear { get; set; } // Năm báo cáo
        public short ReportMonth { get; set; } // Tháng báo cáo
        public short ReportQuarter { get; set; } // Quý báo cáo

        [MaxLength(250, ErrorMessage = "Tối đa 250 ký tự")]
        public string FullNameCreator { get; set; }

        [MaxLength(250, ErrorMessage = "Tối đa 250 ký tự")]
        public string Representative { get; set; } // Người đại diện pháp luật

        public string Note { get; set; }

        /// <summary>
        /// số báo cáo
        /// </summary>
        public int Number { get; set; }

        /// <summary>
        /// lần đầu báo cáo trong kỳ
        /// </summary>
        public bool IsFirstTimeInPeriod { get; set; }

        /// <summary>
        /// lần bổ sung
        /// </summary>
        public int AdditionalTimes { get; set; }

        /// <summary>
        /// lần chỉnh sửa
        /// </summary>
        public int UpdateTimes { get; set; }

        public short SignStatus { get; set; }

        public short StatusTvan { get; set; }

        public int PageSize { get; set; }
        public long RowNumber { get; set; }
        public decimal TotalItems { get; set; }

        [JsonIgnore]
        public Guid TenantId { get; set; }

        public virtual ICollection<TaxReport01DetailDto> TaxReportDetails { get; set; }
    }

    public class TaxReport01DetailDto : EntityDto<long>, IBaseTaxReport01DetailDecreeNo70Dto
    {
        public int Stt { get; set; }

        public long InvoiceReferenceId { get; set; }

        [Required(ErrorMessage = "Id hóa đơn không được để trống")]
        public long InvoiceHeaderId { get; set; }

        [Required(ErrorMessage = "Ký hiệu mẫu hóa đơn không được để trống")]
        [TemplateNo(ErrorMessage = "Định dạng ký hiệu mẫu hóa đơn không đúng")]
        public short TemplateNo { get; set; }

        [Required(ErrorMessage = "Ký hiệu hóa đơn không được để trống")]
        [MaxLength(50, ErrorMessage = "Ký hiệu tối đa 50 ký tự")]
        [SerialNo(ErrorMessage = "Định dạng ký hiệu hóa đơn không đúng")]
        public string SerialNo { get; set; }

        /// <summary>
        /// cho hóa đơn 03
        /// </summary>
        public string ReceiverName { get; set; }

        public string BuyerFullName { get; set; }
        /// <summary>
        /// cho hóa đơn 04
        /// </summary>
        public string BuyerName { get; set; }
        public string BuyerTaxCode { get; set; }

        public DateTime InvoiceDate { get; set; }
        public string InvoiceDateDisplay { get; set; }

        public string InvoiceNo { get; set; }

        /// <summary>
        /// Số hóa đơn 
        /// </summary>
        [Range(1, 99999999, ErrorMessage = "Số hóa đơn không đúng. Số hóa đơn trong khoảng 1-99999999")]
        public int Number { get; set; }

        public decimal? TotalAmount { get; set; }

        public decimal? TotalVatAmount { get; set; }

        public decimal? TotalQuantity { get; set; }

        public decimal TotalPaymentAmount { get; set; }

        public string Status { get; set; }

        public short InvoiceStatus { get; set; }

        public long InvoiceTemplateId { get; set; }

        public string InvoiceReferenceData { get; set; }

        public string InvoiceReferenceDataSplit { get; set; }

        /// <summary>
        /// loại hóa đơn
        /// </summary>
        public short InvoiceType { get; set; }

        public string ProductId { get; set; }
        public string UnitId { get; set; }
        public string ProductName { get; set; }
        public string ProductCode { get; set; }
        public string UnitName { get; set; }

        public decimal Amount { get; set; }

        public string Note { get; set; }

        public short? VatPercent { get; set; }

        public decimal PaymentAmount { get; set; }

        public decimal? Quantity { get; set; }

        public long PageSize { get; set; }
        public long RowNumber { get; set; }
        public decimal TotalItems { get; set; }

        //FK
        public long TaxReportHeaderId { get; set; }
        /// <summary>
        /// Tỷ giá
        /// </summary>
        public decimal ExchangeRate { get; set; }
        /// <summary>
        /// Tổng tiền phí
        /// </summary>
        public decimal? TTPhi { get; set; }
        /// <summary>
        /// Tổng giảm trừ khác
        /// </summary>
        public decimal? TGTKhac { get; set; }
        public string ExtraProperties { get; set; }
        public string BudgetUnitCode { get; set; }
    }

    public class TaxReport01DetailGroupDto : EntityDto<long>, IBaseTaxReport01DetailDecreeNo70Dto
    {
        public int Stt { get; set; }

        public long InvoiceReferenceId { get; set; }

        [Required(ErrorMessage = "Id hóa đơn không được để trống")]
        public long InvoiceHeaderId { get; set; }

        [Required(ErrorMessage = "Ký hiệu mẫu hóa đơn không được để trống")]
        [TemplateNo(ErrorMessage = "Định dạng ký hiệu mẫu hóa đơn không đúng")]
        public short TemplateNo { get; set; }

        [Required(ErrorMessage = "Ký hiệu hóa đơn không được để trống")]
        [MaxLength(50, ErrorMessage = "Ký hiệu tối đa 50 ký tự")]
        [SerialNo(ErrorMessage = "Định dạng ký hiệu hóa đơn không đúng")]
        public string SerialNo { get; set; }

        /// <summary>
        /// cho hóa đơn 03
        /// </summary>
        public string ReceiverName { get; set; }

        public string BuyerFullName { get; set; }
        /// <summary>
        /// cho hóa đơn 04
        /// </summary>
        public string BuyerName { get; set; }
        public string BuyerTaxCode { get; set; }

        public DateTime InvoiceDate { get; set; }
        public string InvoiceDateDisplay { get; set; }

        public string InvoiceNo { get; set; }

        /// <summary>
        /// Số hóa đơn 
        /// </summary>
        [Range(1, 99999999, ErrorMessage = "Số hóa đơn không đúng. Số hóa đơn trong khoảng 1-99999999")]
        public int Number { get; set; }

        public decimal? TotalAmount { get; set; }

        public decimal? TotalVatAmount { get; set; }

        public decimal? TotalQuantity { get; set; }

        public decimal TotalPaymentAmount { get; set; }

        public string Status { get; set; }

        public short InvoiceStatus { get; set; }

        public long InvoiceTemplateId { get; set; }

        public string InvoiceReferenceData { get; set; }

        public string InvoiceReferenceDataSplit { get; set; }

        /// <summary>
        /// loại hóa đơn
        /// </summary>
        public short InvoiceType { get; set; }

        public string ProductId { get; set; }
        public string UnitId { get; set; }
        public string ProductName { get; set; }
        public string ProductCode { get; set; }
        public string UnitName { get; set; }

        public decimal Amount { get; set; }

        public string Note { get; set; }

        public short? VatPercent { get; set; }

        public decimal PaymentAmount { get; set; }

        public decimal? Quantity { get; set; }

        public long PageSize { get; set; }
        public long RowNumber { get; set; }
        public decimal TotalItems { get; set; }

        //FK
        public long TaxReportHeaderId { get; set; }
        /// <summary>
        /// Tỷ giá
        /// </summary>
        public decimal ExchangeRate { get; set; }
        /// <summary>
        /// Tổng tiền phí
        /// </summary>
        public decimal? TTPhi { get; set; }
        /// <summary>
        /// Tổng giảm trừ khác
        /// </summary>
        public decimal? TGTKhac { get; set; }

        public string ExtraProperties { get; set; }

        public List<TaxReport01DetailDto> ReferenceInvoices { get; set; }
        public string BudgetUnitCode { get; set; }
    }
}
