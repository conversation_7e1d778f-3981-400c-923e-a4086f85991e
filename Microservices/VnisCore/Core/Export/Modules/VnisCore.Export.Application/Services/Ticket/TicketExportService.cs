using AutoMapper;
using Core.AspNetCore.Mvc;
using Core.Shared.Constants;
using Core.Shared.Dto;
using Core.Shared.Factory;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using System;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Application.Contracts.Permissions.Invoice.Invoice04;
using VnisCore.Core.Oracle.Application.Contracts.Permissions.Invoice.Ticket;
using VnisCore.Export.Application.Models.Invoice01s;
using VnisCore.Export.Application.Models.Requests;
using static Core.Shared.Extensions.DateRangeValidator;

namespace VnisCore.Export.Application.Services.Ticket
{
    [Authorize]
    [Route(Utilities.ApiUrlBase)]
    public class TicketExportService : AbpController
    {
        private readonly IAppFactory _factory;
        private readonly IFlexcelTicketExportHeaderService _flexcelTicketExportHeaderService;
        private readonly IFlexcelTicketExportDetailService _flexcelTicketExportDetailService;
        private readonly IConfiguration _configuration;

        public TicketExportService(IAppFactory factory,
            IFlexcelTicketExportHeaderService flexcelTicketExportHeaderService,
            IFlexcelTicketExportDetailService flexcelTicketExportDetailService,
            IConfiguration configuration)
        {
            _factory = factory;
            _flexcelTicketExportHeaderService = flexcelTicketExportHeaderService;
            _flexcelTicketExportDetailService = flexcelTicketExportDetailService;
            _configuration = configuration;
        }

        /// <summary>
        /// xuất excel bảng kê
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [Authorize(TicketPermissions.Ticket.ExportExcel)]
        [HttpPost("excel/{idTask?}")]
        public async Task<IActionResult> ExportExcel([FromRoute] Guid? idTask, [FromBody] ExportTicketRequestModel request)
        {
            short.TryParse(_configuration.GetSection("Settings:MonthsLimit").Value, out var monthsLimit);
            if (monthsLimit <= 0)
            {
                monthsLimit = 3;
            }

            DateRangeChecker.ValidateDateRange(request.CreateFromDate.Value.Date, request.CreateToDate.Value.Date, monthsLimit);

            switch (request.Service)
            {
                case "TicketExportHeaderService":
                    {
                        try
                        {

                            var config = new MapperConfiguration(cfg =>
                            {
                                cfg.CreateMap<ExportTicketRequestModel, ExportExcelTicketRequestModel>().ReverseMap();
                            });

                            var mapper = new Mapper(config);
                            var input = mapper.Map<ExportTicketRequestModel, ExportExcelTicketRequestModel>(request);
                            var fileDto = await _flexcelTicketExportHeaderService.ExportExcelAsync(input);
                            return Ok(fileDto);
                        }
                        catch (Exception e)
                        {

                            throw;
                        }
                    }
                case "TicketExportDetailService":
                    {
                        var config = new MapperConfiguration(cfg =>
                        {
                            cfg.CreateMap<ExportTicketRequestModel, ExportExcelTicketRequestModel>().ReverseMap();
                        });

                        var mapper = new Mapper(config);
                        var input = mapper.Map<ExportTicketRequestModel, ExportExcelTicketRequestModel>(request);
                        var fileDto = await _flexcelTicketExportDetailService.ExportExcelAsync(input);
                        return Ok(fileDto);
                    }
            }

            request.TaskId = idTask;
            var response = await _factory.Mediator.Send(request);
            if (!request.TaskId.HasValue)
            {
                //nếu là lần đầu export
                return Ok(response);
            }
            else
            {
                //là lần t2 trở đi
                if (response.TotalPages == response.CurrentPage && response.FileBytes != null && response.FileBytes.Length > 0)
                {
                    //lần cuối cùng
                    //return File(response.Bytes, ContentType.Stream, response.FileName);
                    return Ok(new FileDto
                    {
                        FileBytes = response.FileBytes,
                        ContentType = ContentType.Stream,
                        FileName = response.FileName,
                    });
                }
                else
                    return Ok(response);
            }
        }

    }

}
