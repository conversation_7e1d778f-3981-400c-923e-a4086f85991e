using Core.Application;
using Core.AutoMapper;
using Core.Modularity;
using Core.Shared;
using Core.Shared.DapperSqlTypeHandler;
using Core.Shared.Factory;
using Core.Shared.FileManager.Interfaces;
using Core.Shared.FileManager.Services;
using Core.Shared.Pdf.Interfaces;
using Core.Shared.Pdf.Services;
using Dapper;
using MediatR;
using MediatR.Pipeline;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Reflection;
using VnisCore.Core.MongoDB;
using VnisCore.Core.Oracle.Application.Contracts;
using VnisCore.Core.Oracle.Domain;
using VnisCore.ExportPdf.Infrastructure.Abstractions;
using VnisCore.ExportPdf.Infrastructure.Business;
using VnisCore.ExportPdf.Infrastructure.UnOfficial.Abstractions;
using VnisCore.ExportPdf.Infrastructure.UnOfficial.Interfaces;
using VnisCore.ExportPdf.Infrastructure.UnOfficial.Repositories;
using VnisCore.ExportPdf.Infrastructure.UnOfficial.Services.Invoice01;
using VnisCore.ExportPdf.Infrastructure.UnOfficial.Services.Invoice02;
using VnisCore.ExportPdf.Infrastructure.UnOfficial.Services.Invoice03;
using VnisCore.ExportPdf.Infrastructure.UnOfficial.Services.Invoice04;
using VnisCore.ExportPdf.Infrastructure.UnOfficial.Services.Tbss;
using VnisCore.ExportPdf.Infrastructure.UnOfficial.Services.Ticket;
using VnisCore.Invoice01.Infrastructure;
using VnisCore.PILetter.Infrastructure;
using VnisCore.PITDeductionDocument.Infrastructure;

namespace VnisCore.ExportPdf.Infrastructure
{
    [DependsOn(
        typeof(AbpDddApplicationModule),
        typeof(VnisCoreOracleDomainModule),
        typeof(VnisCoreOracleModuleContractsModule),
        typeof(VnisCoreMongoDbModule),
        typeof(AbpAutoMapperModule),
        typeof(SharedModule),
        typeof(VnisCoreMongoDbModule),
        typeof(VnisCorePITDeductionDocumentInfrastructureModule),
        typeof(VnisCorePITLetterInfrastructureModule),
        typeof(VnisCoreInvoice01InfrastructureModule)
    )]
    public class VnisCoreExportPdfInfrastructureModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            SqlMapper.RemoveTypeMap(typeof(Guid));
            SqlMapper.RemoveTypeMap(typeof(Guid?));
            SqlMapper.AddTypeHandler(typeof(Guid), new GuidTypeHandler());
            SqlMapper.AddTypeHandler(typeof(Dictionary<string, string>), new DictionaryTypeHandler());

            context.Services.AddAutoMapperObjectMapper<VnisCoreExportPdfInfrastructureModule>();
            Configure<AbpAutoMapperOptions>(options =>
            {
                options.AddProfile<VnisCoreExportPdfInfrastructureAutoMapperProfile>(validate: false);
            });
            context.Services.AddTransient(typeof(IAppFactory), typeof(AppFactory));
            context.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(RequestPreProcessorBehavior<,>));
            context.Services.AddMediatR(typeof(VnisCoreExportPdfInfrastructureModule).GetTypeInfo().Assembly);

            //Repositories
            context.Services.AddScoped(typeof(IInvoiceHeaderRepository<>), typeof(InvoiceHeaderRepository<>));
            context.Services.AddScoped(typeof(IInvoiceDetailRepository<>), typeof(InvoiceDetailRepository<>));
            context.Services.AddScoped(typeof(IInvoiceReferenceRepository<>), typeof(InvoiceReferenceRepository<>));
            context.Services.AddScoped(typeof(IInvoiceDetailFieldRepository<>), typeof(InvoiceDetailFieldRepository<>));
            context.Services.AddScoped(typeof(IInvoiceDocumentInfoRepository<>), typeof(InvoiceDocumentInfoRepository<>));
            context.Services.AddScoped(typeof(IInvoiceHeaderExtraRepository<>), typeof(InvoiceHeaderExtraRepository<>));
            context.Services.AddScoped(typeof(IInvoiceDetailExtraRepository<>), typeof(InvoiceDetailExtraRepository<>));
            context.Services.AddScoped(typeof(IInvoiceHeaderFieldRepository<>), typeof(InvoiceHeaderFieldRepository<>));
            context.Services.AddScoped(typeof(IInvoiceXmlRepository<>), typeof(InvoiceXmlRepository<>));
            context.Services.AddScoped(typeof(IInvoiceUnOfficialRepository<>), typeof(InvoiceUnOfficialRepository<>));
            context.Services.AddScoped(typeof(IMediaRepository<>), typeof(MediaRepository<>));
            context.Services.AddScoped<IInvoice01HeaderFieldRepository, Invoice01HeaderFieldRepository>();
            context.Services.AddScoped<IInvoice01DetailFieldRepository, Invoice01DetailFieldRepository>();
            context.Services.AddScoped<IInvoice01TaxBreakdownRepository, Invoice01TaxBreakdownRepository>();
            context.Services.AddScoped<IInvoice05TaxBreakdownRepository, Invoice05TaxBreakdownRepository>();


            //Services
            //context.Services.AddScoped<IInvoiceDownloadService, InvoiceDownloadService>();

            context.Services.AddScoped<IFileService, FileService>();
            context.Services.AddScoped<IPITPdfService, PITPdfService>();
            context.Services.AddScoped<IPdfService, PdfService>();
            context.Services.AddScoped<IPdfInvoiceErrorService, PdfInvoiceErrorService>();
            context.Services.AddScoped<ISemaphore, HtmlToPdfSemaphore>();
            context.Services.AddScoped<IMediaTemplateDesignService, MediaTemplateDesignService>();
            //context.Services.AddScoped<IInvoiceUnOfficialService, InvoiceUnOfficialService>();

            context.Services.AddScoped<IDownloadUnOfficialFactory, DownloadUnOfficialFactory>();
            context.Services.AddScoped<IDownloadUnOfficialService, DownloadUnOfficialInvoice01Service>();
            context.Services.AddScoped<IDownloadUnOfficialService, DownloadUnOfficialInvoice02Service>();
            context.Services.AddScoped<IDownloadUnOfficialService, DownloadUnOfficialInvoice03Service>();
            context.Services.AddScoped<IDownloadUnOfficialService, DownloadUnOfficialInvoice04Service>();
            context.Services.AddScoped<IDownloadUnOfficialService, DownloadUnOfficialInvoice05Service>();

            context.Services.AddScoped<IDownloadOfficialFactory, DownloadOfficialFactory>();
            context.Services.AddScoped<IDownloadOfficialService, DownloadOfficialInvoice01Service>();
            context.Services.AddScoped<IDownloadOfficialService, DownloadOfficialInvoice02Service>();
            context.Services.AddScoped<IDownloadOfficialService, DownloadOfficialInvoice03Service>();
            context.Services.AddScoped<IDownloadOfficialService, DownloadOfficialInvoice04Service>();
            context.Services.AddScoped<IDownloadOfficialService, DownloadOfficialInvoice05Service>();

            context.Services.AddScoped<IDownloadXmlFactory, DownloadXmlFactory>();
            context.Services.AddScoped<IDownloadXmlService, DownloadXmlInvoice01Service>();
            context.Services.AddScoped<IDownloadXmlService, DownloadXmlInvoice02Service>();
            context.Services.AddScoped<IDownloadXmlService, DownloadXmlInvoice03Service>();
            context.Services.AddScoped<IDownloadXmlService, DownloadXmlInvoice04Service>();
            context.Services.AddScoped<IDownloadXmlService, DownloadXmlInvoice05Service>();

            context.Services.AddScoped<IDownloadInvoiceErrorFactory, DownloadInvoiceErrorFactory>();
            context.Services.AddScoped<IDownloadInvoiceErrorService, DownloadInvoiceErrorInvoice01Service>();
            context.Services.AddScoped<IDownloadInvoiceErrorService, DownloadInvoiceErrorInvoice01Service>();
            context.Services.AddScoped<IDownloadInvoiceErrorService, DownloadInvoiceErrorInvoice02Service>();
            context.Services.AddScoped<IDownloadInvoiceErrorService, DownloadInvoiceErrorInvoice03Service>();
            context.Services.AddScoped<IDownloadInvoiceErrorService, DownloadInvoiceErrorInvoice04Service>();
            context.Services.AddScoped<IDownloadInvoiceErrorService, DownloadInvoiceErrorInvoice05Service>();
            context.Services.AddScoped<IDownloadTbssService, DownloadTbssService>(); // IN Tbss cho hóa đơn ngoài hệ thống

            // business
            context.Services.AddScoped<IPITExportPdfBusiness, PITExportPdfBusiness>();
            context.Services.AddScoped<IPITExportXmlBusiness, PITExportXmlBusiness>();
            context.Services.AddScoped<IPITLetterExportXmlBusiness, PITLetterExportXmlBusiness>();
            context.Services.AddScoped<IInvoiceErrorExportXmlBusiness, InvoiceErrorExportXmlBusiness>();
            context.Services.AddScoped<ITbssDownloadXmlBusiness, TbssDownloadXmlBusiness>();
            context.Services.AddScoped<IInvoiceUnofficialBusiness, InvoiceUnofficialBusiness>();
            context.Services.AddScoped<IInvoiceDownloadBusiness, InvoiceDownloadBusiness>();
        }
    }
}