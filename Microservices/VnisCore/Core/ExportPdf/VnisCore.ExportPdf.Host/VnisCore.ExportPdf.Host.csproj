<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
    <LangVersion>latest</LangVersion>
    <Version>5.32.0</Version>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\..\..\Framework\Core\Core.Swashbuckle\Core.Swashbuckle.csproj" />
    <ProjectReference Include="..\..\..\..\..\Framework\Shared\Core.Host.Shared\Core.Host.Shared.csproj" />
    <ProjectReference Include="..\..\..\..\..\Framework\Shared\Core.VaultSharp\Core.VaultSharp.csproj" />
    <ProjectReference Include="..\Modules\VnisCore.ExportPdf.Application\VnisCore.ExportPdf.Application.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="wwwroot\temlateFile\PITLetterTemplate\" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="wwwroot\TemplateFile\FlexCel\PITDeductionDocumentDeclaration\PITDeductionDocumentDeclarationPdfTemplate.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>
