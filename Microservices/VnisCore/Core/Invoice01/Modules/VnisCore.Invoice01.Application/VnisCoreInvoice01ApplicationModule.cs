using Core.Application;
using Core.AutoMapper;
using Core.ElasticSearch.Shared;
using Core.Modularity;
using Core.Shared;
using Core.Shared.DapperSqlTypeHandler;
using Core.Shared.ElasticSearch;
using Core.Shared.Factory;
using Core.Shared.Invoice;
using Core.Shared.Pdf.Interfaces;
using Core.Shared.Pdf.Services;
using Core.Shared.Services;
using Core.Shared.Validations;

using Dapper;
using Einvoice.Module.Invoice01.ValidationRules.CreateAdjust;
using Einvoice.Module.Invoice01.ValidationRules.CreateAdjustmentDetail;
using Einvoice.Module.Invoice01.ValidationRules.CreateAdjustmentHeader;
using Einvoice.Module.Invoice01.ValidationRules.UpdateManyAdjust;
using MediatR;
using MediatR.Pipeline;

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Reflection;

using VnisCore.Core.MongoDB;
using VnisCore.Core.Oracle.Application.Contracts;
using VnisCore.Core.Oracle.Domain;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Invoice01.Application.Factories.Caching;
using VnisCore.Invoice01.Application.Factories.Repositories;
using VnisCore.Invoice01.Application.Factories.Services;
using VnisCore.Invoice01.Application.Invoice01.Dto;
using VnisCore.Invoice01.Application.Invoice01.Models.Requests.Commands;
using VnisCore.Invoice01.Application.Invoice01.Services;
using VnisCore.Invoice01.Application.Invoice01.ValidationRule.Create;
using VnisCore.Invoice01.Application.Invoice01.ValidationRule.CreateAdjust;
using VnisCore.Invoice01.Application.Invoice01.ValidationRule.CreateAdjustmentDetail;
using VnisCore.Invoice01.Application.Invoice01.ValidationRule.CreateAdjustmentHeader;
using VnisCore.Invoice01.Application.Invoice01.ValidationRule.CreateByGroupCustomer;
using VnisCore.Invoice01.Application.Invoice01.ValidationRule.CreateOldDecree;
using VnisCore.Invoice01.Application.Invoice01.ValidationRule.CreateReplace;
using VnisCore.Invoice01.Application.Invoice01.ValidationRule.Update;
using VnisCore.Invoice01.Application.Invoice01.ValidationRule.UpdateAdjustmentDetail;
using VnisCore.Invoice01.Application.Invoice01.ValidationRule.UpdateAdjustmentHeader;
using VnisCore.Invoice01.Application.Invoice01.ValidationRule.UpdateManyAdjust;
using VnisCore.Invoice01.Application.Invoice01.ValidationRule.UpdateOldDcree;
using VnisCore.Invoice01.Application.Invoice01.ValidationRule.UpdateOldDecree;
using VnisCore.Invoice01.Application.Invoice01.ValidationRule.UpdateReplace;
using VnisCore.Invoice01.Application.Invoice01.ValidationRules.Approve;
using VnisCore.Invoice01.Application.Invoice01.ValidationRules.Cancel;
using VnisCore.Invoice01.Application.Invoice01.ValidationRules.CancelApprove;
using VnisCore.Invoice01.Application.Invoice01.ValidationRules.CreateOldDecree;
using VnisCore.Invoice01.Application.Invoice01.ValidationRules.Delete;
using VnisCore.Invoice01.Application.Invoice01.ValidationRules.Update;
using VnisCore.Invoice01.Application.Invoice01.ValidationRules.UpdateAdjustmentDetail;
using VnisCore.Invoice01.Application.Invoice01.ValidationRules.UpdateAdjustmentHeader;
using VnisCore.Invoice01.Application.Invoice01.ValidationRules.UpdateOldDecree;
using VnisCore.Invoice01.Application.Invoice01.ValidationRules.UpdateReplace;
using VnisCore.Invoice01.Application.Invoice01Document.Interfaces;
using VnisCore.Invoice01.Application.Invoice01Document.Services;
using VnisCore.Invoice01.Infrastructure;

namespace VnisCore.Invoice01.Application
{
    [DependsOn(
        typeof(AbpDddApplicationModule),
        typeof(VnisCoreOracleDomainModule),
        typeof(VnisCoreOracleModuleContractsModule),
        typeof(AbpAutoMapperModule),
        typeof(SharedModule),
        typeof(SharedInvoiceModule),
        typeof(VnisCoreMongoDbModule),
        typeof(ElasticSearchSharedModule),
        typeof(VnisCoreInvoice01InfrastructureModule)
    )]
    public class VnisCoreInvoice01ApplicationModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            SqlMapper.RemoveTypeMap(typeof(Guid));
            SqlMapper.RemoveTypeMap(typeof(Guid?));
            SqlMapper.AddTypeHandler(typeof(Guid), new GuidTypeHandler());
            SqlMapper.AddTypeHandler(typeof(Dictionary<string, string>), new DictionaryTypeHandler());

            context.Services.AddScoped<IValidatorFactory, ValidatorFactory>();
            context.Services.AddScoped<IValidationContext, ValidationContext>();

            context.Services.AddSingleton<LockerStore, LockerStore>();
            context.Services.AddScoped<ITaxService, TaxService>();
            context.Services.AddScoped<IInvoice01Service, Invoice01Service>();
            context.Services.AddScoped<ICommonInvoice01Service, CommonInvoice01Service>();
            context.Services.AddScoped<IInvoice01TaxBreakdownRepository, Invoice01TaxBreakdownRepository>();
            context.Services.AddScoped<INumberService<Invoice01HeaderEntity>, NumberService<Invoice01HeaderEntity>>();
            context.Services.AddScoped<IInvoiceService<Invoice01HeaderEntity, Invoice01HeaderFieldEntity, Invoice01DetailFieldEntity>, InvoiceService<Invoice01HeaderEntity, Invoice01HeaderFieldEntity, Invoice01DetailFieldEntity>>();
            context.Services.AddScoped<IInvoiceHeaderRepository<Invoice01HeaderEntity>, InvoiceHeaderRepository<Invoice01HeaderEntity>>();
            context.Services.AddScoped<IInvoiceHeaderExtraRepository<Invoice01HeaderExtraEntity>, InvoiceHeaderExtraRepository<Invoice01HeaderExtraEntity>>();
            context.Services.AddScoped<IInvoiceHeaderFieldRepository<Invoice01HeaderFieldEntity>, InvoiceHeaderFieldRepository<Invoice01HeaderFieldEntity>>();
            context.Services.AddScoped<IInvoiceDetailRepository<Invoice01DetailEntity>, InvoiceDetailRepository<Invoice01DetailEntity>>();
            context.Services.AddScoped<IInvoiceDetailExtraRepository<Invoice01DetailExtraEntity>, InvoiceDetailExtraRepository<Invoice01DetailExtraEntity>>();
            context.Services.AddScoped<IInvoiceDetailFieldRepository<Invoice01DetailFieldEntity>, InvoiceDetailFieldRepository<Invoice01DetailFieldEntity>>();
            context.Services.AddScoped<IInvoiceReferenceRepository<Invoice01ReferenceEntity>, InvoiceReferenceRepository<Invoice01ReferenceEntity>>();
            context.Services.AddScoped<IInvoiceReferenceOldDecreeRepository<Invoice01ReferenceOldDecreeEntity>, InvoiceReferenceOldDecreeRepository<Invoice01ReferenceOldDecreeEntity>>();
            context.Services.AddScoped<IInvoiceDocumentInfoRepository<Invoice01DocumentInfoEntity>, InvoiceDocumentInfoRepository<Invoice01DocumentInfoEntity>>();
            context.Services.AddScoped<ICurrencyRepository, CurrencyRepository>();
            context.Services.AddScoped<IInvoiceTemplateRepository, InvoiceTemplateRepository>();
            context.Services.AddScoped<IInvoice01HeaderRepository, Invoice01HeaderRepository>();
            context.Services.AddScoped(typeof(IInvoiceDocumentRepository<>), typeof(InvoiceDocumentRepository<>));
            context.Services.AddScoped<IRegistrationInvoiceService, RegistrationInvoiceService>();
            context.Services.AddScoped<ISendMailHttpClientService, SendMailHttpClientService>();
            context.Services.AddScoped<ISendMailHttpClient, SendMailHttpClient>();
            context.Services.AddScoped<ILicenseSharedService, LicenseSharedService>();

            context.Services.AddScoped<ElasticSearch, ElasticSearch>();

            context.Services.AddScoped<ICachedInvoiceBusiness, CachedInvoiceBusiness>();

            //pdf service
            context.Services.AddScoped<IPdfService, PdfService>();
            context.Services.AddScoped<ISemaphore, HtmlToPdfSemaphore>();
            context.Services.AddScoped<IMediaTemplateDesignService, MediaTemplateDesignService>();
            context.Services.AddScoped<IInvoiceDocumentFactory, InvoiceDocumentFactory>();
            context.Services.AddScoped<IPdfInvoiceDocumentService, PdfInvoiceDocumentService>();
            context.Services.AddScoped<IInvoiceDocumentService, Invoice01DocumentService>();
            
            //Import Service
            context.Services.AddScoped<IImport01Service, Import01Service>();
            context.Services.AddScoped<IImport01Service, PhonThinhImport01Service>();
            context.Services.AddScoped<IImport01Service, HoaSenImport01Service>();
            context.Services.AddScoped<IImport01Service, ImportInvoice01WithSettingService>();

            AddInvoice01Validators(context.Services);

            var configuration = context.Services.GetConfiguration();
            context.Services.AddAutoMapperObjectMapper<VnisCoreInvoice01ApplicationModule>();
            Configure<AbpAutoMapperOptions>(options =>
            {
                options.AddMaps<VnisCoreInvoice01ApplicationAutoMapperProfile>(validate: false);
            });
            context.Services.AddTransient(typeof(IAppFactory), typeof(AppFactory));
            context.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(RequestPreProcessorBehavior<,>));
            context.Services.AddMediatR(typeof(VnisCoreInvoice01ApplicationModule).GetTypeInfo().Assembly);
        }

        public static void AddExportPdfServices(IServiceCollection services, IConfiguration configuration)
        {
            //HttpClients
            services.AddHttpClient("exportpdfs", options =>
            {
                options.BaseAddress = new Uri(configuration.GetSection("Microservices:ExportPdf:Endpoint").Value);
                options.Timeout = TimeSpan.FromMinutes(int.Parse(configuration.GetSection("Microservices:ExportPdf:Timeout").Value));
            }).ConfigurePrimaryHttpMessageHandler(() =>
            {
                var handler = new HttpClientHandler();
                handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => { return true; };
                return handler;
            });

            services.AddSingleton<IExportPdfHttpClient, ExportPdfGrpc>();
        }

        public static void AddSendMailServices(IServiceCollection services, IConfiguration configuration)
        {
            //HttpClients
            services.AddHttpClient("sendmail", options =>
            {
                options.BaseAddress = new Uri(configuration.GetSection("Microservices:SendMail:Endpoint").Value);
                options.Timeout = TimeSpan.FromMinutes(int.Parse(configuration.GetSection("Microservices:SendMail:Timeout").Value));
            }).ConfigurePrimaryHttpMessageHandler(() =>
            {
                var handler = new HttpClientHandler();
                handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => { return true; };
                return handler;
            });

            services.AddSingleton<ISendMailHttpClient, SendMailHttpClient>();
        }

        public static void AddInvoice01Validators(IServiceCollection services)
        {
            services.AddConfigValidator<CreateInvoice01HeaderDto>(new List<Type>
            {
                typeof(CreateInvoiceCheckBuyerInformationRule),
                typeof(CreateInvoiceCheckTaxBreakDownRule),
                typeof(CreateInvoicePreProcess),
                //typeof(CreateInvoiceCheckCashierCodeRule),
                typeof(CreateInvoiceCheckLicenseRule),
                typeof(CreateInvoiceCheckExistTenantInfoRule),
                typeof(CreateInvoiceCheckExistTemplateRule),
                typeof(CreateInvoiceCheckRegisterInvoiceTemplateRul),
                typeof(CreateInvoiceCheckTemplateCreateRule),
                typeof(CreateInvoiceCheckInvoiceDateRangeRule),
                typeof(CreateInvoiceCheckPaymentMethodRule),
                typeof(CreateInvoiceCheckExistCurrencyRule),
                typeof(CreateInvoiceCheckExchangeRateRule),
                typeof(CreateInvoiceCheckExistTaxRule),
                typeof(CreateInvoiceCheckHeaderExtraRule),
                typeof(CreateInvoiceCheckProductTypeRule),
                typeof(CreateInvoiceCheckDetailExtraRule),
                typeof(CreateInvoiceCheckPendingLastInvoiceDateRule),
            });

            services.AddConfigValidator<CreateInvoice01HeaderOldDecreeDto>(new List<Type>
            {
                typeof(CreateInvoiceOldDecreeCheckBuyerInformationRule),
                typeof(CreateInvoiceOldDecreeCheckTaxBreakDownRule),
                typeof(CreateInvoiceOldDecreePreProcess),
                typeof(UpdateOldDecreeCheckInvoiceDateAfterUpdateRegistrationRule),
                typeof(CreateInvoiceOldDecreeCheckInvoiceReferenceRule),
                typeof(CreateInvoiceOldDecreeCheckExistInvoiceReferenceRule),
                typeof(CreateInvoiceOldDecreeCheckLicenseRule),
                typeof(CreateInvoiceOldDecreeCheckExistTenantInfoRule),
                typeof(CreateInvoiceOldDecreeCheckExistTemplateRule),
                typeof(CreateInvoiceOldDecreeCheckRegisterInvoiceTemplateRule),
                typeof(CreateInvoiceOldDecreeCheckTemplateCreateRule),
                typeof(CreateInvoiceOldDecreeCheckInvoiceDateRangeRule),
                typeof(CreateInvoiceOldDecreeCheckPaymentMethodRule),
                typeof(CreateInvoiceOldDecreeCheckExistCurrencyRule),
                typeof(CreateInvoiceOldDecreeCheckExchangeRateRule),
                typeof(CreateInvoiceOldDecreeCheckExistTaxRule),
                typeof(CreateInvoiceOldDecreeCheckHeaderExtraRule),
                typeof(CreateInvoiceOldDecreeCheckProductTypeRule),
                typeof(CreateInvoiceOldDecreeCheckDetailExtraRule),
                typeof(CreateInvoiceOldDecreeCheckMoneyRule),
                typeof(CreateInvoiceOldDecreeCheckPendingLastInvoiceDateRule),
            });


            services.AddConfigValidator<CreateInvoice01ByGroupCustomerDto>(new List<Type>
            {
                typeof(CreateInvoiceByGroupCustomerPreProcess),
                typeof(CreateInvoiceByGroupCustomerCheckTaxBreakdownRule),
                //typeof(CreateInvoiceByGroupCustomerCheckCashierCodeRule),
                typeof(CreateInvoiceByGroupCustomerCheckCustomerRule),
                typeof(CreateInvoiceByGroupCustomerCheckLicenseRule),
                typeof(CreateInvoiceByGroupCustomerCheckExistTenantInfoRule),
                typeof(CreateInvoiceByGroupCustomerCheckExistTemplateRule),
                typeof(CreateInvoiceByGroupCustomerCheckTemplateCreateRule),
                typeof(CreateInvoiceByGroupCustomerCheckInvoiceDateRangeRule),
                typeof(CreateInvoiceByGroupCustomerCheckExistCurrencyRule),
                typeof(CreateInvoiceByGroupCustomerCheckExchangeRateRule),
                typeof(CreateInvoiceByGroupCustomerCheckPaymentMethodRule),
                typeof(CreateInvoiceByGroupCustomerCheckExistTaxRule),
                typeof(CreateInvoiceByGroupCustomerCheckHeaderExtraRule),
                typeof(CreateInvoiceByGroupCustomerCheckProductTypeRule),
                typeof(CreateInvoiceByGroupCustomerCheckDetailExtraRule),
                typeof(CreateInvoiceByGroupCustomerCheckPendingLastInvoiceDateRule),
            });

            services.AddConfigValidator<CreateReplaceInvoice01Dto>(new List<Type>
            {
                typeof(CreateReplaceInvoiceCheckBuyerInformationRule),
                typeof(CreateReplaceInvoiceCheckTaxBreakDownRule),
                typeof(CreateReplaceInvoicePreProcess),
                //typeof(CreateReplaceInvoiceCheckCashierCodeRule),
                typeof(CreateReplaceInvoiceCheckLicenseRule),
                typeof(CreateReplaceInvoiceCheckReferenceInvoiceRule),
                typeof(CreateReplaceInvoiceCheckDocumentDateRule),
                typeof(CreateReplaceInvoiceCheckRegisterInvoiceTemplateRul),
                typeof(CreateReplaceInvoiceCheckExistTemplateRule),
                typeof(CreateReplaceInvoiceCheckTemplateCreateRule),
                typeof(CreateReplaceInvoiceCheckChangeSerialRule),
                typeof(CreateReplaceInvoiceCheckInvoiceDateRangeRule),
                typeof(CreateReplaceInvoiceCheckExistCurrencyRule),
                typeof(CreateReplaceInvoiceCheckExchangeRateRule),
                typeof(CreateReplaceInvoiceCheckPaymentMethodRule),
                typeof(CreateReplaceInvoiceCheckExistTaxRule),
                typeof(CreateReplaceInvoiceCheckHeaderExtraRule),
                typeof(CreateReplaceInvoiceCheckProductTypeRule),
                typeof(CreateReplaceInvoiceCheckDetailExtraRule),
                typeof(CreateReplaceInvoiceCheckPendingLastInvoiceDateRule),
            });

            services.AddConfigValidator<CreateAdjustmentHeaderInvoice01HeaderDto>(new List<Type>
            {
                typeof(CreateInvoiceAdjustCheckTaxBreakDownCreateRule),
                typeof(CreateInvoiceAdjustmentHeaderPreProcess),
                //typeof(CreateInvoiceAdjustmentHeaderCheckCashierCodeRule),
                //typeof(CreateInvoiceAdjustmentHeaderCheckInvoiceBackDateRule),
                typeof(CreateInvoiceAdjustmentHeaderCheckLicenseRule),
                typeof(CreateInvoiceAdjustmentHeaderCheckReferenceInvoiceRule),
                typeof(CreateAdjustmentInvoiceCheckRegisterInvoiceTemplateRul),
                typeof(Einvoice.Module.Invoice01.ValidationRules.CreateAdjustmentHeader.CreateInvoiceAdjustmentDetailCheckDocumentDateRule),
                typeof(CreateInvoiceAdjustmentHeaderCheckExistTemplateRule),
                typeof(CreateInvoiceAdjustmentHeaderCheckTemplateCreateRule),
                typeof(CreateInvoiceAdjustmentHeaderCheckPaymentMethodRule),
                typeof(CreateInvoiceAdjustmentHeaderCheckChangeSerialRule),
                typeof(CreateInvoiceAdjustmentHeaderCheckInvoiceDateRangeRule),
                //typeof(CreateInvoiceAdjustmentHeaderCheckDocumentDateRule),
                //typeof(CreateInvoiceAdjustmentHeaderCheckDuplicateBuyerCodeRule),
                typeof(CreateInvoiceAdjustmentHeaderCheckHeaderExtraRule),
                typeof(CreateInvoiceAdjustmentHeaderCheckPendingLastInvoiceDateRule),
            });

            services.AddConfigValidator<CreateInvoice01AdjustmentDetailRequest>(new List<Type>
            {
                typeof(CreateInvoiceAdjustmentDetailPreProcess),
                //typeof(CreateInvoiceAdjustmentDetailCheckCashierCodeRule),
                typeof(CreateInvoiceAdjustmentDetailCheckLicenseRule),
                typeof(CreateInvoiceAdjustmentDetailCheckReferenceInvoiceRule),
                typeof(Einvoice.Module.Invoice01.ValidationRules.CreateAdjustmentDetail.CreateInvoiceAdjustmentDetailCheckDocumentDateRule),
                typeof(CreateInvoiceAdjustmentDetailCheckExistTemplateRule),
                typeof(CreateInvoiceAdjustmentDetailCheckExistTaxRule),
                typeof(CreateInvoiceAdjustmentDetailCheckTemplateCreateRule),
                typeof(CreateInvoiceAdjustmentDetailCheckChangeSerialRule),
                typeof(CreateInvoiceAdjustmentDetailCheckInvoiceDateRangeRule),
                typeof(CreateInvoiceAdjustmentDetailCheckProductTypeRule),
                typeof(CreateInvoiceAdjustmentDetailCheckDetailExtraRule),
                typeof(CreateInvoiceAdjustmentDetailCheckPendingLastInvoiceDateRule),
            });

            services.AddConfigValidator<UpdateInvoice01HeaderDto>(new List<Type>
            {
                typeof(UpdateInvoiceCheckBuyerInformationRule),
                typeof(UpdateInvoiceCheckTaxBreakdownRule),
                typeof(UpdateInvoicePreProcess),
                typeof(UpdateInvoiceCheckInvoiceDateAfterUpdateRegistrationRule),
                //typeof(UpdateInvoiceCheckCashierCodeRule),
                typeof(UpdateInvoiceCheckStatusRule),
                typeof(UpdateInvoiceCheckInvoiceDateRangeRule),
                typeof(UpdateInvoiceCheckExistCurrencyRule),
                typeof(UpdateInvoiceCheckExchangeRateRule),
                typeof(UpdateInvoiceCheckPaymentMethodRule),
                typeof(UpdateInvoiceCheckExistTaxRule),
                typeof(UpdateInvoiceCheckHeaderExtraRule),
                typeof(UpdateInvoiceCheckProductTypeRule),
                typeof(UpdateInvoiceCheckDetailExtraRule),
                typeof(UpdateInvoiceCheckPendingLastInvoiceDateRule),
            });

            services.AddConfigValidator<UpdateInvoice01HeaderOldDecreeDto>(new List<Type>
            {
                typeof(UpdateInvoiceOldDecreeCheckBuyerInformationRule),
                typeof(UpdateInvoiceOldDecreeCheckTaxBreakdownRule),
                typeof(UpdateInvoiceOldDecreePreProcess),
                typeof(UpdateOldDecreeCheckInvoiceDateAfterUpdateRegistrationRule),
                typeof(UpdateInvoiceOldDecreeCheckStatusRule),
                typeof(UpdateInvoiceOldDecreeCheckInvoiceReferenceRule),
                typeof(UpdateInvoiceOldDecreeCheckInvoiceDateRangeRule),
                typeof(UpdateInvoiceOldDecreeCheckUnitPriceRule),
                typeof(UpdateInvoiceOldDecreeCheckExistCurrencyRule),
                typeof(UpdateInvoiceOldDecreeCheckExchangeRateRule),
                typeof(UpdateInvoiceOldDecreeCheckPaymentMethodRule),
                typeof(UpdateInvoiceOldDecreeCheckExistTaxRule),
                typeof(UpdateInvoiceOldDecreeCheckHeaderExtraRule),
                typeof(UpdateInvoiceOldDecreeCheckProductTypeRule),
                typeof(UpdateInvoiceOldDecreeCheckDetailExtraRule),
                typeof(UpdateInvoiceOldDecreeCheckMoneyRule),
                typeof(UpdateInvoiceOldDecreeCheckPendingLastInvoiceDateRule),
            });

            services.AddConfigValidator<UpdateReplaceInvoice01HeaderDto>(new List<Type>
            {
                typeof(UpdateReplaceInvoiceCheckBuyerInformationRule),
                typeof(UpdateReplaceInvoiceCheckTaxBreakdownRule),
                typeof(UpdateReplaceInvoicePreProcess),
                typeof(UpdateReplaceCheckInvoiceDateAfterUpdateRegistrationRule),
                //typeof(UpdateReplaceInvoiceCheckCashierCodeRule),
                typeof(UpdateReplaceInvoiceCheckStatusRule),
                typeof(UpdateReplaceInvoiceCheckInvoiceDateRangeRule),
                typeof(UpdateReplaceInvoiceCheckDocumentDateRule),
                typeof(UpdateReplaceInvoiceCheckExistCurrencyRule),
                typeof(UpdateReplaceInvoiceCheckExchangeRateRule),
                typeof(UpdateReplaceInvoiceCheckPaymentMethodRule),
                //typeof(UpdateReplaceInvoiceCheckDuplicateBuyerCodeRule),
                typeof(UpdateReplaceInvoiceCheckExistTaxRule),
                typeof(UpdateReplaceInvoiceCheckHeaderExtraRule),
                typeof(UpdateReplaceInvoiceCheckProductTypeRule),
                typeof(UpdateReplaceInvoiceCheckDetailExtraRule),
                typeof(UpdateReplaceInvoiceCheckPendingLastInvoiceDateRule),
            });

            services.AddConfigValidator<UpdateAdjustmentHeaderInvoice01Dto>(new List<Type>
            {
                typeof(UpdateAdjustmentHeaderInvoicePreProcess),
                typeof(UpdateAdjHeaderCheckInvoiceDateAfterUpdateRegistrationRule),
                //typeof(UpdateAdjustmentHeaderInvoiceCheckCashierCodeRule),
                typeof(UpdateAdjustmentHeaderInvoiceCheckStatusRule),
                typeof(UpdateAdjustmentHeaderInvoiceCheckInvoiceDateRangeRule),
                typeof(UpdateAdjustmentHeaderInvoiceCheckPaymentMethodRule),
                typeof(UpdateAdjustmentHeaderInvoiceCheckDocumentDateRule),
                //typeof(UpdateAdjustmentHeaderInvoiceCheckDuplicateBuyerCodeRule),
                typeof(UpdateAdjustmentHeaderInvoiceCheckHeaderExtraRule),
                typeof(UpdateAdjustmentHeaderInvoiceCheckPendingLastInvoiceDateRule),
            });

            services.AddConfigValidator<UpdateAdjustmentDetailInvoice01Request>(new List<Type>
            {
                typeof(UpdateAdjustmentDetailInvoicePreProcess),
                typeof(UpdateAdjDetailInvoiceCheckInvoiceDateAfterUpdateRegistrationRule),
                //typeof(UpdateAdjustmentDetailInvoiceCheckCashierCodeRule),
                typeof(UpdateAdjustmentDetailInvoiceCheckStatusRule),
                typeof(UpdateAdjustmentDetailInvoiceCheckInvoiceDateRangeRule),
                typeof(UpdateAdjustmentDetailInvoiceCheckDocumentDateRule),
                typeof(UpdateAdjustmentDetailInvoiceCheckExistTaxRule),
                typeof(UpdateAdjustmentDetailInvoiceCheckProductTypeRule),
                typeof(UpdateAdjustmentDetailInvoiceCheckDetailExtraRule),
                typeof(UpdateAdjustmentDetailInvoiceCheckPendingLastInvoiceDateRule),
            });


            services.AddConfigValidator<ApproveInvoice01RequestModel>(new List<Type>
            {
                typeof(ApproveInvoice01PreProcess),
                typeof(ApproveInvoice01CheckUserRule),
                typeof(ApproveInvoice01CheckStatusRule),
            });

            services.AddConfigValidator<CancelInvoice01HeaderDto>(new List<Type>
            {
                typeof(CancelInvoicePreProcess),
                typeof(CancelInvoiceCheckStatusRule),
            });

            services.AddConfigValidator<DeleteInvoice01HeaderDto>(new List<Type>
            {
                typeof(DeleteInvoicePreProcess),
                typeof(DeleteInvoiceCheckStatusRule),
                //typeof(DeleteInvoiceCheckCashierCodeRule),
            });

            services.AddConfigValidator<CancelApproveInvoice01HeaderDto>(new List<Type>
            {
                typeof(CancelApproveInvoice01PreProcess),
                typeof(CancelApproveInvoice01CheckStatusRule),
            });

            // Validator Điều chỉnh nhiều lần
            services.AddConfigValidator<CreateAdjustInvoice01HeaderDto>(new List<Type>
            {
                typeof(CreateInvoiceAdjustCheckBuyerInfomationCreateRule),
                typeof(CreateInvoiceAdjustCheckTaxBreakDownCreateRule),
                typeof(CreateInvoiceAdjustPreProcess),
                typeof(CreateInvoiceAdjustCheckLicenseRule),
                typeof(CreateInvoiceAdjustCheckReferenceInvoiceRule),
                typeof(CreateInvoiceAdjustCheckDocumentDateRule),
                typeof(CreateInvoiceAdjustCheckExistTemplateRule),
                typeof(CreateInvoiceAdjustCheckTemplateCreateRule),
                typeof(CreateInvoiceAdjustCheckPaymentMethodRule),
                typeof(CreateInvoiceAdjustCheckChangeSerialRule),
                typeof(CreateInvoiceAdjustCheckInvoiceDateRangeRule),
                typeof(CreateInvoiceAdjustCheckHeaderExtraRule),
                typeof(CreateAdjustInvoiceCheckExistCurrencyRule),
                typeof (CreateAdjustInvoiceCheckExistTaxRule),
                typeof (CreateInvoiceAdjustCheckProductTypeRule),
                typeof(CreateInvoiceAdjustCheckPendingLastInvoiceDateRule),
            });

            // Validator Điều chỉnh nhiều lần
            services.AddConfigValidator<UpdateManyAdjustInvoice01HeaderDto>(new List<Type>
            {
                typeof(UpdateInvoiceAdjustCheckBuyerInformationRule),
                typeof(UpdateInvoiceAdjustCheckTaxBreakdownRule),
                typeof(UpdateInvoiceAdjustPreProcess),
                //typeof(UpdateInvoiceAdjustCheckLicenseRule),
                typeof(UpdateInvoiceAdjustCheckReferenceInvoiceRule),
                typeof(UpdateInvoiceAdjustCheckExistTemplateRule),
                typeof(UpdateAdjustInvoiceCheckRegisterInvoiceTemplateRule),
                typeof(UpdateInvoiceAdjustCheckDocumentDateRule),
                typeof(UpdateInvoiceAdjustCheckTemplateCreateRule),
                typeof(UpdateInvoiceAdjustCheckPaymentMethodRule),
                //typeof(UpdateInvoiceAdjustCheckChangeSerialRule),
                typeof(UpdateInvoiceAdjustCheckInvoiceDateRangeRule),
                typeof(UpdateInvoiceAdjustCheckHeaderExtraRule),
                typeof(UpdateAdjustInvoiceCheckExistCurrencyRule),
                typeof (UpdateAdjustInvoiceCheckExistTaxRule),
                typeof (UpdateInvoiceAdjustmentDetailCheckProductTypeRule),
                typeof(UpdateInvoiceAdjustCheckPendingLastInvoiceDateRule),
            });
        }
    }


}
