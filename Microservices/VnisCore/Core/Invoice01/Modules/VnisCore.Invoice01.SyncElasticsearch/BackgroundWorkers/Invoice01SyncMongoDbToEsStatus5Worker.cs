using System.Threading.Tasks;
using Core.BackgroundWorkers;
using Core.Shared.Constants;
using Core.Threading;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using VnisCore.Invoice01.SyncElasticsearch.Interface;

namespace VnisCore.Invoice01.SyncElasticsearch.BackgroundWorkers
{
    public class Invoice01SyncMongoDbToEsStatus5Worker : AsyncPeriodicBackgroundWorkerBase
    {
        private readonly IConfiguration _configuration;

        public Invoice01SyncMongoDbToEsStatus5Worker(
            AbpAsyncTimer timer,
            IConfiguration configuration,
            IServiceScopeFactory serviceScopeFactory) :
            base(timer, serviceScopeFactory)
        {
            _configuration = configuration;
            Timer.Period = 1000; //30 s
            //int.TryParse(configuration.GetSection("Settings:TimePeriod").Value, out var period);
            //if (period > 0)
            //    timer.Period = period * 1000;
        }

        protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
        {
            await workerContext
                .ServiceProvider
                .GetRequiredService<IInvoice01SyncElasticsearchBusiness>()
                .SyncMongoDbToElasticsearch(SyncElasticSearchStatus.PendingSyncDelete.GetHashCode());

            //await workerContext
            //    .ServiceProvider
            //    .GetRequiredService<IInvoice01SyncESBusiness>()
            //    .SyncESAsync(SyncElasticSearchStatus.PendingSyncDelete.GetHashCode());
        }
    }
}