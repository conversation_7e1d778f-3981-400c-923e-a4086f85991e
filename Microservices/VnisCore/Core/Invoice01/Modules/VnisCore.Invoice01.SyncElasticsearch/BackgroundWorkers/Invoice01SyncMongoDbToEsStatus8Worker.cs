using System.Threading.Tasks;
using Core.BackgroundWorkers;
using Core.Shared.Constants;
using Core.Threading;
using Microsoft.Extensions.DependencyInjection;
using VnisCore.Invoice01.SyncElasticsearch.Interface;

namespace VnisCore.Invoice01.SyncElasticsearch.BackgroundWorkers
{
    public class Invoice01SyncMongoDbToEsStatus8Worker : AsyncPeriodicBackgroundWorkerBase
    {
        public Invoice01SyncMongoDbToEsStatus8Worker(
            AbpAsyncTimer timer,
            IServiceScopeFactory serviceScopeFactory) :
            base(timer, serviceScopeFactory)
        {
            Timer.Period = 1000; //30 s
            //int.TryParse(configuration.GetSection("Settings:TimePeriod").Value, out var period);
            //if (period > 0)
            //    timer.Period = period * 1000;
        }

        protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
        {
            await workerContext
                .ServiceProvider
                .GetRequiredService<IInvoice01SyncElasticsearchBusiness>()
                .SyncMongoDbToElasticsearch(SyncElasticSearchStatus.PendingSyncStatusTvanResponse.GetHashCode());

            //await workerContext
            //    .ServiceProvider
            //    .GetRequiredService<IInvoice01SyncESBusiness>()
            //    .SyncESAsync(SyncElasticSearchStatus.PendingSyncStatusTvanResponse.GetHashCode());
        }
    }
}