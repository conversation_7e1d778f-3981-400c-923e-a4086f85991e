using Core.BackgroundWorkers;
using Core.Shared.Constants;
using Core.Threading;

using Microsoft.Extensions.DependencyInjection;

using System.Threading.Tasks;

using VnisCore.Invoice01.SyncElasticsearch.Interface;

namespace VnisCore.Invoice01.SyncElasticsearch.BackgroundWorkers
{
    public class Invoice01SyncMongoToEsIsDeclaredWorker : AsyncPeriodicBackgroundWorkerBase
    {
        public Invoice01SyncMongoToEsIsDeclaredWorker(
            AbpAsyncTimer timer,
            IServiceScopeFactory serviceScopeFactory) : base(timer, serviceScopeFactory)
        {
            Timer.Period = 1000; //30 s
        }

        protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
        {
            await workerContext
                .ServiceProvider
                .GetRequiredService<IInvoice01SyncESBusiness>()
                .SyncESAsync(SyncElasticSearchStatus.PendingSyncIsDeclared.GetHashCode());
        }
    }
}
