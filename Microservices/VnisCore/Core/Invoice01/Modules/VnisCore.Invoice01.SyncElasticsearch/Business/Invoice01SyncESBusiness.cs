using Core.ElasticSearch.Shared.Invoices.Invoice01.IRepository;
using Core.Shared.Constants;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Serilog;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.MongoDB.IRepository;
using VnisCore.Invoice01.SyncElasticsearch.Configuration;
using VnisCore.Invoice01.SyncElasticsearch.Interface;

namespace VnisCore.Invoice01.SyncElasticsearch.Business
{
    public class Invoice01SyncESBusiness : IInvoice01SyncESBusiness
    {
        private readonly SettingsOptions _settingsOptions;
        private readonly IVnisCoreMongoInvoice01Repository _vnisCoreMongoInvoice01Repository;
        private readonly IConfiguration _configuration;
        private readonly IESInvoice01Repository _iESInvoice01Repository;

        public Invoice01SyncESBusiness(
            IOptions<SettingsOptions> settingsOptions,
            IConfiguration configuration,
            IVnisCoreMongoInvoice01Repository vnisCoreMongoInvoice01Repository,
            IESInvoice01Repository iESInvoice01Repository)
        {
            _vnisCoreMongoInvoice01Repository = vnisCoreMongoInvoice01Repository;
            _settingsOptions = settingsOptions.Value;
            _configuration = configuration;
            _iESInvoice01Repository = iESInvoice01Repository;
        }

        /// <summary>
        /// đồng bộ ES
        /// </summary>
        /// <returns></returns>
        public async Task SyncESAsync(int syncESStatus)
        {
            var tenantGroupIndexEs = "group-x";
            var tenantGroups = _settingsOptions.TenantGroupsPrivate.Split(",").Select(decimal.Parse).ToList();
            if (_settingsOptions.IsEnableTenantGroupPrivate > 0)
            {
                tenantGroupIndexEs = $"group-{_settingsOptions.TenantGroup}";
                tenantGroups = new List<decimal> { decimal.Parse(_settingsOptions.TenantGroup) };
            }

            int.TryParse(_configuration["Settings:MaxDocumentIndexEs"], out var maxDocumentIndexEs);
            if (maxDocumentIndexEs <= 0)
                maxDocumentIndexEs = 100; // giá trị mặc định nếu ko cấu hình

            var data = await _vnisCoreMongoInvoice01Repository.Invoice01SyncToEs(tenantGroups, maxDocumentIndexEs, syncESStatus, _settingsOptions.IsEnableTenantGroupPrivate);
            if (!data.Any())
                return;

            switch (syncESStatus)
            {
                case (int)SyncElasticSearchStatus.PendingSyncApprove:
                    await _iESInvoice01Repository.UpdateEsApproveAsync(data, tenantGroupIndexEs);
                    break;
                case (int)SyncElasticSearchStatus.PendingSyncCancel:
                    await _iESInvoice01Repository.UpdateESCancelAsync(data, tenantGroupIndexEs);
                    break;
                case (int)SyncElasticSearchStatus.PendingSyncDelete:
                    await _iESInvoice01Repository.UpdateESDeleteAsync(data, tenantGroupIndexEs);
                    break;
                case (int)SyncElasticSearchStatus.PendingSyncSign:
                    await _iESInvoice01Repository.UpdateESSignStatusAsync(data, tenantGroupIndexEs);
                    break;
                //case (int)SyncElasticSearchStatus.PendingSyncStatusTvanRequest:
                //    break;
                case (int)SyncElasticSearchStatus.PendingSyncStatusTvanResponse:
                    await _iESInvoice01Repository.UpdateESVerificationCodeAsync(data, tenantGroupIndexEs);
                    break;
                //case (int)SyncElasticSearchStatus.PendingSyncCreateInvoiceError:
                //    break;
                case (int)SyncElasticSearchStatus.PendingSyncIsDeclared:
                    await _iESInvoice01Repository.UpdateESIsDeclaredAsync(data, tenantGroupIndexEs);
                    break;
                default:
                    Log.Error("Trạng thái đồng bộ không đúng");
                    break;
            }
        }
    }
}
