using Core.Data;
using Core.Dto.Shared.PrintNote;
using System;
using System.Collections.Generic;

namespace VnisCore.Invoice02.Application.Invoice02.Models
{
    public class Invoice02SpecificProductExtraModel
    {
        public long Id { get; set; }
        public long InvoiceHeaderId { get; set; }
        public long InvoiceDetailId { get; set; }
        public long SpecificProductFieldId { get; set; }
        public int Type { get; set; }
        public string FieldName { get; set; }
        public string FieldValue { get; set; }
    }
}
