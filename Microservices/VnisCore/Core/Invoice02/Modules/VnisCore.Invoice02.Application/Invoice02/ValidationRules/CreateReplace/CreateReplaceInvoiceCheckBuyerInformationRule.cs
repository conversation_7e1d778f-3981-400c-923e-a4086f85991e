using Core.Shared.Validations;
using System;
using System.Collections.Generic;
using VnisCore.Invoice02.Application.Invoice02.Dto;

namespace VnisCore.Invoice02.Application.Invoice02.ValidationRules.CreateReplace
{
    /// <summary>
    /// kiểm tra thông tin người mua
    /// </summary>
    public class CreateReplaceInvoiceCheckBuyerInformationRule : IValidationRule<CreateReplaceInvoice02Dto, ValidationResult>
    {

        public CreateReplaceInvoiceCheckBuyerInformationRule()
        {
        }

        public ValidationResult Handle(CreateReplaceInvoice02Dto input)
        {
            // Nếu MST người bán nhập
            // Cần nhập thông tin Tên đơn vị và Địa chỉ mua
            if (!input.BuyerTaxCode.IsNullOrEmpty())
            {
                if (input.BuyerFullName.IsNullOrEmpty() || input.BuyerAddressLine.IsNullOrEmpty())
                {
                    return new ValidationResult(false, "<PERSON><PERSON> nhập thông tin Mã số thuế người mua, y<PERSON><PERSON> c<PERSON><PERSON> b<PERSON>t buộc nhập Tên đơn vị và Địa chỉ. Vui lòng kiểm tra lại");
                }
            }

            return new ValidationResult(true);
        }
    }
}
