using Core.Shared.Validations;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Invoice02.Application.Invoice02.Dto;
using Core.Shared.Factory;
using Microsoft.Extensions.Localization;
using Core.Localization.Resources.AbpLocalization;
using VnisCore.Invoice02.Application.Factories.Services;
using Core.Dto.Shared;

namespace VnisCore.Invoice02.Application.Invoice02.ValidationRule.CreateReplace
{
    /// <summary>
    /// Kiểm tra đăng ký phát hành có mã không mã
    /// </summary>
    public class CreateReplaceInvoiceCheckRegisterInvoiceTemplateRule : IValidationRuleAsync<CreateInvoice02HeaderDto, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;
        private readonly IValidationContext _validationContext;
        private readonly ICommonInvoice02Service _commonInvoice02Service;

        public CreateReplaceInvoiceCheckRegisterInvoiceTemplateRule(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory,
            IValidationContext validationContext,
            ICommonInvoice02Service commonInvoice02Service)
        {
            _localizer = localizer;
            _appFactory = appFactory;
            _validationContext = validationContext;
            _commonInvoice02Service = commonInvoice02Service;
        }

        public async Task<ValidationResult> HandleAsync(CreateInvoice02HeaderDto input)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var userId = _appFactory.CurrentUser.Id.Value;
            var template = _validationContext.GetItem<CreateInvoice02GetDataValidDto>("Template");
            var registrations = await _commonInvoice02Service.GetRegisterAvailabilities(tenantId, userId, input.TemplateNo);

            if (!registrations.Any())
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice02.NoRegisterInvoiceTemplate"]);

            var idsRegistration = registrations.Select(x => x.Id).ToList();

            if (!idsRegistration.Contains(template.TemplateId))
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice02.InvoiceTemplateNotRegister"]);

            return new ValidationResult(true);
        }
    }
}
