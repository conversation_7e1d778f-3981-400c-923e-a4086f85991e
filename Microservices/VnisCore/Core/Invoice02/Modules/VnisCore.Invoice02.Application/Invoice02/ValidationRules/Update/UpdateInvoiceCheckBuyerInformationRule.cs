using Core.Shared.Validations;
using System;
using System.Collections.Generic;
using VnisCore.Invoice02.Application.Invoice02.Dto;

namespace VnisCore.Invoice02.Application.Invoice02.ValidationRules.Update
{
    /// <summary>
    /// kiểm tra thông tin người mua
    /// </summary>
    public class UpdateInvoiceCheckBuyerInformationRule : IValidationRule<UpdateInvoice02HeaderDto, ValidationResult>
    {

        public UpdateInvoiceCheckBuyerInformationRule()
        {
        }

        public ValidationResult Handle(UpdateInvoice02HeaderDto input)
        {
            // Nếu MST người bán nhập
            // Cần nhập thông tin Tên đơn vị và Địa chỉ mua
            if (!input.BuyerTaxCode.IsNullOrEmpty())
            {
                if (input.BuyerFullName.IsNullOrEmpty() || input.BuyerAddressLine.IsNullOrEmpty())
                {
                    return new ValidationResult(false, "<PERSON><PERSON> nhập thông tin Mã số thuế người mua, yê<PERSON> cầu bắt buộc nhập Tên đơn vị và Địa chỉ. Vui lòng kiểm tra lại");
                }
            }

            return new ValidationResult(true);
        }
    }
}
