using Core.Shared.Validations;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VnisCore.Invoice02.Application.Invoice02.Dto;

namespace VnisCore.Invoice02.Application.Invoice02.ValidationRules.UpdateReplace
{
    /// <summary>
    /// kiểm tra thông tin người mua
    /// </summary>
    public class UpdateReplaceInvoiceCheckBuyerInformationRule : IValidationRule<UpdateReplaceInvoice02HeaderDto, ValidationResult>
    {

        public UpdateReplaceInvoiceCheckBuyerInformationRule()
        {
        }

        public ValidationResult Handle(UpdateReplaceInvoice02HeaderDto input)
        {
            // Nếu MST người bán nhập
            // Cần nhập thông tin Tên đơn vị và Địa chỉ mua
            if (!input.BuyerTaxCode.IsNullOrEmpty())
            {
                if (input.BuyerFullName.IsNullOrEmpty() || input.BuyerAddressLine.IsNullOrEmpty())
                {
                    return new ValidationResult(false, "<PERSON><PERSON> nhập thông tin Mã số thuế người mua, yêu cầu bắt buộc nhập Tên đơn vị và Địa chỉ. Vui lòng kiểm tra lại");
                }
            }

            return new ValidationResult(true);
        }
    }
}
