using Core.BackgroundWorkers;
using Core.Shared.Constants;
using Core.Threading;

using Microsoft.Extensions.DependencyInjection;

using System.Threading.Tasks;

using VnisCore.Invoice02.SyncElasticsearch.Interface;

namespace VnisCore.Invoice02.SyncElasticsearch.BackgroundWorkers
{
    public class Invoice02SyncMongoToEsIsDeclaredWorker : AsyncPeriodicBackgroundWorkerBase
    {
        public Invoice02SyncMongoToEsIsDeclaredWorker(
            AbpAsyncTimer timer, 
            IServiceScopeFactory serviceScopeFactory) : base(timer, serviceScopeFactory)
        {
            Timer.Period = 1000; //30 s
        }

        protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
        {
            await workerContext
                .ServiceProvider
                .GetRequiredService<IInvoice02SyncESBusiness>()
                .SyncESAsync(SyncElasticSearchStatus.PendingSyncIsDeclared.GetHashCode());
        }
    }
}
