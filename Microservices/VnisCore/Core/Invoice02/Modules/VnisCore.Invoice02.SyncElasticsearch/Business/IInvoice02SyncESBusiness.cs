using Core.ElasticSearch.Shared.Invoices.Invoice02.IRepository;
using Core.Shared.Constants;

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;

using Serilog;

using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using VnisCore.Core.MongoDB.IRepository.Invoices.Invoice02;
using VnisCore.Invoice02.SyncElasticsearch.Configuration;
using VnisCore.Invoice02.SyncElasticsearch.Interface;

namespace VnisCore.Invoice02.SyncElasticsearch.Business
{
    public class Invoice02SyncESBusiness: IInvoice02SyncESBusiness
    {
        private readonly SettingsOptions _settingsOptions;
        private readonly IVnisCoreMongoInvoice02Repository _vnisCoreMongoInvoice02Repository;
        private readonly IConfiguration _configuration;
        private readonly IESInvoice02Repository _iESInvoice02Repository;

        public Invoice02SyncESBusiness(
            IOptions<SettingsOptions> settingsOptions,
            IConfiguration configuration,
            IVnisCoreMongoInvoice02Repository vnisCoreMongoInvoice02Repository,
            IESInvoice02Repository iESInvoice02Repository)
        {
            _vnisCoreMongoInvoice02Repository = vnisCoreMongoInvoice02Repository;
            _settingsOptions = settingsOptions.Value;
            _configuration = configuration;
            _iESInvoice02Repository = iESInvoice02Repository;
        }
        /// <summary>
        /// đồng bộ ES
        /// </summary>
        /// <returns></returns>
        public async Task SyncESAsync(int syncESStatus)
        {
            var tenantGroupIndexEs = "group-x";
            var tenantGroups = _settingsOptions.TenantGroupsPrivate.Split(",").Select(decimal.Parse).ToList();
            if (_settingsOptions.IsEnableTenantGroupPrivate > 0)
            {
                tenantGroupIndexEs = $"group-{_settingsOptions.TenantGroup}";
                tenantGroups = new List<decimal> { decimal.Parse(_settingsOptions.TenantGroup) };
            }

            int.TryParse(_configuration["Settings:MaxDocumentIndexEs"], out var maxDocumentIndexEs);
            if (maxDocumentIndexEs <= 0)
                maxDocumentIndexEs = 100; // giá trị mặc định nếu ko cấu hình

            var data = await _vnisCoreMongoInvoice02Repository.Invoice02SyncToEs(tenantGroups, maxDocumentIndexEs, syncESStatus, _settingsOptions.IsEnableTenantGroupPrivate);
            if (!data.Any())
                return;

            switch (syncESStatus)
            {
                case (int)SyncElasticSearchStatus.PendingSyncApprove:
                    await _iESInvoice02Repository.UpdateEsApproveAsync(data, tenantGroupIndexEs);
                    break;
                case (int)SyncElasticSearchStatus.PendingSyncCancel:
                    await _iESInvoice02Repository.UpdateESCancelAsync(data, tenantGroupIndexEs);
                    break;
                case (int)SyncElasticSearchStatus.PendingSyncDelete:
                    await _iESInvoice02Repository.UpdateESDeleteAsync(data, tenantGroupIndexEs);
                    break;
                case (int)SyncElasticSearchStatus.PendingSyncSign:
                    await _iESInvoice02Repository.UpdateESSignStatusAsync(data, tenantGroupIndexEs);
                    break;
                //case (int)SyncElasticSearchStatus.PendingSyncStatusTvanRequest:
                //    break;
                case (int)SyncElasticSearchStatus.PendingSyncStatusTvanResponse:
                    await _iESInvoice02Repository.UpdateESVerificationCodeAsync(data, tenantGroupIndexEs);
                    break;
                case (int)SyncElasticSearchStatus.PendingSyncIsDeclared:
                    await _iESInvoice02Repository.UpdateESIsDecraredAsync(data, tenantGroupIndexEs);
                    break;
                //case (int)SyncElasticSearchStatus.PendingSyncCreateInvoiceError:
                //    break;
                default:
                    Log.Error("Trạng thái đồng bộ không đúng");
                    break;
            }
        }
    }
}
