using Core.Application.Dtos;
using Core.Data;
using Core.Domain.Repositories;
using Core.ObjectMapping;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Models;
using Dapper;
using Nest;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice03;
using VnisCore.Invoice03.Application.Factories.Models;
using VnisCore.Invoice03.Application.Invoice03.Dto;
using VnisCore.Invoice03.Application.Invoice03.Models;
using VnisCore.Invoice03.Application.Invoice03.Models.Requests.Queries;
using VnisCore.Invoice03.Application.Invoice03.Models.Responses.Queries;
using VnisCore.Invoice03.Application.Invoice03.RabbitMqEventBus.CreateInvoice03.MessageEventData;
using VnisCore.Invoice03.Application.Invoice03.RabbitMqEventBus.CreateInvoice03OldDecree.MessageEventData;
using VnisCore.Invoice03.Application.Invoice03.RabbitMqEventBus.CreateReplaceInvoice03.MessageEventData;
using VnisCore.Invoice03.Application.Invoice03.RabbitMqEventBus.UpdateInvoice03.MessageEventData;
using VnisCore.Invoice03.Application.Invoice03.RabbitMqEventBus.UpdateReplaceInvoice03.MessageEventData;
using static VnisCore.Invoice03.Application.Factories.Constants.CommonInvoice03Const;

namespace VnisCore.Invoice03.Application.Factories.Services
{
    public class Invoice03Service : IInvoice03Service
    {
        private readonly IAppFactory _appFactory;
        private readonly IElasticClient _elasticClient;
        private readonly IRepository<Invoice03DetailEntity, long> _repoInvoice03Detail;
        private readonly IInvoiceService<Invoice03HeaderEntity, Invoice03HeaderFieldEntity, Invoice03DetailFieldEntity> _invoiceService;
        protected IObjectMapper _objectMapper { get; }

        public Invoice03Service(
            IAppFactory appFactory,
            IElasticClient elasticClient,
            IRepository<Invoice03DetailEntity, long> repoInvoice03Detail,
            IInvoiceService<Invoice03HeaderEntity, Invoice03HeaderFieldEntity, Invoice03DetailFieldEntity> invoiceService,
            IObjectMapper objectMapper
            )
        {
            _appFactory = appFactory;
            _repoInvoice03Detail = repoInvoice03Detail;
            _invoiceService = invoiceService;
            _elasticClient = elasticClient;
            _objectMapper = objectMapper;
        }

        public async Task<List<long>> GetSEQsNextVal(int level, string SequenceName)
        {
            var sql = $@"   SELECT ""{SequenceName}"".NEXTVAL
                            FROM(
                               SELECT level
                               FROM dual
                               CONNECT BY level <= {level}
                            ) ";

            var result = await _appFactory.VnisCoreOracle.Connection.QueryAsync<long>(sql);

            return result.ToList();
        }


        public async Task<PagedResultDto<PagingInvoice03Response>> GetListElasticAsync(Guid tenantId, PagingInvoice03Request input)
        {
            var datas = new List<PagingInvoice03Response>();
            var pagedResponse = await PagingElasticSearchAsync(tenantId, input);

            var paged = pagedResponse.Documents.ToList();

            foreach (var item in paged)
            {
                PagingInvoice03Response model = new PagingInvoice03Response
                {
                    Id = item.Id,
                    ErpId = item.ErpId,
                    TransactionId = item.TransactionId,
                    TemplateNo = item.TemplateNo,
                    SerialNo = item.SerialNo,
                    InvoiceNo = item.InvoiceNo,
                    Number = item.Number,
                    InvoiceStatus = EnumExtension.ToEnum<InvoiceStatus>(item.InvoiceStatus),
                    SignStatus = EnumExtension.ToEnum<SignStatus>(item.SignStatus),
                    ApproveStatus = EnumExtension.ToEnum<ApproveStatus>(item.ApproveStatus),
                    ApproveCancelStatus = EnumExtension.ToEnum<ApproveStatus>(item.ApproveCancelStatus),
                    ApproveDeleteStatus = EnumExtension.ToEnum<ApproveStatus>(item.ApproveDeleteStatus),
                    InvoiceDate = item.InvoiceDate,
                    TotalAmount = item.TotalAmount,
                    TotalPaymentAmount = item.TotalPaymentAmount,
                    PrintedTime = item.PrintedTime,
                    UserNameCreator = item.UserNameCreator,
                    CreatorErp = item.CreatorErp,
                    Note = item.Note,
                    Source = EnumExtension.ToEnum<InvoiceSource>(item.Source),
                    IsViewed = item.IsViewed,
                    IsOpened = item.IsOpened,
                    IsDeclared = item.IsDeclared,
                    VerificationCode = item.VerificationCode
                };
                datas.Add(model);
            }

            var result = new PagedResultDto<PagingInvoice03Response>
            {
                Items = datas,
                //Page = request.Page ?? 1,
                //Q = request.Q,
                //Size = request.Size ?? 10,
                //TotalItems = Convert.ToInt32(pagedResponse.Total),
                TotalCount = pagedResponse.Total
            };
            return result;
        }

        public async Task<PagedResultDto<PagingInvoice03Response>> GetListAsync(Guid tenantId, Guid userId, PagingInvoice03Request input)
        {
            var query = await GenerateDrawGetListQuery(tenantId, userId, input);

            var data = await _appFactory.VnisCoreOracle.Connection.QueryAsync<PagingInvoice03Response>(query);

            var result = new PagedResultDto<PagingInvoice03Response>();
            if (data != null && data.Any())
            {
                data.ToList().ForEach(x =>
                {
                    if (!string.IsNullOrEmpty(x.ExtraProperties))
                    {
                        var extraProperties = JsonConvert.DeserializeObject<Dictionary<string, string>>(x.ExtraProperties);
                        var headerExtraProperties = new List<InvoiceHeaderExtraModel>();
                        if (extraProperties.ContainsKey("invoiceHeaderExtras") && !string.IsNullOrEmpty(extraProperties["invoiceHeaderExtras"]))
                        {
                            headerExtraProperties = JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(extraProperties["invoiceHeaderExtras"]);
                        }

                        if (headerExtraProperties.Any())
                        {
                            x.InvoiceHeaderExtras = headerExtraProperties.Select(x => new PagingInvoice03Response.PagingInvoice03HeaderExtraResponseModel
                            {
                                FieldValue = x.FieldValue,
                                FieldName = x.FieldName,
                            }).ToList();
                        }
                    }
                });

                result.TotalCount = data.First().TotalItems;
                result.Items = data.ToList();
            }

            return result;
        }
        private async Task<ISearchResponse<ElastichSearchInvoiceModel>> PagingElasticSearchAsync(Guid tenantCode, PagingInvoice03Request query)
        {
            //Full text search - multiMatch
            var multiMatch = new List<Func<MultiMatchQueryDescriptor<ElastichSearchInvoiceModel>, IMultiMatchQuery>>();

            if (!string.IsNullOrEmpty(query.Keyword))
            {
                //int.TryParse(query.Q, out int outInvoiceNo);
                //var invoiceStatus = EnumExtension.TryToEnum<InvoiceStatus>(query.Q);
                //var signStatus = EnumExtension.TryToEnum<SignStatus>(query.Q);

                multiMatch.Add(m => m.Fields(f => f
               .Field(p => p.UserNameCreator)
               .Field(p => p.DeliveryBy)
               .Field(p => p.DeliveryOrderNumber)
               .Field(p => p.DeliveryOrderBy)
               .Field(p => p.SerialNo)
               .Field(p => p.TemplateNo)
               .Field(p => p.ErpId)
               .Field(p => p.TransactionId)
               .Field(p => p.CreatorErp)
               )
                .Query(query.Keyword)
                .Operator(Operator.And)
               );
            }
            //               .Field(p => p.Number)
            //   .Field(p => p.InvoiceStatus)
            //   .Field(p => p.SignStatus)
            //
            //Keyword customize
            var filters = new List<Func<QueryContainerDescriptor<ElastichSearchInvoiceModel>, QueryContainer>>();

            if (query.CancelFromDate.HasValue)
                filters.Add(f => f.DateRange(t => t.Field(f => f.CancelTime).GreaterThanOrEquals(query.CancelFromDate)));


            //lấy hóa đơn có số hoặc không số
            if (query.IsNullInvoice)
                filters.Add(f => !f.Exists(t => t.Field(f => f.Number)));
            else
                filters.Add(f => f.Exists(t => t.Field(f => f.Number)));

            if (query.CancelToDate.HasValue)
            {
                filters.Add(f => f.DateRange(t => t.Field(f => f.CancelTime).LessThanOrEquals(query.CancelToDate)));
            }

            if (query.IssuedTime.HasValue)
            {
                var dateNextIssuedAt = query.IssuedTime.Value.AddDays(1);
                filters.Add(f => f.DateRange(t => t.Field(f => f.IssuedTime).LessThan(dateNextIssuedAt)) &&
                f.DateRange(t => t.Field(f => f.IssuedTime).GreaterThanOrEquals(query.IssuedTime)));
            }

            //Full text search - multiMatch
            var multiCustomers = new List<Func<MultiMatchQueryDescriptor<ElastichSearchInvoiceModel>, IMultiMatchQuery>>();

            if (query.InvoiceNo != null)
            {
                int.TryParse(query.InvoiceNo, out int outInvoiceNo);
                filters.Add(f => f.Terms(t => t.Field(f => f.InvoiceNo).Terms(outInvoiceNo.ToString("0000000"))));
            }

            if (query.ApproveStatuses != null && query.ApproveStatuses.Any())
                filters.Add(f => f.Terms(t => t.Field(f => f.ApproveStatus).Terms(query.ApproveStatuses)));

            if (query.InvoiceStatuses != null && query.InvoiceStatuses.Any())
                filters.Add(f => f.Terms(t => t.Field(f => f.InvoiceStatus.GetHashCode()).Terms(query.InvoiceStatuses)));

            if (query.SignStatuses != null && query.SignStatuses.Any())
                filters.Add(f => f.Terms(t => t.Field(f => f.SignStatus).Terms(query.SignStatuses)));

            if (!string.IsNullOrEmpty(query.TransactionId))
                filters.Add(f => f.MatchPhrase(t => t.Field(f => f.TransactionId).Query(query.TransactionId)));

            if (!string.IsNullOrEmpty(query.ErpId))
                filters.Add(f => f.MatchPhrase(t => t.Field(f => f.ErpId).Query(query.ErpId)));

            if (query.HeaderExtras != null && query.HeaderExtras.Count > 0)
            {
                foreach (var item in query.HeaderExtras)
                {
                    var split = item.Split("-");
                    var fieldName = split.FirstOrDefault();
                    var fieldValue = item.Substring(fieldName.Length + 1, item.Length - fieldName.Length - 1);
                    filters.Add(f => f.MatchPhrase(t => t.Field(f => f.InvoiceHeaderExtras.Select(x => x.FieldName)).Query(fieldName.ToLower())) &&
                    f.MatchPhrase(t => t.Field(f => f.InvoiceHeaderExtras.Select(x => x.FieldValue)).Query(fieldValue.ToLower())));
                }
            }

            if (query.CreateFromDate.HasValue)
                filters.Add(f => f.DateRange(t => t.Field(f => f.InvoiceDate).GreaterThanOrEquals(query.CreateFromDate)));

            if (query.CreateToDate.HasValue)
                filters.Add(f => f.DateRange(t => t.Field(f => f.InvoiceDate).LessThanOrEquals(query.CreateToDate)));

            if (!string.IsNullOrEmpty(query.UserNameCreator))
                filters.Add(f => f.MatchPhrase(t => t.Field(f => f.UserNameCreator).Query(query.UserNameCreator)));

            if (!string.IsNullOrEmpty(query.UserNameCreatorErp))
                filters.Add(f => f.MatchPhrase(t => t.Field(f => f.CreatorErp).Query(query.UserNameCreatorErp)));

            //Searching query
            //.From(filters.Count > 1 ? 0 : ((query.SkipCount - 1) * 10))
            var searchResponse = await _elasticClient.SearchAsync<ElastichSearchInvoiceModel>(s => s
                .From(query.SkipCount)
                .Size(query.MaxResultCount)
                .Query(q => q
                        .Bool(b => b
                            .Must(
                                bm => bm.Match(p => p
                                .Field(f => f.TenantId)
                                .Query(tenantCode.ToString())),
                                //bm => bm.DateRange(p => p
                                //    .Field(f => f.InvoiceDate)
                                //    .GreaterThanOrEquals(query.CreateFromDate)
                                //    .LessThanOrEquals(query.CreateToDate)),
                                bm => bm.MultiMatch(multiMatch.FirstOrDefault()),
                                bm => bm.MultiMatch(multiCustomers.FirstOrDefault())
                                //bm => bm.Bool(b => b
                                //    .Must(
                                //    s => MatchAny(s, s => s.TemplateCode, query.AllReadTemplateCodes.Select(x => x.ToString()).ToArray()),
                                //    s => MatchAny(s, s => s.TemplateCode, query.CodeInvoiceTemplates?.Select(x => x.ToString()).ToArray()))
                                ////s => MatchAny(s, s => s.Code, query.Codes?.Select(x => x.ToString()).ToArray()))
                                //)
                                )
                            .Filter(filters)
                            ))
                .Sort(s => s.Descending(f => f.InvoiceDate).Descending(f => f.Number).Descending(f => f.CreationTime))
                );


            return searchResponse;
        }

        public async Task<GetInvoice03Response> GetByIdAsync(Guid tenantId, long invoiceId)
        {
            var query = await GenerateDrawGetByIdQuery(tenantId, invoiceId);

            var data = await _appFactory.VnisCoreOracle.Connection.QueryFirstOrDefaultAsync<GetInvoice03Response>(query);

            if (data != null)
            {
                if (!String.IsNullOrEmpty(data.InvoiceHeaderExtrasJson))
                    data.InvoiceHeaderExtras = JsonConvert.DeserializeObject<List<GetInvoice03Response.GetInvoice03HeaderExtraResponseModel>>(data.InvoiceHeaderExtrasJson);

                if (!String.IsNullOrEmpty(data.InvoiceReferenceJson))
                {
                    var referenceEntity = JsonConvert.DeserializeObject<Invoice03ReferenceEntity>(data.InvoiceReferenceJson);
                    data.InvoiceReferenceId = referenceEntity.InvoiceReferenceId;
                    data.TransactionData = $"{data.SellerTaxCode}|{referenceEntity.TemplateNoReference}|{referenceEntity.SerialNoReference}|{referenceEntity.InvoiceNoReference}|{referenceEntity.InvoiceDateReference:yyyy-MM-dd}";
                    data.InvoiceDateReference = referenceEntity.InvoiceDateReference;
                    data.Content = referenceEntity.Note;
                }

                if (!String.IsNullOrEmpty(data.InvoiceDetailsJson))
                {
                    var invoiceDetails = JsonConvert.DeserializeObject<List<GetInvoice03Response.GetInvoice03DetailResponseModel>>(data.InvoiceDetailsJson);

                    invoiceDetails.ForEach(x =>
                    {
                        if (!String.IsNullOrEmpty(x.InvoiceDetailExtrasJson))
                            x.InvoiceDetailExtras = JsonConvert.DeserializeObject<List<GetInvoice03Response.GetInvoice03DetailExtraResponseModel>>(x.InvoiceDetailExtrasJson);
                    });

                    data.InvoiceDetails = invoiceDetails;
                }
            }


            return data;
        }

        public async Task<GetInvoice03Response> GetByIdElasticAsync(Guid tenantId, long invoiceId)
        {
            var data = (await _elasticClient.SearchAsync<GetInvoice03Response>(s => s
            .Query(q => q
                        .Bool(b => b
                            .Must(
                                bm => bm.Match(p => p
                                .Field(f => f.TenantId)
                                .Query(tenantId.ToString())),
                                bm => bm.Match(p => p
                                .Field(f => f.Id)
                                .Query(invoiceId.ToString()))
                                )
                            ))))
            .Documents.FirstOrDefault();

            return data;
        }

        public async Task<InfosNeedCreateInvoice03Model> GetInfosBeforePublishCreateInvoice03(Guid tenantId, List<string> productCodes, List<string> unitNames)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var andProductCode = new StringBuilder();
            if (productCodes != null && productCodes.Any())
                andProductCode.Append($@" AND ""ProductCode"" IN('{String.Join("','", productCodes)}') ");

            var andUnitName = new StringBuilder();
            if (unitNames != null && unitNames.Any())
            {
                unitNames = unitNames.Select(x => x.ToUpper()?.Replace("'", "''")).ToList();
                andUnitName.Append($@" AND ""NormalizedName"" IN('{String.Join("','", unitNames)}') ");
            }

            var sql = new StringBuilder();
            sql.Append($@"  WITH Products AS (                                                                     
                                  SELECT ""Id"", ""ProductTypeId"", ""ProductCode""                                    
                                  FROM ""Product""                                                                     
                                  WHERE                                                                                
                                      ""TenantId"" = '{rawTenantId}'                                                   
                                      {andProductCode}                                                                 
                                      AND ""IsDeleted"" = 0                                                            
                              ),                                                                                       
                              ProductTypes AS(                                                                         
                                  SELECT ""Id"", ""HideQuantity"", ""HideUnit"", ""HideUnitPrice""                     
                                  FROM ""ProductType""                                                                 
                                  WHERE                                                                                
                                      ""Id"" IN(SELECT ""ProductTypeId"" FROM Products)                                
                                      AND ""IsDeleted"" = 0                                                            
                              ),                                                                                       
                              Units AS(                                                                                
                                  SELECT ""Id"", ""Name"", ""Rounding""                                                
                                  FROM ""Unit""                                                                        
                                  WHERE                                                                                
                                      ""TenantId"" = '{rawTenantId}'                                                   
                                      {andUnitName}                                                                    
                                      AND ""IsDeleted"" = 0                                                            
                              ),                                                                                       
                              HeaderFields AS(                                                                         
                                  SELECT ""Id"", ""FieldName""                                                         
                                  FROM ""Invoice03HeaderField""                                                        
                                  WHERE ""TenantId"" = '{rawTenantId}' AND ""IsDeleted"" = 0                                                
                              ),                                                                                       
                              DetailFields AS(                                                                         
                                  SELECT ""Id"", ""FieldName""                                                         
                                  FROM ""Invoice03DetailField""                                                        
                                  WHERE ""TenantId"" = '{rawTenantId}' AND ""IsDeleted"" = 0                                                
                              )                                                                                        
                              SELECT 1 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM Products    
                              UNION ALL                                                                                                    
                              SELECT 2 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM ProductTypes
                              UNION ALL                                                                                                    
                              SELECT 3 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM Units       
                              UNION ALL                                                                                                    
                              SELECT 4 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM HeaderFields
                              UNION ALL                                                                                                    
                              SELECT 5 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM DetailFields ");


            var data = await _appFactory.VnisCoreOracle.Connection.QueryAsync<TypeData>(sql.ToString());
            var result = new InfosNeedCreateInvoice03Model();

            var products = data.First(x => x.Type == 1);
            var productTypes = data.First(x => x.Type == 2);
            var units = data.First(x => x.Type == 3);
            var headerFields = data.First(x => x.Type == 4);
            var detailFields = data.First(x => x.Type == 5);

            if (!String.IsNullOrEmpty(products.Data))
                result.Products = JsonConvert.DeserializeObject<List<ProductEntity>>(products.Data).ToDictionary(x => x.ProductCode, x => x);

            if (!String.IsNullOrEmpty(productTypes.Data))
                result.ProductTypes = JsonConvert.DeserializeObject<List<ProductTypeEntity>>(productTypes.Data).ToDictionary(x => x.Id, x => x);

            if (!String.IsNullOrEmpty(units.Data))
                result.Units = JsonConvert.DeserializeObject<List<UnitEntity>>(units.Data).ToDictionary(x => x.Name, x => x);

            if (!String.IsNullOrEmpty(headerFields.Data))
                result.HeaderFields = JsonConvert.DeserializeObject<List<Invoice03HeaderFieldEntity>>(headerFields.Data).ToDictionary(x => x.FieldName, x => x);

            if (!String.IsNullOrEmpty(detailFields.Data))
                result.DetailFields = JsonConvert.DeserializeObject<List<Invoice03DetailFieldEntity>>(detailFields.Data).ToDictionary(x => x.FieldName, x => x);

            return result;
        }

        public async Task<InfosNeedCreateInvoice03Model> GetInfosBeforePublishCreateAdjusmentInvoice03(Guid tenantId, List<string> productCodes, List<string> unitNames)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var andProductCode = new StringBuilder();
            if (productCodes != null && productCodes.Any())
                andProductCode.Append($@" AND ""ProductCode"" IN('{String.Join("','", productCodes)}') ");

            var andUnitName = new StringBuilder();
            if (unitNames != null && unitNames.Any())
            {
                unitNames = unitNames.Select(x => x.ToUpper()?.Replace("'", "''")).ToList();
                andUnitName.Append($@" AND ""NormalizedName"" IN('{String.Join("','", unitNames)}') ");
            }

            var sql = new StringBuilder();
            sql.Append($@"  WITH Products AS (                                                                     
                                  SELECT ""Id"", ""ProductTypeId"", ""ProductCode""                                    
                                  FROM ""Product""                                                                     
                                  WHERE                                                                                
                                      ""TenantId"" = '{rawTenantId}'                                                   
                                      {andProductCode}                                                                 
                                      AND ""IsDeleted"" = 0                                                            
                              ),                                                                                       
                              ProductTypes AS(                                                                         
                                  SELECT ""Id"", ""HideQuantity"", ""HideUnit"", ""HideUnitPrice""                     
                                  FROM ""ProductType""                                                                 
                                  WHERE                                                                                
                                      ""Id"" IN(SELECT ""ProductTypeId"" FROM Products)                                
                                      AND ""IsDeleted"" = 0                                                            
                              ),                                                                                       
                              Units AS(                                                                                
                                  SELECT ""Id"", ""Name"", ""Rounding""                                                
                                  FROM ""Unit""                                                                        
                                  WHERE                                                                                
                                      ""TenantId"" = '{rawTenantId}'                                                   
                                      {andUnitName}                                                                    
                                      AND ""IsDeleted"" = 0                                                            
                              ),                                                                                       
                              HeaderFields AS(                                                                         
                                  SELECT ""Id"", ""FieldName""                                                         
                                  FROM ""Invoice03HeaderField""                                                        
                                  WHERE ""TenantId"" = '{rawTenantId}' AND ""IsDeleted"" = 0                                                
                              ),                                                                                       
                              DetailFields AS(                                                                         
                                  SELECT ""Id"", ""FieldName""                                                         
                                  FROM ""Invoice03DetailField""                                                        
                                  WHERE ""TenantId"" = '{rawTenantId}' AND ""IsDeleted"" = 0                                                
                              )                                                                                        
                              SELECT 1 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM Products    
                              UNION ALL                                                                                                    
                              SELECT 2 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM ProductTypes
                              UNION ALL                                                                                                    
                              SELECT 3 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM Units       
                              UNION ALL                                                                                                    
                              SELECT 4 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM HeaderFields
                              UNION ALL                                                                                                    
                              SELECT 5 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM DetailFields ");


            var data = await _appFactory.VnisCoreOracle.Connection.QueryAsync<TypeData>(sql.ToString());
            var result = new InfosNeedCreateInvoice03Model();

            var products = data.First(x => x.Type == 1);
            var productTypes = data.First(x => x.Type == 2);
            var units = data.First(x => x.Type == 3);
            var headerFields = data.First(x => x.Type == 4);
            var detailFields = data.First(x => x.Type == 5);

            if (!String.IsNullOrEmpty(products.Data))
                result.Products = JsonConvert.DeserializeObject<List<ProductEntity>>(products.Data).ToDictionary(x => x.ProductCode, x => x);

            if (!String.IsNullOrEmpty(productTypes.Data))
                result.ProductTypes = JsonConvert.DeserializeObject<List<ProductTypeEntity>>(productTypes.Data).ToDictionary(x => x.Id, x => x);

            if (!String.IsNullOrEmpty(units.Data))
                result.Units = JsonConvert.DeserializeObject<List<UnitEntity>>(units.Data).ToDictionary(x => x.Name, x => x);

            if (!String.IsNullOrEmpty(headerFields.Data))
                result.HeaderFields = JsonConvert.DeserializeObject<List<Invoice03HeaderFieldEntity>>(headerFields.Data).ToDictionary(x => x.FieldName, x => x);

            if (!String.IsNullOrEmpty(detailFields.Data))
                result.DetailFields = JsonConvert.DeserializeObject<List<Invoice03DetailFieldEntity>>(detailFields.Data).ToDictionary(x => x.FieldName, x => x);

            return result;
        }

        public async Task<InfosNeedUpdateInvoice03Model> GetInfosBeforePublishUpdateInvoice03(Guid tenantId, long invoiceId, List<string> productCodes, List<string> unitNames)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var andProductCode = new StringBuilder();
            if (productCodes != null && productCodes.Any())
                andProductCode.Append($@" AND ""ProductCode"" IN('{String.Join("','", productCodes)}') ");

            var andUnitName = new StringBuilder();
            if (unitNames != null && unitNames.Any())
            {
                unitNames = unitNames.Select(x => x.ToUpper()?.Replace("'", "''")).ToList();
                andUnitName.Append($@" AND ""NormalizedName"" IN('{String.Join("','", unitNames)}') ");
            }

            var sql = new StringBuilder();
            sql.Append($@"  WITH Products AS (                                                                     
                                SELECT ""Id"", ""ProductTypeId"", ""ProductCode""                                    
                                FROM ""Product""                                                                     
                                WHERE                                                                                
                                    ""TenantId"" = '{rawTenantId}'                                                   
                                    {andProductCode}                                                                 
                                    AND ""IsDeleted"" = 0                                                            
                            ),                                                                                       
                            ProductTypes AS(                                                                         
                                SELECT ""Id"", ""HideQuantity"", ""HideUnit"", ""HideUnitPrice""                     
                                FROM ""ProductType""                                                                 
                                WHERE                                                                                
                                    ""Id"" IN(SELECT ""ProductTypeId"" FROM Products)                                
                                    AND ""IsDeleted"" = 0                                                            
                            ),                                                                                       
                            HeaderFields AS(                                                                         
                                SELECT ""Id"", ""FieldName""                                                         
                                FROM ""Invoice03HeaderField""                                                        
                                WHERE ""TenantId"" = '{rawTenantId}' AND ""IsDeleted"" = 0                                                
                            ),                                                                                       
                            DetailFields AS(                                                                         
                                SELECT ""Id"", ""FieldName""                                                         
                                FROM ""Invoice03DetailField""                                                        
                                WHERE ""TenantId"" = '{rawTenantId}' AND ""IsDeleted"" = 0                                                
                            ),
                            Details AS(                                                                         
                                SELECT *                                                         
                                FROM ""Invoice03Detail""                                                        
                                WHERE ""InvoiceHeaderId"" = {invoiceId}                                               
                            )
                            SELECT 1 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM Products    
                            UNION ALL                                                                                                    
                            SELECT 2 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM ProductTypes
                            UNION ALL                                                                                                    
                            SELECT 3 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM HeaderFields
                            UNION ALL                                                                                                    
                            SELECT 4 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM DetailFields  
                            UNION ALL
                            SELECT 5 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM Details 
                    ");


            var data = await _appFactory.VnisCoreOracle.Connection.QueryAsync<TypeData>(sql.ToString());
            var result = new InfosNeedUpdateInvoice03Model();

            var products = data.First(x => x.Type == 1);
            var productTypes = data.First(x => x.Type == 2);
            var headerFields = data.First(x => x.Type == 3);
            var detailFields = data.First(x => x.Type == 4);
            var details = data.First(x => x.Type == 5);

            if (!String.IsNullOrEmpty(products.Data))
                result.Products = JsonConvert.DeserializeObject<List<ProductEntity>>(products.Data).ToDictionary(x => x.ProductCode, x => x);

            if (!String.IsNullOrEmpty(productTypes.Data))
                result.ProductTypes = JsonConvert.DeserializeObject<List<ProductTypeEntity>>(productTypes.Data).ToDictionary(x => x.Id, x => x);

            if (!String.IsNullOrEmpty(headerFields.Data))
                result.HeaderFields = JsonConvert.DeserializeObject<List<Invoice03HeaderFieldEntity>>(headerFields.Data).ToDictionary(x => x.FieldName, x => x);

            if (!String.IsNullOrEmpty(detailFields.Data))
                result.DetailFields = JsonConvert.DeserializeObject<List<Invoice03DetailFieldEntity>>(detailFields.Data).ToDictionary(x => x.FieldName, x => x);

            if (!String.IsNullOrEmpty(details.Data))
            {
                var detailInfos = JsonConvert.DeserializeObject<List<Invoice03DetailInfo>>(details.Data);
                if (detailInfos != null && detailInfos.Any())
                {
                    result.Details = detailInfos.Select(x => new Invoice03DetailEntity
                    {
                        Id = x.Id,
                        Amount = x.Amount,
                        Index = x.Index,
                        Note = x.Note,
                        InvoiceHeaderId = x.InvoiceHeaderId,
                        RoundingUnit = x.RoundingUnit,
                        ProductId = x.ProductId,
                        ProductType = x.ProductType,
                        PaymentAmount = x.PaymentAmount,
                        ProductCode = x.ProductCode?.Trim(),
                        ProductName = x.ProductName,
                        Partition = x.Partition,
                        Quantity = x.Quantity,
                        UnitName = x.UnitName?.Trim(),
                        UnitPrice = x.UnitPrice,
                        ExtraProperties = !string.IsNullOrEmpty(x.ExtraProperties) ? new ExtraPropertyDictionary(JsonConvert.DeserializeObject<Dictionary<string, object>>(x.ExtraProperties)) : null
                    }).ToList();
                }
            }

            return result;
        }

        private async Task UpdateInvoiceDetailAsync(IEnumerable<Invoice03DetailEntity> entityInvoiceDetails, List<Invoice03DetailModel> modelDetails)
        {
            var removeDetails = new List<Invoice03DetailEntity>();

            //group để bỏ trường hợp Index thêm mới
            var duplicate = modelDetails.ToDictionary(x => x.Index, x => x)
                                        .Select(x => x.Key)
                                        .Intersect(entityInvoiceDetails.Select(x => x.Index));
            var newDetails = modelDetails.Where(x => !duplicate.Contains(x.Index));


            //thêm mới
            foreach (var item in newDetails)
            {
                var newDetail = new Invoice03DetailEntity
                {
                    Amount = item.Amount,
                    Note = item.Note,
                    ProductCode = item.ProductCode?.Trim(),
                    ProductName = item.ProductName,
                    UnitName = item.UnitName?.Trim(),
                    UnitPrice = item.UnitPrice,
                    Index = item.Index,
                    Quantity = item.Quantity,
                    PaymentAmount = item.PaymentAmount,
                    InvoiceHeaderId = item.InvoiceHeaderId,
                    TenantId = item.TenantId,
                };

                await _repoInvoice03Detail.InsertAsync(newDetail);
                await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
            }

            await _repoInvoice03Detail.DeleteManyAsync(removeDetails);
        }

        private async Task<string> GenerateDrawGetListQuery(Guid tenantId, Guid userId, PagingInvoice03Request input)
        {
            var condition = new StringBuilder();
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var rawUserId = OracleExtension.ConvertGuidToRaw(userId);

            if (input.IsNullInvoice)
                condition.Append($@"AND ""Number"" IS NULL ");
            else
                condition.Append($@"AND ""Number"" IS NOT NULL ");

            if (!string.IsNullOrEmpty(input.Keyword))
            {
                var q = input.Keyword.Trim()?.Replace("'", "''").ToLower();
                int.TryParse(q, out int outInvoiceNo);

                condition.Append($@"AND (
                                    LOWER(""UserNameCreator"") LIKE LOWER(N'%{q}%') 
                                    OR LOWER(""ReceiverFullName"") LIKE LOWER(N'%{q}%')
                                    OR LOWER(""ReceiverName"") LIKE LOWER(N'%{q}%')
                                    OR ""Number"" = {outInvoiceNo} 
                                    OR LOWER(""SerialNo"") LIKE LOWER('%{q}%')
                                    OR ""ErpId"" LIKE '%{q}%' 
                                    OR ""TransactionId"" LIKE '%{q}%' 
                                    OR ""CreatorErp"" LIKE N'%{q}%' 
                                    ) ");

            }

            if (input.InvoiceTemplateIds != null && input.InvoiceTemplateIds.Any())
                condition.Append($@"AND ""InvoiceTemplateId"" in ({string.Join(",", input.InvoiceTemplateIds)}) ");


            if (input.CreateFromDate.HasValue)
            {
                var createFromDate = input.CreateFromDate.Value.Date.ToUniversalTime();
                condition.Append($@"AND ""InvoiceDate"" >= '{createFromDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' ");
            }

            if (input.CreateToDate.HasValue)
            {
                var createToDate = input.CreateToDate.Value.Date.AddDays(1);
                condition.Append($@"AND ""InvoiceDate"" < '{createToDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' ");
            }

            // Tìm kiếm theo InvoiceTemplateIds
            if (input.InvoiceTemplateIds != null && input.InvoiceTemplateIds.Any())
                condition.Append($@" AND ""InvoiceTemplateId"" in ({string.Join(',', input.InvoiceTemplateIds)}) ");
            else
            {
                //lấy danh sách mẫu dc xem
                condition.Append(@$" AND ""InvoiceTemplateId"" in 
                                    (
                                        SELECT  T.""Id"" 
                                        FROM ""InvoiceTemplate"" T
                                        INNER JOIN(
                                            SELECT ""InvoiceTemplateId""
                                            FROM ""UserReadTemplate""
                                            WHERE ""UserId"" = '{rawUserId}' AND ""InvoiceTemplateId"" IS NOT NULL
                                        )
                                        A ON T.""Id"" = A.""InvoiceTemplateId""
                                        WHERE T.""TenantId"" = '{rawTenantId}' AND T.""IsDeleted"" = 0 
                                        AND T.""TemplateNo"" = {RegistrationInvoiceType.CTu.GetHashCode()} 
                                    ) ");
            }

            if (!string.IsNullOrEmpty(input.InvoiceNo))
            {
                int.TryParse(input.InvoiceNo, out int outInvoiceNo);
                condition.Append($@"AND ""Number"" = {outInvoiceNo} ");
            }

            if (input.InvoiceStatuses != null && input.InvoiceStatuses.Any())
                condition.Append($@"AND ""InvoiceStatus"" IN ({String.Join(",", input.InvoiceStatuses)}) ");

            if (input.SignStatuses != null && input.SignStatuses.Any())
                condition.Append($@"AND ""SignStatus"" IN ({String.Join(",", input.SignStatuses)}) ");

            if (input.VerificationCodeStatuses != null && input.VerificationCodeStatuses.Any())
            {
                if (input.VerificationCodeStatuses.Contains((short)VerificationCodeStatuses.DaCapMa)
                    && !input.VerificationCodeStatuses.Contains((short)VerificationCodeStatuses.ChuaCapMa))
                {
                    condition.Append($@"AND ""VerificationCode"" IS NOT NULL ");
                }
                else if (!input.VerificationCodeStatuses.Contains((short)VerificationCodeStatuses.DaCapMa)
                    && input.VerificationCodeStatuses.Contains((short)VerificationCodeStatuses.ChuaCapMa))
                {
                    condition.Append($@"AND ""VerificationCode"" IS NULL ");
                }
            }

            if (input.Source != null && input.Source.Any())
                condition.Append($@"AND ""Source"" In ({String.Join(",", input.Source)}) ");

            if (input.ApproveStatuses != null && input.ApproveStatuses.Any())
                condition.Append($@"AND ""ApproveStatus"" IN ({String.Join(",", input.ApproveStatuses)}) ");

            if (input.IsGetDataForInvoiceError.HasValue && input.IsGetDataForInvoiceError.Value)
            {
                condition.Append($@"AND ""InvoiceStatus"" NOT IN ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) ");
            }

            if (!string.IsNullOrEmpty(input.UserNameCreator))
                condition.Append($@"AND ""UserNameCreator"" LIKE '%{input.UserNameCreator}%' ");

            if (!string.IsNullOrEmpty(input.UserNameCreatorErp))
                condition.Append($@"AND ""CreatorErp"" LIKE '%{input.UserNameCreatorErp}%' ");

            if (input.IsDeclared != null && input.IsDeclared.Any())
            {
                condition.Append($@" AND ""IsDeclared"" IN ({String.Join(",", input.IsDeclared)} ) ");
            }

            if (input.FromNumber.HasValue)
                condition.Append($@"AND ""Number"" >= {input.FromNumber.Value} ");

            if (input.ToNumber.HasValue)
                condition.Append($@"AND ""Number"" <= {input.ToNumber.Value} ");

            var sql = new StringBuilder();

            //  "FileDocumentId",   
            sql.Append($@"SELECT * FROM
                            (SELECT * FROM                                                                                 
                                (                                                                                  
                                    SELECT A.*, rownum rn                                                          
                                    FROM                                                                           
                                        (                                                                          
                                            SELECT  ""Id"",                                                         
                                                    ""ErpId"",                                                       
                                                    ""TransactionId"",                                             
                                                    ""TemplateNo"",                                                
                                                    ""SerialNo"",                                                  
                                                    ""InvoiceNo"",                                                 
                                                    ""Number"",                                                    
                                                    ""InvoiceStatus"",                                             
                                                    ""SignStatus"",                                                
                                                    ""ApproveStatus"",                                             
                                                    ""ApproveCancelStatus"",                                             
                                                    ""ApproveDeleteStatus"",                                             
                                                    ""InvoiceDate"",                                               
                                                    ""TotalAmount"",                                          
                                                    ""TotalPaymentAmount"",                                        
                                                    ""PrintedTime"",                                               
                                                    ""FullNameCreator"",                                           
                                                    ""UserNameCreator"",                                           
                                                    ""CreatorErp"",                                                
                                                    ""ReceiverName"",                                                
                                                    ""Note"",                                                      
                                                    ""Source"",                                                    
                                                    ""IsViewed"",                                                  
                                                    ""IsOpened"",
                                                    ""IsDeclared"", 
                                                    ""StatusTvan"",                                                   
                                                    ""VerificationCode"",   
                                                    ""ExtraProperties"" AS ""InvoiceHeaderExtrasJson"",
                                                    COUNT(*) OVER () TotalItems                                   
                                            FROM ""Invoice03Header""                                               
                                            WHERE ""TenantId"" = '{rawTenantId}' AND ""IsDeleted"" = 0 {condition}                       
                                            ORDER BY ""InvoiceDate"" DESC, ""SerialNo"" DESC, ""Number"" DESC, ""CreationTime"" DESC  
                                        ) A                                                                        
                                    WHERE rownum <= {input.SkipCount + input.MaxResultCount} 
                                )
                            WHERE rn > {input.SkipCount} 
                        ) 
                        InvoiceHeader 
                        ORDER BY ""InvoiceDate"" DESC, ""SerialNo"" DESC, ""Number"" DESC ");

            return sql.ToString();
        }

        private async Task<string> GenerateDrawGetByIdQuery(Guid tenantId, long id)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);

            StringBuilder sql = new StringBuilder();

            sql.Append($@"  With Refer AS(                                                                                   
                                SELECT ""InvoiceReferenceId"", ""TemplateNoReference"",                                 
                                       ""SerialNoReference"", ""InvoiceNoReference"",                                   
                                       ""InvoiceDateReference"", ""Note""                                               
                                FROM ""Invoice03Reference""                                                             
                                WHERE ""InvoiceHeaderId"" = {id}                                                        
                            ),                                                                                          
                            Details AS(                                                                                 
                                SELECT ""Id"", ""Index"",     
                                        ""PaymentAmount"", ""ProductId"", ""ProductCode"", ""ProductName"",             
                                        ""UnitId"", ""UnitName"", ""UnitPrice"", ""RoundingUnit"",                      
                                        ""Quantity"",  ""Amount"", ,                        
                                        ""Note""                    
                                FROM ""Invoice03Detail""                                                                
                                WHERE ""InvoiceHeaderId"" = {id}                                                        
                            ),                                                                                          
                            SELECT H.*,                                                  
                                    (                                                                                   
                                        SELECT ( JSON_OBJECT(* RETURNING CLOB) )                                        
                                        FROM Refer                                                                      
                                    ) ""InvoiceReferenceJson"",                                                         
                                    (                                                                                   
                                        SELECT                                                                          
                                        (                                                                               
                                            JSON_ARRAYAGG(                                                              
                                                JSON_OBJECT(                                                            
                                                    D.*,                                                                
                                                    'InvoiceDetailExtrasJson' VALUE(                                    
                                                        SELECT (JSON_ARRAYAGG (JSON_OBJECT(E.* RETURNING CLOB) RETURNING CLOB) )                        
                                                        FROM DetailExtras E                                            
                                                        WHERE E.""InvoiceDetailId"" = D.""Id""                         
                                                    ) 
                                                    RETURNING CLOB                                                     
                                                )   
                                                RETURNING CLOB                                                         
                                            )                                                                          
                                        )                                                                              
                                        FROM Details D                                                                 
                                    ) ""InvoiceDetailsJson""                                                           
                            FROM ""Invoice03Header"" H                                                                 
                            WHERE ""Id"" = {id} AND ""TenantId"" = '{rawTenantId}' ");

            return sql.ToString();
        }

        public async Task<string> GenerateDrawCreateInvoice(CreateInvoice03HeaderEventSendData input)
        {
            var approveStatus = await _invoiceService.GetApproveStatusAsync(input.TenantId);
            var approveCancelStatus = await _invoiceService.GetApproveCancelStatusAsync(input.TenantId);
            var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusAsync(input.TenantId);

            var rawTenantId = OracleExtension.ConvertGuidToRaw(input.TenantId);
            var rawUserId = OracleExtension.ConvertGuidToRaw(input.UserId);

            var tenantInfoMetadata = _invoiceService.GetTenantInfoMetadata(input.InfosNeedCreateInvoice.TenantInfo);

            var headerId = input.Id;
            var lstDetailId = await GetSEQsNextVal(input.InvoiceDetails.Count, SEQ_Name.SEQ_Invoice03Detail);

            StringBuilder rawSql = new StringBuilder();

            #region HEADER EXTRAS
            var headerExtraProperties = "";
            if (input.InvoiceHeaderExtras != null && input.InvoiceHeaderExtras.Count > 0)
            {
                var extraProperties = GetHeaderExtraProperties(input.InvoiceHeaderExtras.Select(x => new CommonHeaderExtraModel
                {
                    FieldName = x.FieldName.Replace("'", "''"),
                    FieldValue = x.FieldValue.Replace("'", "''")
                }).ToList(), input.InfosNeedCreateInvoice.HeaderFields);
                headerExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }

            #endregion


            #region HEADER SQL

            rawSql.Append($@"   BEGIN                                                                     
                                        INSERT INTO ""Invoice03Header"" (                                      
                                            ""Id"",                                                           
                                            ""TenantId"",                                                     
                                            ""CreationTime"",                                                 
                                            ""CreatorId"",                                                    
                                                                                                         
                                            ""Source"",                                                       
                                            ""BatchId"",                                                      
                                            ""ErpId"",                                                        
                                            ""CreatorErp"",                                                   
                                            ""TransactionId"",                                                
                                            ""InvoiceTemplateId"",                                            
                                            ""TemplateNo"",                                                   
                                            ""SerialNo"",                                                     
                                            ""Note"",                                                         
                                            ""InvoiceDate"",                                                  
                                            ""InvoiceStatus"",                                                
                                            ""SignStatus"",                                                   
                                            ""ApproveStatus"",
                                            ""ApproveCancelStatus"",
                                            ""ApproveDeleteStatus"",
                                                                                                         
                                            ""RegistrationHeaderId"",                                         
                                            ""RegistrationDetailId"",                                         
                                            ""Number"",                                                       
                                            ""InvoiceNo"",                                                    
                                                                                                        
                                            ""SellerId"",     
                                            ""SellerCode"", 
                                            ""SellerTaxCode"",                                                
                                            ""SellerAddressLine"",                                            
                                            ""SellerCountryCode"",                                            
                                            ""SellerDistrictName"",                                           
                                            ""SellerCityName"",                                               
                                            ""SellerPhoneNumber"",                                            
                                            ""SellerFaxNumber"",                                              
                                            ""SellerEmail"",                                                  
                                            ""SellerBankName"",                                               
                                            ""SellerBankAccount"",                                            
                                            ""SellerLegalName"",                                              
                                            ""SellerFullName"",                                               
                                                                                                        
                                            ""RoundingCurrency"",                                             
                                            ""FromCurrency"",                                                 
                                            ""CurrencyConversion"",                                           
                                            ""ToCurrency"",                                                   
                                            ""ExchangeRate"",                                                 
                                            ""PaymentDate"",                                                  
                                            ""PaymentAmountWords"",                                           
                                            ""PaymentAmountWordsEn"",                                         
                                            ""TotalAmount"",                                                  
                                            ""TotalPaymentAmount"",                                           
                                            ""FullNameCreator"",                                              
                                            ""UserNameCreator"",                                              
                                                                                                        
                                            ""DeliveryOrderNumber"",                                                   
                                            ""DeliveryOrderBy"",                                                
                                            ""DeliveryBy"",                                               
                                            ""ContractNumber"",                                             
                                            ""TransportationMethod"",

                                            ""ReceiverName"",
                                            ""ReceiverFullName"",
                                            ""ReceiverTaxCode"",
                                            ""ReceiverAddressLine"",
                                                                                          
                                            ""IsActive"",                                                     
                                            ""InvoiceDateYear"",                                              
                                            ""InvoiceDateQuater"",                                            
                                            ""InvoiceDateMonth"",                                             
                                            ""InvoiceDateWeek"",                                              
                                            ""InvoiceDateNumber"",                                            
                                            ""IsOpened"",                                                     
                                            ""IsViewed"",                                                     
                                            ""Partition"" ,
                                            ""ExtraProperties"",
                                            ""InvoiceDeleteSource""
                                        )                                                                       
                                        VALUES(                                                                 
                                            {headerId},                                                                                                                                                   
                                            '{rawTenantId}',                                                                                                                                          
                                            '{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',                                                                                               
                                            '{rawUserId}',                                                                                                                                            
                                            {(short)input.Resource.GetHashCode()},                                                                                                                    
                                            '{OracleExtension.ConvertGuidToRaw(Guid.NewGuid())}',                                                                                                     
                                            '{input.ErpId?.Replace("'", "''")}',                                                                                                                                          
                                            '{input.CreatorErp?.Replace("'", "''")}',                                                                                                                                     
                                            '{_invoiceService.GetTransactionId(input.Resource, input.TransactionId)}',                                                                                
                                            {input.InfosNeedCreateInvoice.Template.Id},                                                                                                               
                                            {input.TemplateNo},                                                                                                                                       
                                            '{input.SerialNo}',                                                                                                                                       
                                            N'{input.Note?.Replace("'", "''")}',                                                                                                                                          
                                            '{input.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',                                                                                           
                                            {(short)InvoiceStatus.Goc.GetHashCode()},                                                                                                                   
                                            {(short)SignStatus.ChoKy.GetHashCode()},                                                                                                                    
                                            {(short)approveStatus.GetHashCode()},
                                            {(short)approveCancelStatus.GetHashCode()},
                                            {(short)approveDeleteStatus.GetHashCode()},
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            '{rawTenantId}',     
                                            '{input.InfosNeedCreateInvoice.TenantInfo.Code}',
                                            '{input.InfosNeedCreateInvoice.TenantInfo.TaxCode}',                                                                                                        
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.Address?.Replace("'", "''")}',                                                                                                       
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.Country?.Replace("'", "''")}',                                                                                                       
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.District?.Replace("'", "''")}',                                                                                                      
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.City?.Replace("'", "''")}',                                                                                                          
                                            '{input.InfosNeedCreateInvoice.TenantInfo.Phone}',                                         
                                            '{input.InfosNeedCreateInvoice.TenantInfo.Fax}',                                             
                                            '{input.InfosNeedCreateInvoice.TenantInfo.Emails?.Replace("'", "''")}',                                         
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.BankName?.Replace("'", "''")}',                                  
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.BankAccount?.Replace("'", "''")}',                            
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.LegalName?.Replace("'", "''")}',                                
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.FullNameVi?.Replace("'", "''")}',                                                                                                   
                                            {input.InfosNeedCreateInvoice.ToCurrency.Rounding},                                                                                                       
                                            '{input.InfosNeedCreateInvoice.FromCurrency.CurrencyCode}',                                                                                               
                                            {input.InfosNeedCreateInvoice.ToCurrency.Conversion},                                                                                                     
                                            '{input.InfosNeedCreateInvoice.ToCurrency.CurrencyCode}',                                                                                                 
                                            {input.ExchangeRate},                                                                                                                                    
                                            '{input.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',                                                                                          
                                            N'{(await _invoiceService.ReadMoneyViAsync(input.TenantId, input.InfosNeedCreateInvoice.ToCurrency, input.TotalPaymentAmount, InvoiceStatus.Goc))?.Trim().Replace("'", "''")}',        
                                            N'{(await _invoiceService.ReadMoneyEnAsync(input.TenantId, input.InfosNeedCreateInvoice.ToCurrency, input.TotalPaymentAmount, InvoiceStatus.Goc))?.Trim().Replace("'", "''")}',        
                                            {input.TotalAmount},                                                                                                                                      
                                            {input.TotalPaymentAmount},                                                                                                                               
                                            '{input.UserFullName?.Replace("'", "''")}',                                                                                                                                   
                                            '{input.UserName?.Replace("'", "''")}',                                                                                                                                         
                                            '{input.DeliveryOrderNumber?.Replace("'", "''")}',                                                                                                                                                                                                                        
                                            N'{input.DeliveryOrderBy?.Replace("'", "''")}',                                                                                                                                 
                                            N'{input.DeliveryBy?.Replace("'", "''")}',                                                                                                                           
                                            '{input.ContractNumber?.Replace("'", "''")}',                                                 
                                            N'{input.TransportationMethod?.Replace("'", "''")}',                                                                                                                                 
                                            N'{input.ReceiverName?.Replace("'", "''")}',                                                                                                                                 
                                            N'{input.ReceiverFullName?.Replace("'", "''")}',                                                                                                                                 
                                            N'{input.ReceiverTaxCode?.Trim()}',                                                                                                                                 
                                            N'{input.ReceiverAddressLine?.Replace("'", "''")}',                                                                                                                                 
                                            1,                                                                                                                                                          
                                            {input.InvoiceDate.Year},                                                                                                                                   
                                            {input.InvoiceDate.GetQuarter()},                                                                                                                           
                                            {input.InvoiceDate.Month},                                                                                                                                  
                                            {input.InvoiceDate.GetWeek()},                                                                                                                              
                                            {input.InvoiceDate.Day},                                                                                                                                    
                                            0,                                                                                                                                                          
                                            0,                                                                                                                                                          
                                            {long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))},
                                            N'{headerExtraProperties}',
                                            0
                                        ); ");

            #endregion

            //StringBuilder sqlValueHeaderExtras = new StringBuilder();
            StringBuilder sqlValueDetails = new StringBuilder();



            #region DETAILS SQL

            var i = 0;
            foreach (var item in input.InvoiceDetails)
            {
                var detailId = lstDetailId[i];

                var unit = (!string.IsNullOrEmpty(item.UnitName) && input.InfosNeedCreateInvoice.Units != null && input.InfosNeedCreateInvoice.Units.ContainsKey(item.UnitName?.Trim()))
                        ? input.InfosNeedCreateInvoice.Units[item.UnitName?.Trim()]
                        : null;

                var productCode = item.ProductCode?.Trim();
                var product = string.IsNullOrEmpty(productCode)
                            ? null
                            : ((input.InfosNeedCreateInvoice.Products != null && input.InfosNeedCreateInvoice.Products.ContainsKey(productCode)) ? input.InfosNeedCreateInvoice.Products[productCode] : null);

                ProductTypeEntity productType = null;
                if (product != null && product.ProductTypeId.HasValue)
                    productType = (input.InfosNeedCreateInvoice.ProductTypes != null && input.InfosNeedCreateInvoice.ProductTypes.ContainsKey(product.ProductTypeId.Value))
                                ? input.InfosNeedCreateInvoice.ProductTypes[product.ProductTypeId.Value]
                                : null;

                #region DETAI EXTRAS SQL
                var detailExtraProperties = "";
                if (item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any())
                {
                    var extraProperties = GetDetailExtraProperties(detailId, item.InvoiceDetailExtras.Select(x => new CommonDetailExtraModel
                    {
                        FieldName = x.FieldName.Replace("'", "''"),
                        FieldValue = x.FieldValue.Replace("'", "''")
                    }).ToList(), input.InfosNeedCreateInvoice.DetailFields);
                    detailExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                }

                #endregion
                var unitName = !string.IsNullOrEmpty(item.UnitName) ? item.UnitName?.Trim().Replace("'", "''") : null;
                sqlValueDetails.Append($@"UNION ALL SELECT                                                                       
                                                        {detailId},                                              
                                                        {item.Index},                                                     
                                                        '{rawTenantId}',                                                  
                                                        {headerId},                                                           
                                                        {item.Amount},                                                   
                                                        N'{item.Note?.Replace("'", "''")}',                                                   
                                                        N'{item.PaymentAmount}',                                          
                                                        {(product == null ? 0 : product.Id)},                             
                                                        N'{item.ProductName.Replace("'", "''")}',                                            
                                                        {item.ProductType},                                            
                                                        '{(String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode)}',                                     
                                                        {item.Quantity},                                                  
                                                        {(unit == null ? 0 : unit.Id)},                                   
                                                        N'{unitName}',                                       
                                                        {item.UnitPrice},                                                 
                                                        {(unit == null ? 4 : unit.Rounding)},                             
                                                        {long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))},                                                                  
                                                        N'{detailExtraProperties}'                                                           
                                                    FROM DUAL ");

                i++;
            }

            #endregion

            if (sqlValueDetails.Length > 0)
            {
                sqlValueDetails = sqlValueDetails.Remove(0, 9);

                rawSql.Append($@"INSERT INTO ""Invoice03Detail"" (
                                              ""Id"",                                 
                                              ""Index"",                              
                                              ""TenantId"",                           
                                              ""InvoiceHeaderId"",                    
                                              ""Amount"",                             
                                              ""Note"",                               
                                              ""PaymentAmount"",                      
                                              ""ProductId"",                          
                                              ""ProductName"",                        
                                              ""ProductType"",                        
                                              ""ProductCode"",                        
                                              ""Quantity"",                          
                                              ""UnitId"",                             
                                              ""UnitName"",                           
                                              ""UnitPrice"",                          
                                              ""RoundingUnit"",                 
                                              ""Partition"",                          
                                              ""ExtraProperties""                        
                                        ) {sqlValueDetails} ; ");
            }

            return rawSql.Append($" END; ").ToString();
        }

        public async Task<string> GenerateDrawCreateInvoiceOldDecree(CreateInvoice03HeaderOldDecreeEventSendData input)
        {
            var approveStatus = await _invoiceService.GetApproveStatusAsync(input.TenantId);
            var approveCancelStatus = await _invoiceService.GetApproveCancelStatusAsync(input.TenantId);
            var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusAsync(input.TenantId);

            var rawTenantId = OracleExtension.ConvertGuidToRaw(input.TenantId);
            var rawUserId = OracleExtension.ConvertGuidToRaw(input.UserId);

            var tenantInfoMetadata = _invoiceService.GetTenantInfoMetadata(input.InfosNeedCreateInvoice.TenantInfo);

            var headerId = input.Id;
            var lstDetailId = await GetSEQsNextVal(input.InvoiceDetails.Count, SEQ_Name.SEQ_Invoice03Detail);

            StringBuilder rawSql = new StringBuilder();

            #region HEADER EXTRAS
            var headerExtraProperties = "";
            if (input.InvoiceHeaderExtras != null && input.InvoiceHeaderExtras.Count > 0)
            {
                var extraProperties = GetHeaderExtraProperties(input.InvoiceHeaderExtras.Select(x => new CommonHeaderExtraModel
                {
                    FieldName = x.FieldName,
                    FieldValue = x.FieldValue.Replace("'", "''")
                }).ToList(), input.InfosNeedCreateInvoice.HeaderFields);
                headerExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }

            #endregion


            #region HEADER SQL

            rawSql.Append($@"   BEGIN                                                                     
                                        INSERT INTO ""Invoice03Header"" (                                      
                                            ""Id"",                                                           
                                            ""TenantId"",                                                     
                                            ""CreationTime"",                                                 
                                            ""CreatorId"",                                                    
                                                                                                         
                                            ""Source"",                                                       
                                            ""BatchId"",                                                      
                                            ""ErpId"",                                                        
                                            ""CreatorErp"",                                                   
                                            ""TransactionId"",                                                
                                            ""InvoiceTemplateId"",                                            
                                            ""TemplateNo"",                                                   
                                            ""SerialNo"",                                                     
                                            ""Note"",                                                         
                                            ""InvoiceDate"",                                                  
                                            ""InvoiceStatus"",                                                
                                            ""SignStatus"",                                                   
                                            ""ApproveStatus"",
                                            ""ApproveCancelStatus"",
                                            ""ApproveDeleteStatus"",
                                                                                                         
                                            ""RegistrationHeaderId"",                                         
                                            ""RegistrationDetailId"",                                         
                                            ""Number"",                                                       
                                            ""InvoiceNo"",                                                    
                                                                                                        
                                            ""SellerId"",     
                                            ""SellerCode"", 
                                            ""SellerTaxCode"",                                                
                                            ""SellerAddressLine"",                                            
                                            ""SellerCountryCode"",                                            
                                            ""SellerDistrictName"",                                           
                                            ""SellerCityName"",                                               
                                            ""SellerPhoneNumber"",                                            
                                            ""SellerFaxNumber"",                                              
                                            ""SellerEmail"",                                                  
                                            ""SellerBankName"",                                               
                                            ""SellerBankAccount"",                                            
                                            ""SellerLegalName"",                                              
                                            ""SellerFullName"",                                               
                                                                                                        
                                            ""RoundingCurrency"",                                             
                                            ""FromCurrency"",                                                 
                                            ""CurrencyConversion"",                                           
                                            ""ToCurrency"",                                                   
                                            ""ExchangeRate"",                                                 
                                            ""PaymentDate"",                                                  
                                            ""PaymentAmountWords"",                                           
                                            ""PaymentAmountWordsEn"",                                         
                                            ""TotalAmount"",                                                  
                                            ""TotalPaymentAmount"",                                           
                                            ""FullNameCreator"",                                              
                                            ""UserNameCreator"",                                              
                                                                                                        
                                            ""DeliveryOrderNumber"",                                                   
                                            ""DeliveryOrderBy"",                                                
                                            ""DeliveryBy"",                                               
                                            ""ContractNumber"",                                             
                                            ""TransportationMethod"",

                                            ""ReceiverName"",
                                            ""ReceiverFullName"",
                                            ""ReceiverTaxCode"",
                                            ""ReceiverAddressLine"",
                                                                                          
                                            ""IsActive"",                                                     
                                            ""InvoiceDateYear"",                                              
                                            ""InvoiceDateQuater"",                                            
                                            ""InvoiceDateMonth"",                                             
                                            ""InvoiceDateWeek"",                                              
                                            ""InvoiceDateNumber"",                                            
                                            ""IsOpened"",                                                     
                                            ""IsViewed"",                                                     
                                            ""Partition"" ,
                                            ""ExtraProperties"",
                                            ""ReferenceInvoiceType"",
                                            ""InvoiceDeleteSource""
                                        )                                                                       
                                        VALUES(                                                                 
                                            {headerId},                                                                                                                                                   
                                            '{rawTenantId}',                                                                                                                                          
                                            '{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',                                                                                               
                                            '{rawUserId}',                                                                                                                                            
                                            {(short)input.Resource.GetHashCode()},                                                                                                                    
                                            '{OracleExtension.ConvertGuidToRaw(Guid.NewGuid())}',                                                                                                     
                                            '{input.ErpId?.Replace("'", "''")}',                                                                                                                                          
                                            '{input.CreatorErp?.Replace("'", "''")}',                                                                                                                                     
                                            '{_invoiceService.GetTransactionId(input.Resource, input.TransactionId)}',                                                                                
                                            {input.InfosNeedCreateInvoice.Template.Id},                                                                                                               
                                            {input.TemplateNo},                                                                                                                                       
                                            '{input.SerialNo}',                                                                                                                                       
                                            N'{input.Note?.Replace("'", "''")}',                                                                                                                                          
                                            '{input.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',                                                                                           
                                            {(short)input.InvoiceStatus.GetHashCode()},                                                                                                                   
                                            {(short)SignStatus.ChoKy.GetHashCode()},                                                                                                                    
                                            {(short)approveStatus.GetHashCode()},
                                            {(short)approveCancelStatus.GetHashCode()},
                                            {(short)approveDeleteStatus.GetHashCode()},
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            '{rawTenantId}',     
                                            '{input.InfosNeedCreateInvoice.TenantInfo.Code}',
                                            '{input.InfosNeedCreateInvoice.TenantInfo.TaxCode}',                                                                                                        
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.Address?.Replace("'", "''")}',                                                                                                       
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.Country?.Replace("'", "''")}',                                                                                                       
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.District?.Replace("'", "''")}',                                                                                                      
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.City?.Replace("'", "''")}',                                                                                                          
                                            '{input.InfosNeedCreateInvoice.TenantInfo.Phone}',                                         
                                            '{input.InfosNeedCreateInvoice.TenantInfo.Fax}',                                             
                                            '{input.InfosNeedCreateInvoice.TenantInfo.Emails}',                                         
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.BankName?.Replace("'", "''")}',                                  
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.BankAccount}',                            
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.LegalName?.Replace("'", "''")}',                                
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.FullNameVi?.Replace("'", "''")}',                                                                                                   
                                            {input.InfosNeedCreateInvoice.ToCurrency.Rounding},                                                                                                       
                                            '{input.InfosNeedCreateInvoice.FromCurrency.CurrencyCode}',                                                                                               
                                            {input.InfosNeedCreateInvoice.ToCurrency.Conversion},                                                                                                     
                                            '{input.InfosNeedCreateInvoice.ToCurrency.CurrencyCode}',                                                                                                 
                                            {input.ExchangeRate},                                                                                                                                    
                                            '{input.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',                                                                                          
                                            N'{(await _invoiceService.ReadMoneyViAsync(input.TenantId, input.InfosNeedCreateInvoice.ToCurrency, input.TotalPaymentAmount, InvoiceStatus.Goc))?.Trim().Replace("'", "''")}',        
                                            N'{(await _invoiceService.ReadMoneyEnAsync(input.TenantId, input.InfosNeedCreateInvoice.ToCurrency, input.TotalPaymentAmount, InvoiceStatus.Goc))?.Trim().Replace("'", "''")}',        
                                            {input.TotalAmount},                                                                                                                                      
                                            {input.TotalPaymentAmount},                                                                                                                               
                                            '{input.UserFullName?.Replace("'", "''")}',                                                                                                                                   
                                            '{input.UserName}',                                                                                                                                         
                                            '{input.DeliveryOrderNumber}',                                                                                                                                                                                                                        
                                            N'{input.DeliveryOrderBy?.Replace("'", "''")}',                                                                                                                                 
                                            N'{input.DeliveryBy?.Replace("'", "''")}',                                                                                                                           
                                            '{input.ContractNumber}',                                                 
                                            N'{input.TransportationMethod?.Replace("'", "''")}',                                                                                                                                 
                                            N'{input.ReceiverName?.Replace("'", "''")}',                                                                                                                                 
                                            N'{input.ReceiverFullName?.Replace("'", "''")}',                                                                                                                                 
                                            N'{input.ReceiverTaxCode?.Trim()}',                                                                                                                                 
                                            N'{input.ReceiverAddressLine.Replace("'", "''")}',                                                                                                                                 
                                            1,                                                                                                                                                          
                                            {input.InvoiceDate.Year},                                                                                                                                   
                                            {input.InvoiceDate.GetQuarter()},                                                                                                                           
                                            {input.InvoiceDate.Month},                                                                                                                                  
                                            {input.InvoiceDate.GetWeek()},                                                                                                                              
                                            {input.InvoiceDate.Day},                                                                                                                                    
                                            0,                                                                                                                                                          
                                            0,                                                                                                                                                          
                                            {long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))},
                                            N'{headerExtraProperties}',
                                            {input.ReferenceInvoiceType.GetHashCode()},
                                            0
                                        ); ");

            #endregion

            //StringBuilder sqlValueHeaderExtras = new StringBuilder();
            StringBuilder sqlValueDetails = new StringBuilder();



            #region DETAILS SQL

            var i = 0;
            foreach (var item in input.InvoiceDetails)
            {
                var detailId = lstDetailId[i];

                var unit = (!string.IsNullOrEmpty(item.UnitName) && input.InfosNeedCreateInvoice.Units != null && input.InfosNeedCreateInvoice.Units.ContainsKey(item.UnitName?.Trim()))
                        ? input.InfosNeedCreateInvoice.Units[item.UnitName?.Trim()]
                        : null;

                var productCode = item.ProductCode?.Trim();
                var product = string.IsNullOrEmpty(productCode)
                            ? null
                            : ((input.InfosNeedCreateInvoice.Products != null && input.InfosNeedCreateInvoice.Products.ContainsKey(productCode)) ? input.InfosNeedCreateInvoice.Products[productCode] : null);

                ProductTypeEntity productType = null;
                if (product != null && product.ProductTypeId.HasValue)
                    productType = (input.InfosNeedCreateInvoice.ProductTypes != null && input.InfosNeedCreateInvoice.ProductTypes.ContainsKey(product.ProductTypeId.Value))
                                ? input.InfosNeedCreateInvoice.ProductTypes[product.ProductTypeId.Value]
                                : null;

                #region DETAI EXTRAS SQL
                var detailExtraProperties = "";
                if (item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any())
                {
                    var extraProperties = GetDetailExtraProperties(detailId, item.InvoiceDetailExtras.Select(x => new CommonDetailExtraModel
                    {
                        FieldName = x.FieldName,
                        FieldValue = x.FieldValue.Replace("'", "''")
                    }).ToList(), input.InfosNeedCreateInvoice.DetailFields);
                    detailExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                }

                #endregion
                var unitName = !string.IsNullOrEmpty(item.UnitName) ? item.UnitName?.Trim().Replace("'", "''") : null;
                sqlValueDetails.Append($@"UNION ALL SELECT                                                                       
                                                        {detailId},                                              
                                                        {item.Index},                                                     
                                                        '{rawTenantId}',                                                  
                                                        {headerId},                                                           
                                                        {item.Amount},                                                   
                                                        N'{item.Note?.Replace("'", "''")}',                                                   
                                                        N'{item.PaymentAmount}',                                          
                                                        {(product == null ? 0 : product.Id)},                             
                                                        N'{item.ProductName.Replace("'", "''")}',                                            
                                                        {item.ProductType},                                            
                                                        '{(String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode)}',                                     
                                                        {item.Quantity},                                                  
                                                        {(unit == null ? 0 : unit.Id)},                                   
                                                        N'{unitName}',                                       
                                                        {item.UnitPrice},                                                 
                                                        {(unit == null ? 4 : unit.Rounding)},                             
                                                        {long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))},                                                                  
                                                        N'{detailExtraProperties}'                                                           
                                                    FROM DUAL ");

                i++;
            }

            #endregion

            if (sqlValueDetails.Length > 0)
            {
                sqlValueDetails = sqlValueDetails.Remove(0, 9);

                rawSql.Append($@"INSERT INTO ""Invoice03Detail"" (
                                              ""Id"",                                 
                                              ""Index"",                              
                                              ""TenantId"",                           
                                              ""InvoiceHeaderId"",                    
                                              ""Amount"",                             
                                              ""Note"",                               
                                              ""PaymentAmount"",                      
                                              ""ProductId"",                          
                                              ""ProductName"",                        
                                              ""ProductType"",                        
                                              ""ProductCode"",                        
                                              ""Quantity"",                          
                                              ""UnitId"",                             
                                              ""UnitName"",                           
                                              ""UnitPrice"",                          
                                              ""RoundingUnit"",                 
                                              ""Partition"",                          
                                              ""ExtraProperties""                        
                                        ) {sqlValueDetails} ; ");
            }

            rawSql.Append($@"  INSERT INTO ""Invoice03ReferenceOldDecree"" (              
                                ""CreationTime"",                           
                                ""InvoiceDateReference"",                                  
                                ""InvoiceHeaderId"",                                      
                                ""InvoiceNoReference"",                                 
                                ""NumberReference"",                                
                                ""SerialNoReference"",                         
                                ""TemplateNoReference"",
                                ""TenantId"",
                                ""InvoiceStatus"",
                                ""Note"",
                                ""Partition""
                            )                                                  
                            VALUES (
                                '{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                '{input.InvoiceReference.InvoiceDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                {input.Id},
                                '{input.InvoiceReference.InvoiceNo}',
                                {int.Parse(input.InvoiceReference.InvoiceNo)},
                                '{input.InvoiceReference.SerialNo}',
                                '{input.InvoiceReference.TemplateNo}',
                                '{rawTenantId}',
                                {(short)input.InvoiceStatus.GetHashCode()},
                                N'{input.InvoiceReference.Note?.Replace("'", "''")}',
                                {long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))}
                            ); ");

            return rawSql.Append($" END; ").ToString();
        }

        public async Task<string> GenerateDrawCreateReplaceInvoice(CreateReplaceInvoice03HeaderEventSendData input)
        {
            var approveStatus = await _invoiceService.GetApproveStatusAsync(input.TenantId);
            var approveCancelStatus = await _invoiceService.GetApproveCancelStatusAsync(input.TenantId);
            var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusAsync(input.TenantId);
            var rawTenantId = OracleExtension.ConvertGuidToRaw(input.TenantId);
            var rawUserId = OracleExtension.ConvertGuidToRaw(input.UserId);
            var rawSellerId = OracleExtension.ConvertGuidToRaw(input.InfosNeedCreateInvoice.InvoiceReference.SellerId);

            var headerId = input.Id;
            var lstDetailId = await GetSEQsNextVal(input.InvoiceDetails.Count, SEQ_Name.SEQ_Invoice03Detail);

            StringBuilder rawSql = new StringBuilder();

            #region HEADER EXTRAPROPERTIES
            var headerExtraProperties = "";
            if (input.InvoiceHeaderExtras != null && input.InvoiceHeaderExtras.Count > 0)
            {
                var extraProperties = GetHeaderExtraProperties(input.InvoiceHeaderExtras.Select(x => new CommonHeaderExtraModel
                {
                    FieldName = x.FieldName,
                    FieldValue = x.FieldValue.Replace("'", "''")
                }).ToList(), input.InfosNeedCreateInvoice.HeaderFields);
                headerExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }
            #endregion

            #region HEADER SQL
            try
            {
                rawSql.Append($@"   BEGIN                                                                     
                                        INSERT INTO ""Invoice03Header"" (                                      
                                            ""Id"",                                                           
                                            ""TenantId"",                                                     
                                            ""CreationTime"",                                                 
                                            ""CreatorId"",                                                    
                                                                                                         
                                            ""Source"",                                                       
                                            ""BatchId"",                                                      
                                            ""ErpId"",                                                        
                                            ""CreatorErp"",                                                   
                                            ""TransactionId"",                                                
                                            ""InvoiceTemplateId"",                                            
                                            ""TemplateNo"",                                                   
                                            ""SerialNo"",                                                     
                                            ""Note"",                                                         
                                            ""InvoiceDate"",                                                  
                                            ""InvoiceStatus"",                                                
                                            ""SignStatus"",                                                   
                                            ""ApproveStatus"",
                                            ""ApproveCancelStatus"",
                                            ""ApproveDeleteStatus"",                                                
                                            ""InvoiceDeleteSource"",                                                
                                                                                                         
                                            ""RegistrationHeaderId"",                                         
                                            ""RegistrationDetailId"",                                         
                                            ""Number"",                                                       
                                            ""InvoiceNo"",                                                    
                                                                                                        
                                            ""SellerId"",     
                                            ""SellerCode"",
                                            ""SellerTaxCode"",                                                
                                            ""SellerAddressLine"",                                            
                                            ""SellerCountryCode"",                                            
                                            ""SellerDistrictName"",                                           
                                            ""SellerCityName"",                                               
                                            ""SellerPhoneNumber"",                                            
                                            ""SellerFaxNumber"",                                              
                                            ""SellerEmail"",                                                  
                                            ""SellerBankName"",                                               
                                            ""SellerBankAccount"",                                            
                                            ""SellerLegalName"",                                              
                                            ""SellerFullName"",                                               
                                                                                                        
                                            ""RoundingCurrency"",                                             
                                            ""FromCurrency"",                                                 
                                            ""CurrencyConversion"",                                           
                                            ""ToCurrency"",                                                   
                                            ""ExchangeRate"",   
                                               
                                            ""PaymentDate"",                                                  
                                            ""PaymentAmountWords"",                                           
                                            ""PaymentAmountWordsEn"",                                         
                                            ""TotalAmount"",                                                  
                                            ""TotalPaymentAmount"",                                           
                                            ""FullNameCreator"",                                              
                                            ""UserNameCreator"",                                              
                                                                                                        
                                            ""DeliveryOrderNumber"",                                                   
                                            ""DeliveryOrderBy"",                                                
                                            ""DeliveryBy"",                                               
                                            ""ContractNumber"",                                            
                                            ""TransportationMethod"",

                                            ""ReceiverName"",
                                            ""ReceiverFullName"",
                                            ""ReceiverTaxCode"",
                                            ""ReceiverAddressLine"",

                                            ""IsActive"",                                                     
                                            ""InvoiceDateYear"",                                              
                                            ""InvoiceDateQuater"",                                            
                                            ""InvoiceDateMonth"",                                             
                                            ""InvoiceDateWeek"",                                              
                                            ""InvoiceDateNumber"",                                            
                                            ""IsOpened"",                                                     
                                            ""IsViewed"",                                                     
                                            ""Partition"",                                                     
                                            ""ExtraProperties""                                                     
                                        )                                                                       
                                        VALUES(                                                                 
                                            {headerId},                                                                                                                                                   
                                            '{rawTenantId}',                                                                                                                                          
                                            '{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',                                                                                               
                                            '{rawUserId}',                                                                                                                                            
                                            {(short)input.Resource.GetHashCode()},                                                                                                                    
                                            '{OracleExtension.ConvertGuidToRaw(Guid.NewGuid())}',                                                                                                     
                                            '{input.ErpId?.Replace("'", "''")}',                                                                                                                                          
                                            '{input.CreatorErp?.Replace("'", "''")}',                                                                                                                                     
                                            '{input.InfosNeedCreateInvoice.InvoiceReference.TransactionId}',                                                                                
                                            {input.InfosNeedCreateInvoice.Template.Id},                                                                                                               
                                            {input.TemplateNo},                                                                                                                                       
                                            '{input.SerialNo}',                                                                                                                                       
                                            N'{input.Note?.Replace("'", "''")}',                                                                                                                                          
                                            '{input.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',                                                                                           
                                            {(short)InvoiceStatus.ThayThe.GetHashCode()},                                                                                                                   
                                            {(short)SignStatus.ChoKy.GetHashCode()},                                                                                                                    
                                            {(short)approveStatus.GetHashCode()},
                                            {(short)approveCancelStatus.GetHashCode()},
                                            {(short)approveDeleteStatus.GetHashCode()},                                                                                                    
                                            0,                                                                                                    
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            '{rawSellerId}',    
                                            '{input.InfosNeedCreateInvoice.TenantInfo.Code}',
                                            '{input.InfosNeedCreateInvoice.TenantInfo.TaxCode}',                                                                                                        
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.Address.Replace("'", "''")}',                                                                                                       
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.Country?.Replace("'", "''")}',                                                                                                       
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.District?.Replace("'", "''")}',                                                                                                      
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.City?.Replace("'", "''")}',                                                                                                          
                                            '{input.InfosNeedCreateInvoice.TenantInfo.Phone}',                                         
                                            '{input.InfosNeedCreateInvoice.TenantInfo.Fax}',                                             
                                            '{input.InfosNeedCreateInvoice.TenantInfo.Emails}',                                         
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.BankName?.Replace("'", "''")}',                                  
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.BankAccount}',                            
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.LegalName?.Replace("'", "''")}',                                
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.FullNameVi?.Replace("'", "''")}',                                                                                                   
                                            {input.InfosNeedCreateInvoice.ToCurrency.Rounding},                                                                                                       
                                            '{input.InfosNeedCreateInvoice.FromCurrency.CurrencyCode}',                                                                                               
                                            {input.InfosNeedCreateInvoice.ToCurrency.Conversion},                                                                                                     
                                            '{input.InfosNeedCreateInvoice.ToCurrency.CurrencyCode}',                                                                                                 
                                            {input.ExchangeRate},                                                                                                                                     
                                            '{input.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',                                                                                          
                                            N'{(await _invoiceService.ReadMoneyViAsync(input.TenantId, input.InfosNeedCreateInvoice.ToCurrency, input.TotalPaymentAmount, InvoiceStatus.Goc))?.Trim().Replace("'", "''")}',        
                                            N'{(await _invoiceService.ReadMoneyEnAsync(input.TenantId, input.InfosNeedCreateInvoice.ToCurrency, input.TotalPaymentAmount, InvoiceStatus.Goc))?.Trim().Replace("'", "''")}',        
                                            {input.TotalAmount},                                                                                                                                      
                                            {input.TotalPaymentAmount},                                                                                                                               
                                            '{input.UserFullName?.Replace("'", "''")}',                                                                                                                                   
                                            '{input.UserName}',                                                                                                                                          
                                            '{input.DeliveryOrderNumber}',                                                                                                                              
                                            N'{input.DeliveryOrderBy?.Replace("'", "''")}',                                                                                                                                 
                                            N'{input.DeliveryBy?.Replace("'", "''")}',                                                                                                                           
                                            '{input.ContractNumber?.Trim()}',                                                                                                                               
                                            N'{input.TransportationMethod?.Replace("'", "''")}', 
                                            N'{input.ReceiverName?.Replace("'", "''")}',                                                                                                                                 
                                            N'{input.ReceiverFullName?.Replace("'", "''")}',                                                                                                                                 
                                            N'{input.ReceiverTaxCode?.Replace("'", "''")}',                                                                                                                                 
                                            N'{input.ReceiverAddressLine.Replace("'", "''")}', 
                                            1,                                                                                                                                                          
                                            {input.InvoiceDate.Year},                                                                                                                                   
                                            {input.InvoiceDate.GetQuarter()},                                                                                                                           
                                            {input.InvoiceDate.Month},                                                                                                                                  
                                            {input.InvoiceDate.GetWeek()},                                                                                                                              
                                            {input.InvoiceDate.Day},                                                                                                                                    
                                            0,                                                                                                                                                          
                                            0,                                                                                                                                                          
                                            {long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))},                                                                                                   
                                            N'{headerExtraProperties}'                                                                                                   
                                        ); ");

            }
            catch (Exception ex)
            {

                throw;
            }

            #endregion

            //StringBuilder sqlValueHeaderExtras = new StringBuilder();
            StringBuilder sqlValueDetails = new StringBuilder();
            //StringBuilder sqlValueDetailExtras = new StringBuilder();

            #region HEADER EXTRAS SQL

            //if (input.InvoiceHeaderExtras != null && input.InvoiceHeaderExtras.Count > 0)
            //{
            //    foreach (var item in input.InvoiceHeaderExtras)
            //    {
            //        //Nếu ko có HeaderExtra thì bỏ qua, ko insert
            //        if (!input.InfosNeedCreateInvoice.HeaderFields.ContainsKey(item.FieldName))
            //            continue;

            //        var field = input.InfosNeedCreateInvoice.HeaderFields[item.FieldName];

            //        sqlValueHeaderExtras.Append($@"UNION ALL SELECT 
            //                                                      {OracleFucntionName.FUNC_GetNextSequenceValue}('""{SEQ_Name.SEQ_Invoice03HeaderExtra}""'),
            //                                                      {headerId},                                                       
            //                                                      { field.Id },                                                   
            //                                                      '{ item.FieldValue }',                                          
            //                                                      '{ rawTenantId }',                                              
            //                                                      { long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm)) },
            //                                                      1,
            //                                                      '{ DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")) }'      
            //                                                FROM DUAL ");
            //    }
            //}

            #endregion

            #region DETAILS SQL

            var i = 0;
            foreach (var item in input.InvoiceDetails)
            {
                var detailId = lstDetailId[i];

                var unit = (!string.IsNullOrEmpty(item.UnitName) && input.InfosNeedCreateInvoice.Units != null && input.InfosNeedCreateInvoice.Units.ContainsKey(item.UnitName?.Trim()))
                        ? input.InfosNeedCreateInvoice.Units[item.UnitName?.Trim()]
                        : null;

                var productCode = item.ProductCode?.Trim();
                var product = string.IsNullOrEmpty(productCode)
                            ? null
                            : ((input.InfosNeedCreateInvoice.Products != null && input.InfosNeedCreateInvoice.Products.ContainsKey(productCode)) ? input.InfosNeedCreateInvoice.Products[productCode] : null);

                ProductTypeEntity productType = null;
                if (product != null && product.ProductTypeId.HasValue)
                    productType = (input.InfosNeedCreateInvoice.ProductTypes != null && input.InfosNeedCreateInvoice.ProductTypes.ContainsKey(product.ProductTypeId.Value))
                                ? input.InfosNeedCreateInvoice.ProductTypes[product.ProductTypeId.Value]
                                : null;

                #region DETAIL EXTRAPROPERTIES
                var detailExtraProperties = "";
                if (item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any())
                {
                    var extraProperties = GetDetailExtraProperties(detailId, item.InvoiceDetailExtras.Select(x => new CommonDetailExtraModel
                    {
                        FieldValue = x.FieldValue.Replace("'", "''"),
                        FieldName = x.FieldName,
                    }).ToList(), input.InfosNeedCreateInvoice.DetailFields);
                    detailExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                }
                #endregion

                var unitName = !string.IsNullOrEmpty(item.UnitName) ? item.UnitName?.Trim().Replace("'", "''") : null;
                sqlValueDetails.Append($@"UNION ALL SELECT   
                                                        {detailId},                                              
                                                        {item.Index},                                                     
                                                        '{rawTenantId}',                                                  
                                                        {headerId},                                                           
                                                        {item.Amount},                                                  
                                                        N'{item.Note?.Replace("'", "''")}',                                                   
                                                        N'{item.PaymentAmount}',                                          
                                                        {(product == null ? 0 : product.Id)},                             
                                                        N'{item.ProductName.Replace("'", "''")}',                                            
                                                        {item.ProductType},                                            
                                                        '{(String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode)}',                                      
                                                        {item.Quantity},                                                    
                                                        {(unit == null ? 0 : unit.Id)},                                   
                                                        N'{unitName}',                                       
                                                        {item.UnitPrice},                                                 
                                                        {(unit == null ? 4 : unit.Rounding)},    
                                                        {long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))},        
                                                        N'{detailExtraProperties}'
                                                    FROM DUAL ");

                i++;
            }

            #endregion

            if (sqlValueDetails.Length > 0)
            {
                sqlValueDetails = sqlValueDetails.Remove(0, 9);

                rawSql.Append($@"INSERT INTO ""Invoice03Detail"" (
                                        ""Id"",                                 
                                        ""Index"",                              
                                        ""TenantId"",                           
                                        ""InvoiceHeaderId"",                    
                                        ""Amount"",                          
                                        ""Note"",                               
                                        ""PaymentAmount"",                      
                                        ""ProductId"",                          
                                        ""ProductName"",                        
                                        ""ProductType"",                        
                                        ""ProductCode"",                        
                                        ""Quantity"",                             
                                        ""UnitId"",                             
                                        ""UnitName"",                           
                                        ""UnitPrice"",                          
                                        ""RoundingUnit"",                  
                                        ""Partition"",                           
                                        ""ExtraProperties""                         
                                ) {sqlValueDetails} ; ");
            }

            #region INVOICE REFERENCE SQL

            rawSql.Append($@"  INSERT INTO ""Invoice03Reference"" (             
                                                    ""CreationTime"",                           
                                                    ""InvoiceDateReference"",                                  
                                                    ""InvoiceHeaderId"",                                      
                                                    ""InvoiceNoReference"",                                 
                                                    ""InvoiceReferenceId"",                           
                                                    ""NumberReference"",                                
                                                    ""SerialNoReference"",                         
                                                    ""TemplateNoReference"",
                                                    ""TenantId"",
                                                    ""InvoiceStatus"",
                                                    ""Note"",
                                                    ""Partition""
                                                )                                                  
                                                VALUES (
                                                    '{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                                    '{input.InfosNeedCreateInvoice.InvoiceReference.InvoiceDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                                    {input.Id},
                                                    '{input.InfosNeedCreateInvoice.InvoiceReference.InvoiceNo}',
                                                    {input.InfosNeedCreateInvoice.InvoiceReference.Id},
                                                    {input.InfosNeedCreateInvoice.InvoiceReference.Number ?? 0},
                                                    '{input.InfosNeedCreateInvoice.InvoiceReference.SerialNo}',
                                                    {input.InfosNeedCreateInvoice.InvoiceReference.TemplateNo},
                                                    '{rawTenantId}',
                                                    {(short)InvoiceStatus.ThayThe.GetHashCode()},
                                                    N'{input.Content?.Replace("'", "''")}',
                                                    {long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))}
                                                ); ");


            #endregion

            if (input.IdFileDocument.HasValue)
            {
                #region INVOICE DOCUMENTINFO SQL

                rawSql.Append($@"  INSERT INTO ""Invoice03DocumentInfo"" (             
                                                    ""TenantId"",
                                                    ""Partition"",
                                                    ""InvoiceHeaderId"",
                                                    ""FileId"",
                                                    ""DocumentNo"",
                                                    ""DocumentDate"",
                                                    ""DocumentReason"",
                                                    ""Type"",
                                                    ""IsUploadFile""
                                                )                                                  
                                                VALUES (
                                                    '{rawTenantId}',
                                                    {long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))},
                                                    {headerId},
                                                    {input.IdFileDocument.Value},
                                                    N'{input.DocumentNo?.Replace("'", "''")}',
                                                    '{input.DocumentDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                                    N'{input.DocumentReason?.Replace("'", "''")}',
                                                    {(short)DocumentTemplateType.Replace.GetHashCode()},
                                                    {(input.IsUploadFile ? 1 : 0)}
                                                ); ");
                #endregion


                #region INVOICE DOCUMENT SQL

                rawSql.Append($@"  UPDATE ""Invoice03Document"" SET ""InvoiceHeaderId"" = {headerId} WHERE ""Id"" = {input.IdFileDocument.Value}; ");

                #endregion
            }


            return rawSql.Append($" END; ").ToString();
        }

        public async Task<string> GenerateDrawUpdateInvoice(UpdateInvoice03HeaderEventSendData input)
        {
            StringBuilder rawSql = new StringBuilder($@"BEGIN ");

            #region HEADER EXTRAPROPERTIES
            var headerExtraProperties = "";
            if (input.InvoiceHeaderExtras != null && input.InvoiceHeaderExtras.Count > 0)
            {
                var extraProperties = GetHeaderExtraProperties(input.InvoiceHeaderExtras.Select(x => new CommonHeaderExtraModel
                {
                    FieldName = x.FieldName,
                    FieldValue = x.FieldValue.Replace("'", "''")
                }).ToList(), input.InfosNeedUpdateInvoice.HeaderFields);
                headerExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }
            #endregion

            #region HEADER SQL

            rawSql.Append($@"   UPDATE ""Invoice03Header""
                                SET 
                                    ""ToCurrency"" = '{input.InfosNeedUpdateInvoice.ToCurrency.CurrencyCode}',
                                    ""InvoiceDate"" = '{input.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                    ""Note"" = N'{input.Note?.Trim().Replace("'", "''")}',
                                    ""RoundingCurrency"" = {input.InfosNeedUpdateInvoice.ToCurrency.Rounding},
                                    ""CurrencyConversion"" = {input.InfosNeedUpdateInvoice.ToCurrency.Conversion},
                                    ""ExchangeRate"" = {input.ExchangeRate},
                                    ""TotalAmount"" = {input.TotalAmount},
                                    ""TotalPaymentAmount"" = {input.TotalPaymentAmount},
                                    ""PaymentAmountWords"" = N'{(await _invoiceService.ReadMoneyViAsync(input.InfosNeedUpdateInvoice.Invoice.TenantId, input.InfosNeedUpdateInvoice.ToCurrency, input.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(input.InfosNeedUpdateInvoice.Invoice.InvoiceStatus))))?.Trim().Replace("'", "''")}',
                                    ""PaymentAmountWordsEn"" = N'{(await _invoiceService.ReadMoneyEnAsync(input.InfosNeedUpdateInvoice.Invoice.TenantId, input.InfosNeedUpdateInvoice.ToCurrency, input.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(input.InfosNeedUpdateInvoice.Invoice.InvoiceStatus))))?.Trim().Replace("'", "''")}',
                                    ""DeliveryBy"" = N'{input.DeliveryBy?.Replace("'", "''")}',
                                    ""DeliveryOrderBy"" = N'{input.DeliveryOrderBy?.Replace("'", "''")}',
                                    ""DeliveryOrderNumber"" = '{input.DeliveryOrderNumber?.Trim()}',
                                    ""ContractNumber"" = '{input.ContractNumber}',
                                    ""TransportationMethod"" = N'{input.TransportationMethod?.Replace("'", "''")}',
                                    ""ReceiverName"" = N'{input.ReceiverName?.Replace("'", "''")}',
                                    ""ReceiverFullName"" = N'{input.ReceiverFullName?.Replace("'", "''")}',
                                    ""ReceiverTaxCode"" = N'{input.ReceiverTaxCode?.Trim()}',
                                    ""ReceiverAddressLine"" = N'{input.ReceiverAddressLine.Replace("'", "''")}',
                                    ""CreatorErp"" = '{input.CreatorErp?.Replace("'", "''")}',
                                    ""ExtraProperties"" = N'{headerExtraProperties}'
                                WHERE ""Id"" = {input.Id}; 
                        ");

            #endregion

            var commandInvoiceDetails = new List<Invoice03DetailModel>();
            foreach (var item in input.InvoiceDetails)
            {
                var productCode = item.ProductCode?.Trim();
                var product = string.IsNullOrEmpty(productCode)
                            ? null
                            : ((input.InfosNeedUpdateInvoice.Products != null && input.InfosNeedUpdateInvoice.Products.ContainsKey(productCode)) ? input.InfosNeedUpdateInvoice.Products[productCode] : null);

                commandInvoiceDetails.Add(new Invoice03DetailModel
                {
                    TenantId = input.InfosNeedUpdateInvoice.Invoice.TenantId,
                    InvoiceHeaderId = input.Id,
                    Index = item.Index,
                    Amount = item.Amount,
                    Note = item.Note,
                    PaymentAmount = item.PaymentAmount,
                    ProductId = product != null ? product.Id : 0,
                    ProductCode = String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode,
                    ProductName = item.ProductName,
                    ProductType = item.ProductType,
                    Quantity = item.Quantity,
                    UnitId = 0,
                    UnitName = item.UnitName?.Trim(),
                    UnitPrice = item.UnitPrice,
                    InvoiceDetailExtras = item.InvoiceDetailExtras?.Select(y => new Invoice03DetailExtraModel
                    {
                        TenantId = input.InfosNeedUpdateInvoice.Invoice.TenantId,
                        InvoiceDetailFieldId = input.InfosNeedUpdateInvoice.DetailFields[y.FieldName].Id,
                        FieldValue = y.FieldValue,
                        FieldName = y.FieldName,
                    }).ToList()
                });
            }

            var rawTenantId = OracleExtension.ConvertGuidToRaw(input.TenantId);

            await GenerateUpdateInvoiceDetailAsync(rawSql, rawTenantId, input.InfosNeedUpdateInvoice.Details, input.InfosNeedUpdateInvoice.DetailFields, commandInvoiceDetails, input.InvoiceDate.Date);

            rawSql.Append($@" END; ");
            return rawSql.ToString();
        }

        public async Task<string> GenerateDrawUpdateReplaceInvoice(UpdateReplaceInvoice03HeaderEventSendData input)
        {
            StringBuilder rawSql = new StringBuilder($@"BEGIN ");

            #region HEADER EXTRAPROPERTIES
            var headerExtraProperties = "";
            if (input.InvoiceHeaderExtras != null && input.InvoiceHeaderExtras.Count > 0)
            {
                var extraProperties = GetHeaderExtraProperties(input.InvoiceHeaderExtras.Select(x => new CommonHeaderExtraModel
                {
                    FieldName = x.FieldName,
                    FieldValue = x.FieldValue.Replace("'", "''")
                }).ToList(), input.InfosNeedUpdateInvoice.HeaderFields);
                headerExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }
            #endregion

            #region HEADER SQL

            rawSql.Append($@"   UPDATE ""Invoice03Header""
                                SET 
                                    ""ToCurrency"" = '{input.InfosNeedUpdateInvoice.ToCurrency.CurrencyCode}',
                                    ""InvoiceDate"" = '{input.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                    ""Note"" = N'{input.Note?.Trim().Replace("'", "''")}',
                                    ""RoundingCurrency"" = {input.InfosNeedUpdateInvoice.ToCurrency.Rounding},
                                    ""CurrencyConversion"" = {input.InfosNeedUpdateInvoice.ToCurrency.Conversion},
                                    ""ExchangeRate"" = {input.ExchangeRate},
                                    ""TotalAmount"" = {input.TotalAmount},
                                    ""TotalPaymentAmount"" = {input.TotalPaymentAmount},
                                    ""PaymentAmountWords"" = N'{await _invoiceService.ReadMoneyViAsync(input.InfosNeedUpdateInvoice.Invoice.TenantId, input.InfosNeedUpdateInvoice.ToCurrency, input.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(input.InfosNeedUpdateInvoice.Invoice.InvoiceStatus)))}',
                                    ""PaymentAmountWordsEn"" = '{await _invoiceService.ReadMoneyEnAsync(input.InfosNeedUpdateInvoice.Invoice.TenantId, input.InfosNeedUpdateInvoice.ToCurrency, input.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(input.InfosNeedUpdateInvoice.Invoice.InvoiceStatus)))}',
                                    ""DeliveryBy"" = N'{input.DeliveryBy?.Replace("'", "''")}',
                                    ""DeliveryOrderBy"" = N'{input.DeliveryOrderBy?.Replace("'", "''")}',
                                    ""DeliveryOrderNumber"" = '{input.DeliveryOrderNumber?.Trim()}',
                                    ""ContractNumber"" = '{input.ContractNumber}',
                                    ""TransportationMethod"" = N'{input.TransportationMethod?.Replace("'", "''")}',
                                    ""ReceiverName"" = N'{input.ReceiverName?.Replace("'", "''")}',
                                    ""ReceiverFullName"" = N'{input.ReceiverFullName?.Replace("'", "''")}',
                                    ""ReceiverTaxCode"" = N'{input.ReceiverTaxCode?.Trim()}',
                                    ""ReceiverAddressLine"" = N'{input.ReceiverAddressLine.Replace("'", "''")}',
                                    ""CreatorErp"" = '{input.CreatorErp?.Replace("'", "''")}',
                                    ""ExtraProperties"" = N'{headerExtraProperties}'
                                WHERE ""Id"" = {input.Id}; 
                        ");

            #endregion

            var commandInvoiceDetails = new List<Invoice03DetailModel>();
            foreach (var item in input.InvoiceDetails)
            {
                var productCode = item.ProductCode?.Trim();
                var product = string.IsNullOrEmpty(productCode)
                            ? null
                            : ((input.InfosNeedUpdateInvoice.Products != null && input.InfosNeedUpdateInvoice.Products.ContainsKey(productCode)) ? input.InfosNeedUpdateInvoice.Products[productCode] : null);

                commandInvoiceDetails.Add(new Invoice03DetailModel
                {
                    Amount = item.Amount,
                    Index = item.Index,
                    InvoiceHeaderId = input.Id,
                    Note = item.Note,
                    PaymentAmount = item.PaymentAmount,
                    ProductId = product != null ? product.Id : 0,
                    ProductCode = String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode,
                    ProductName = item.ProductName,
                    ProductType = item.ProductType,
                    Quantity = item.Quantity,
                    TenantId = input.InfosNeedUpdateInvoice.Invoice.TenantId,
                    UnitId = 0,
                    UnitName = item.UnitName?.Trim(),
                    UnitPrice = item.UnitPrice,
                    InvoiceDetailExtras = item.InvoiceDetailExtras?.Select(y => new Invoice03DetailExtraModel
                    {
                        FieldName = y.FieldName,
                        FieldValue = y.FieldValue,
                        InvoiceDetailFieldId = input.InfosNeedUpdateInvoice.DetailFields[y.FieldName].Id,
                        TenantId = input.InfosNeedUpdateInvoice.Invoice.TenantId,
                    }).ToList()
                });
            }

            var rawTenantId = OracleExtension.ConvertGuidToRaw(input.TenantId);

            await GenerateUpdateInvoiceDetailAsync(rawSql, rawTenantId, input.InfosNeedUpdateInvoice.Details, input.InfosNeedUpdateInvoice.DetailFields, commandInvoiceDetails, input.InvoiceDate.Date);

            //update documentInfo
            //TODO: làm có thể vừa insert vừa update trường hợp mà chưa có dữ liệu trong bảng InvoiceDocumentInfo
            if (input.IdFileDocument.HasValue)
            {
                #region INVOICE DOCUMENTINFO SQL

                rawSql.Append($@"  UPDATE ""Invoice03DocumentInfo""
                               SET             
                                ""Partition"" = {long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))},
                                ""FileId"" = {input.IdFileDocument.Value},
                                ""DocumentNo"" = N'{input.DocumentNo}'
                                ""DocumentDate"" = '{input.DocumentDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                ""DocumentReason"" = N'{input.DocumentReason}',
                                ""IsUploadFile"" = {(input.IsUploadFile ? 1 : 0)}
                               WHERE ""InvoiceHeaderId"" = {input.Id};   ");
                #endregion

            }

            rawSql.Append($@" END; ");
            return rawSql.ToString();
        }


        private async Task GenerateUpdateInvoiceDetailAsync(StringBuilder rawSql, string rawTenantId, IEnumerable<Invoice03DetailEntity> entityDetails, Dictionary<string, Invoice03DetailFieldEntity> entityDetailFields, List<Invoice03DetailModel> modelDetails, DateTime invoiceDate)
        {
            var removeDetails = new List<Invoice03DetailEntity>();

            //group để bỏ trường hợp Index thêm mới
            var duplicate = modelDetails.ToDictionary(x => x.Index, x => x)
                                        .Select(x => x.Key)
                                        .Intersect(entityDetails.Select(x => x.Index));
            var newDetails = modelDetails.Where(x => !duplicate.Contains(x.Index));

            foreach (var item in entityDetails)
            {
                if (duplicate.Contains(item.Index)) // SỬA
                {
                    var detailExtraProperties = "";
                    //lấy dữ liệu hóa đơn ở api để cho vào detail này
                    var detail = modelDetails.FirstOrDefault(x => x.Index == item.Index);

                    if (detail.InvoiceDetailExtras != null && detail.InvoiceDetailExtras.Any())
                    {
                        var extraProperties = GetDetailExtraProperties(item.Id, detail.InvoiceDetailExtras.Select(x => new CommonDetailExtraModel
                        {
                            FieldValue = x.FieldValue.Replace("'", "''"),
                            FieldName = x.FieldName,
                        }).ToList(), entityDetailFields);
                        detailExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                    }

                    rawSql.Append($@"   UPDATE ""Invoice03Detail""
                                        SET
                                            ""Amount"" = {detail.Amount},
                                            ""Index"" = {detail.Index},
                                            ""Note"" = N'{detail.Note?.Replace("'", "''")}',
                                            ""PaymentAmount"" = {detail.PaymentAmount},
                                            ""ProductCode"" = '{detail.ProductCode?.Trim()}',
                                            ""ProductName"" = N'{detail.ProductName.Replace("'", "''")}',
                                            ""ProductType"" = {detail.ProductType},
                                            ""Quantity"" = {detail.Quantity},
                                            ""UnitName"" = N'{detail.UnitName?.Trim().Replace("'", "''")}',
                                            ""UnitPrice"" = {detail.UnitPrice},
                                            ""ExtraProperties"" = N'{detailExtraProperties}'
                                        WHERE ""Id"" = {item.Id};
                                    ");

                    //var entityDetailExtra = detailExtras.ContainsKey(item.Id) ? detailExtras[item.Id] : null;
                    //await UpdateInvoiceDetailExtrasAsync(detail.TenantId, item.Id, detail.InvoiceDetailExtras, entityDetailExtra);
                }
                else
                {
                    //k phải thì bỏ qua, check tiếp vì bị xóa
                    removeDetails.Add(item);
                    //if (detailExtras.ContainsKey(item.Id))
                    //    removeDetailExtras.AddRange(detailExtras[item.Id]);
                }
            }

            // THÊM MỚI
            if (newDetails != null && newDetails.Any())
            {
                var lstDetailId = await GetSEQsNextVal(newDetails.Count(), SEQ_Name.SEQ_Invoice03Detail);

                var i = 0;
                foreach (var item in newDetails)
                {
                    var detailId = lstDetailId[i];

                    var detailExtraProperties = "";
                    if (item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any())
                    {
                        var extraProperties = GetDetailExtraProperties(detailId, item.InvoiceDetailExtras.Select(x => new CommonDetailExtraModel
                        {
                            FieldValue = x.FieldValue.Replace("'", "''"),
                            FieldName = x.FieldName,
                        }).ToList(), entityDetailFields);
                        detailExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                    }

                    var unitName = !string.IsNullOrEmpty(item.UnitName) ? item.UnitName?.Trim()?.Replace("'", "''") : null;
                    rawSql.Append($@"  INSERT INTO ""Invoice03Detail"" 
                                        (
                                            ""Id"",
                                            ""Amount"",
                                            ""Note"",
                                            ""ProductId"",
                                            ""ProductCode"",
                                            ""ProductName"",
                                            ""ProductType"",
                                            ""UnitName"",
                                            ""UnitPrice"",
                                            ""Index"",
                                            ""Quantity"",
                                            ""PaymentAmount"",
                                            ""InvoiceHeaderId"",
                                            ""TenantId"",
                                            ""UnitId"",
                                            ""RoundingUnit"",
                                            ""Partition"",
                                            ""ExtraProperties""
                                        ) VALUES (
                                            {detailId},
                                            {item.Amount},
                                            N'{item.Note?.Trim().Replace("'", "''")}',
                                            {item.ProductId},
                                            '{item.ProductCode?.Trim()?.Replace("'", "''")}',
                                            N'{item.ProductName?.Trim()?.Replace("'", "''")}',
                                            {item.ProductType},
                                            N'{unitName}',
                                            {item.UnitPrice},
                                            {item.Index},
                                            {item.Quantity},
                                            {item.PaymentAmount},
                                            {item.InvoiceHeaderId},
                                            '{rawTenantId}', 
                                            0,
                                            4,
                                            {long.Parse(invoiceDate.ToString(FormatDate._yyyyMMddHHmm))},
                                            {(string.IsNullOrEmpty(detailExtraProperties) ? "null" : $"N'{detailExtraProperties}'")}
                                        ); ");

                    i++;
                }
            }

            // XÓA DETAIL VÀ DETAIL EXTRA (NẾU CÓ)
            if (removeDetails != null && removeDetails.Any())
            {
                rawSql.Append($@"   DELETE FROM ""Invoice03Detail"" WHERE ""Id"" IN ( {String.Join(",", removeDetails.Select(x => x.Id))} ); ");
            }

            //if (removeDetailExtras != null && removeDetailExtras.Any())
            //{
            //    rawSql.Append($@"   DELETE FROM ""Invoice03DetailExtra"" WHERE ""Id"" IN ( { String.Join(",", removeDetailExtras.Select(x => x.Id)) } ); ");
            //}
        }

        private Dictionary<string, string> GetHeaderExtraProperties(List<CommonHeaderExtraModel> invoiceHeaderExtras, Dictionary<string, Invoice03HeaderFieldEntity> entityHeaderFields)
        {
            var headerExtraProperties = new Dictionary<string, string>();

            var headerExtras = new List<InvoiceHeaderExtraModel>();
            foreach (var item in invoiceHeaderExtras)
            {
                //Nếu ko có HeaderExtra thì bỏ qua, ko insert
                if (!entityHeaderFields.ContainsKey(item.FieldName))
                    continue;

                var field = entityHeaderFields[item.FieldName];
                headerExtras.Add(new InvoiceHeaderExtraModel
                {
                    FieldName = item.FieldName,
                    FieldValue = item.FieldValue,
                    InvoiceHeaderFieldId = field.Id
                });

            }
            headerExtraProperties.Add("invoiceHeaderExtras", JsonConvert.SerializeObject(headerExtras));

            return headerExtraProperties;
        }

        private Dictionary<string, string> GetDetailExtraProperties(long detailId, List<CommonDetailExtraModel> invoiceDetailExtras, Dictionary<string, Invoice03DetailFieldEntity> entityDetailFields)
        {
            var detailExtraProperties = new Dictionary<string, string>();

            var detailExtras = new List<InvoiceDetailExtraModel>();
            foreach (var extra in invoiceDetailExtras)
            {
                //Nếu ko có HeaderExtra thì bỏ qua, ko insert
                if (!entityDetailFields.ContainsKey(extra.FieldName))
                    continue;

                var field = entityDetailFields[extra.FieldName];
                detailExtras.Add(new InvoiceDetailExtraModel
                {
                    InvoiceDetailId = detailId,
                    InvoiceDetailFieldId = field.Id,
                    FieldValue = extra.FieldValue,
                    FieldName = field.FieldName,
                });
            }
            detailExtraProperties.Add("invoiceDetailExtras", JsonConvert.SerializeObject(detailExtras));

            return detailExtraProperties;
        }
    }
}
