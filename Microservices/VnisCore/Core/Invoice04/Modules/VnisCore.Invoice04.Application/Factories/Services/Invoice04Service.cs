using Core.Application.Dtos;
using Core.Data;
using Core.ObjectMapping;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Models;
using Core.Tvan.Constants;
using Core.Tvan.Models.Xmls.TCTResponse;
using Dapper;
using Nest;
using Newtonsoft.Json;

using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice04;
using VnisCore.Invoice04.Application.Factories.Models;
using VnisCore.Invoice04.Application.Invoice04.Dto;
using VnisCore.Invoice04.Application.Invoice04.Models;
using VnisCore.Invoice04.Application.Invoice04.Models.Requests.Queries;
using VnisCore.Invoice04.Application.Invoice04.Models.Responses.Queries;
using VnisCore.Invoice04.Application.Invoice04.RabbitMqEventBus.CreateInvoice04.MessageEventData;
using VnisCore.Invoice04.Application.Invoice04.RabbitMqEventBus.CreateReplaceInvoice04.MessageEventData;
using VnisCore.Invoice04.Application.Invoice04.RabbitMqEventBus.UpdateInvoice04.MessageEventData;
using VnisCore.Invoice04.Application.Invoice04.RabbitMqEventBus.UpdateReplaceInvoice04.MessageEventData;
using static Core.Tvan.Models.Xmls.TCTResponse.DLieuInvoiceErrorResponseModel;
using static VnisCore.Invoice04.Application.Factories.Constants.CommonInvoice04Const;

namespace VnisCore.Invoice04.Application.Factories.Services
{
    public class Invoice04Service : IInvoice04Service
    {
        private readonly IAppFactory _appFactory;
        private readonly IElasticClient _elasticClient;
        private readonly IInvoiceService<Invoice04HeaderEntity, Invoice04HeaderFieldEntity, Invoice04DetailFieldEntity> _invoiceService;
        protected IObjectMapper _objectMapper { get; }

        public Invoice04Service(
            IAppFactory appFactory,
            IElasticClient elasticClient,
            IInvoiceService<Invoice04HeaderEntity, Invoice04HeaderFieldEntity, Invoice04DetailFieldEntity> invoiceService,
            IObjectMapper objectMapper
            )
        {
            _appFactory = appFactory;
            _invoiceService = invoiceService;
            _elasticClient = elasticClient;
            _objectMapper = objectMapper;
        }

        public async Task<List<long>> GetSEQsNextVal(int level, string SequenceName)
        {
            var sql = $@"   SELECT ""{SequenceName}"".NEXTVAL
                            FROM(
                               SELECT level
                               FROM dual
                               CONNECT BY level <= {level}
                            ) ";

            var result = await _appFactory.VnisCoreOracle.Connection.QueryAsync<long>(sql);

            return result.ToList();
        }


        public async Task<PagedResultDto<PagingInvoice04Response>> GetListAsync(Guid tenantId, Guid userId, PagingInvoice04Request input)
        {
            var query = await GenerateDrawGetListQuery(tenantId, userId, input);

            var data = await _appFactory.VnisCoreOracle.Connection.QueryAsync<PagingInvoice04Response>(query);

            var result = new PagedResultDto<PagingInvoice04Response>();
            if (data != null && data.Any())
            {
                data.ToList().ForEach(x =>
                {
                    if (!string.IsNullOrEmpty(x.ExtraProperties))
                    {
                        var extraProperties = JsonConvert.DeserializeObject<Dictionary<string, string>>(x.ExtraProperties);
                        var headerExtraProperties = new List<InvoiceHeaderExtraModel>();
                        if (extraProperties.ContainsKey("invoiceHeaderExtras") && !string.IsNullOrEmpty(extraProperties["invoiceHeaderExtras"]))
                        {
                            headerExtraProperties = JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(extraProperties["invoiceHeaderExtras"]);
                        }

                        if (headerExtraProperties.Any())
                        {
                            x.InvoiceHeaderExtras = headerExtraProperties.Select(x => new PagingInvoice04Response.PagingInvoice04HeaderExtraResponseModel
                            {
                                FieldValue = x.FieldValue,
                                FieldName = x.FieldName,
                            }).ToList();
                        }
                    }

                    //x.GDTResponseStatus = 0;

                    //if (x.SerialNo.StartsWith('C'))
                    //{
                    //    x.GDTResponseStatus = x.TvanInfoInvoiceErrorStatus;
                    //}
                    //else if (!string.IsNullOrEmpty(x.TvanInfoInvoiceHasCodeReason) || !string.IsNullOrEmpty(x.TvanInfoCheckInvoiceReason))
                    //{
                    //    x.GDTResponseStatus = -1;
                    //}

                    var errorsMess = new string[] { @$"{(string.IsNullOrEmpty(x.TvanInfoInvoiceErrorReason) ? "" : $"Lý do sai sót: {string.Join("; ", JsonConvert.DeserializeObject<List<LDoHDonModel>>(x.TvanInfoInvoiceErrorReason)?.Select(x => x.MTLoi))}")}",
                                                    @$"{(string.IsNullOrEmpty(x.TvanInfoInvoiceHasCodeReason) ? "" : $"Kết quả kiểm tra dữ liệu hóa đơn có mã: {string.Join("; ", JsonConvert.DeserializeObject<List<DLieuMS01TBaoModel.LDoModel>>(x.TvanInfoInvoiceHasCodeReason).Select(x => x.MTLoi))}")}",
                                                    @$"{(string.IsNullOrEmpty(x.TvanInfoInvoiceWithoutCodeReason) ? "" : $"Kết quả kiểm tra dữ liệu hóa đơn không mã: {string.Join("; ", JsonConvert.DeserializeObject<List<DLieuMS01TBaoModel.LDoModel>>(x.TvanInfoInvoiceWithoutCodeReason).Select(x => x.MTLoi))}")}",
                                                    @$"{(string.IsNullOrEmpty(x.TvanInfoCheckInvoiceReason) ? "" : $"Thông báo về hóa đơn cần rà soát: {x.TvanInfoCheckInvoiceReason}")}" };

                    x.InvoiceErrorMessage = string.Join("\n", errorsMess.Where(x => !string.IsNullOrEmpty(x)));
                });

                result.TotalCount = data.First().TotalItems;
                result.Items = data.ToList();
            }

            return result;
        }

        public async Task<InfosNeedCreateInvoice04Model> GetInfosBeforePublishCreateInvoice04(Guid tenantId, List<string> productCodes, List<string> unitNames)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var andProductCode = new StringBuilder();
            if (productCodes != null && productCodes.Any())
                andProductCode.Append($@" AND ""ProductCode"" IN('{ String.Join("','", productCodes) }') ");

            var andUnitName = new StringBuilder();
            if (unitNames != null && unitNames.Any())
            {
                unitNames = unitNames.Where(x => !string.IsNullOrEmpty(x)).Select(x => x.ToUpper()?.Replace("'", "''")).ToList();

                if (unitNames.Any())
                    andUnitName.Append($@" AND ""NormalizedName"" IN('{ String.Join("','", unitNames) }') ");
            }

            var sql = new StringBuilder();
            sql.Append($@"  WITH Products AS (                                                                     
                                  SELECT ""Id"", ""ProductTypeId"", ""ProductCode""                                    
                                  FROM ""Product""                                                                     
                                  WHERE                                                                                
                                      ""TenantId"" = '{rawTenantId}'                                                   
                                      {andProductCode}                                                                 
                                      AND ""IsDeleted"" = 0                                                            
                              ),                                                                                       
                              ProductTypes AS(                                                                         
                                  SELECT ""Id"", ""HideQuantity"", ""HideUnit"", ""HideUnitPrice""                     
                                  FROM ""ProductType""                                                                 
                                  WHERE                                                                                
                                      ""Id"" IN(SELECT ""ProductTypeId"" FROM Products)                                
                                      AND ""IsDeleted"" = 0                                                            
                              ),                                                                                       
                              Units AS(                                                                                
                                  SELECT ""Id"", ""Name"", ""Rounding""                                                
                                  FROM ""Unit""                                                                        
                                  WHERE                                                                                
                                      ""TenantId"" = '{rawTenantId}'                                                   
                                      {andUnitName}                                                                    
                                      AND ""IsDeleted"" = 0                                                            
                              ),                                                                                       
                              HeaderFields AS(                                                                         
                                  SELECT ""Id"", ""FieldName""                                                         
                                  FROM ""Invoice04HeaderField""                                                        
                                  WHERE ""TenantId"" = '{rawTenantId}' AND ""IsDeleted"" = 0                                                
                              ),                                                                                       
                              DetailFields AS(                                                                         
                                  SELECT ""Id"", ""FieldName""                                                         
                                  FROM ""Invoice04DetailField""                                                        
                                  WHERE ""TenantId"" = '{rawTenantId}' AND ""IsDeleted"" = 0                                                
                              )                                                                                        
                              SELECT 1 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM Products    
                              UNION ALL                                                                                                    
                              SELECT 2 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM ProductTypes
                              UNION ALL                                                                                                    
                              SELECT 3 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM Units       
                              UNION ALL                                                                                                    
                              SELECT 4 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM HeaderFields
                              UNION ALL                                                                                                    
                              SELECT 5 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM DetailFields ");


            var data = await _appFactory.VnisCoreOracle.Connection.QueryAsync<TypeData>(sql.ToString());
            var result = new InfosNeedCreateInvoice04Model();

            var products = data.First(x => x.Type == 1);
            var productTypes = data.First(x => x.Type == 2);
            var units = data.First(x => x.Type == 3);
            var headerFields = data.First(x => x.Type == 4);
            var detailFields = data.First(x => x.Type == 5);

            if (!String.IsNullOrEmpty(products.Data))
                result.Products = JsonConvert.DeserializeObject<List<ProductEntity>>(products.Data).ToDictionary(x => x.ProductCode, x => x);

            if (!String.IsNullOrEmpty(productTypes.Data))
                result.ProductTypes = JsonConvert.DeserializeObject<List<ProductTypeEntity>>(productTypes.Data).ToDictionary(x => x.Id, x => x);

            if (!String.IsNullOrEmpty(units.Data))
                result.Units = JsonConvert.DeserializeObject<List<UnitEntity>>(units.Data).ToDictionary(x => x.Name, x => x);

            if (!String.IsNullOrEmpty(headerFields.Data))
                result.HeaderFields = JsonConvert.DeserializeObject<List<Invoice04HeaderFieldEntity>>(headerFields.Data).ToDictionary(x => x.FieldName, x => x);

            if (!String.IsNullOrEmpty(detailFields.Data))
                result.DetailFields = JsonConvert.DeserializeObject<List<Invoice04DetailFieldEntity>>(detailFields.Data).ToDictionary(x => x.FieldName, x => x);

            return result;
        }

        public async Task<InfosNeedUpdateInvoice04Model> GetInfosBeforePublishUpdateInvoice04(Guid tenantId, long invoiceId, List<string> productCodes, List<string> unitNames)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var andProductCode = new StringBuilder();
            if (productCodes != null && productCodes.Any())
                andProductCode.Append($@" AND ""ProductCode"" IN('{ String.Join("','", productCodes) }') ");

            var andUnitName = new StringBuilder();
            if (unitNames != null && unitNames.Any())
            {
                unitNames = unitNames.Where(x => !string.IsNullOrEmpty(x)).Select(x => x.ToUpper()?.Replace("'", "''")).ToList();

                if (unitNames.Any())
                    andUnitName.Append($@" AND ""NormalizedName"" IN('{ String.Join("','", unitNames) }') ");
            }

            var sql = new StringBuilder();
            sql.Append($@"  WITH Products AS (                                                                     
                                SELECT ""Id"", ""ProductTypeId"", ""ProductCode""                                    
                                FROM ""Product""                                                                     
                                WHERE                                                                                
                                    ""TenantId"" = '{rawTenantId}'                                                   
                                    {andProductCode}                                                                 
                                    AND ""IsDeleted"" = 0                                                            
                            ),                                                                                       
                            ProductTypes AS(                                                                         
                                SELECT ""Id"", ""HideQuantity"", ""HideUnit"", ""HideUnitPrice""                     
                                FROM ""ProductType""                                                                 
                                WHERE                                                                                
                                    ""Id"" IN(SELECT ""ProductTypeId"" FROM Products)                                
                                    AND ""IsDeleted"" = 0                                                            
                            ),                                                                                       
                            HeaderFields AS(                                                                         
                                SELECT ""Id"", ""FieldName""                                                         
                                FROM ""Invoice04HeaderField""                                                        
                                WHERE ""TenantId"" = '{rawTenantId}' AND ""IsDeleted"" = 0                                                
                            ),                                                                                       
                            DetailFields AS(                                                                         
                                SELECT ""Id"", ""FieldName""                                                         
                                FROM ""Invoice04DetailField""                                                        
                                WHERE ""TenantId"" = '{rawTenantId}' AND ""IsDeleted"" = 0                                                
                            ),
                            Details AS(                                                                         
                                SELECT *                                                         
                                FROM ""Invoice04Detail""                                                        
                                WHERE ""InvoiceHeaderId"" = {invoiceId}                                               
                            )
                            SELECT 1 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM Products    
                            UNION ALL                                                                                                    
                            SELECT 2 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM ProductTypes
                            UNION ALL                                                                                                    
                            SELECT 3 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM HeaderFields
                            UNION ALL                                                                                                    
                            SELECT 4 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM DetailFields
                            UNION ALL
                            SELECT 5 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM Details                    ");


            var data = await _appFactory.VnisCoreOracle.Connection.QueryAsync<TypeData>(sql.ToString());
            var result = new InfosNeedUpdateInvoice04Model();

            var products = data.First(x => x.Type == 1);
            var productTypes = data.First(x => x.Type == 2);
            var headerFields = data.First(x => x.Type == 3);
            var detailFields = data.First(x => x.Type == 4);
            var details = data.First(x => x.Type == 5);

            if (!String.IsNullOrEmpty(products.Data))
                result.Products = JsonConvert.DeserializeObject<List<ProductEntity>>(products.Data).ToDictionary(x => x.ProductCode, x => x);

            if (!String.IsNullOrEmpty(productTypes.Data))
                result.ProductTypes = JsonConvert.DeserializeObject<List<ProductTypeEntity>>(productTypes.Data).ToDictionary(x => x.Id, x => x);

            if (!String.IsNullOrEmpty(headerFields.Data))
                result.HeaderFields = JsonConvert.DeserializeObject<List<Invoice04HeaderFieldEntity>>(headerFields.Data).ToDictionary(x => x.FieldName, x => x);

            if (!String.IsNullOrEmpty(detailFields.Data))
                result.DetailFields = JsonConvert.DeserializeObject<List<Invoice04DetailFieldEntity>>(detailFields.Data).ToDictionary(x => x.FieldName, x => x);

            if (!String.IsNullOrEmpty(details.Data))
            {
                var detailInfos = JsonConvert.DeserializeObject<List<Invoice04DetailInfo>>(details.Data);
                if (detailInfos != null && detailInfos.Any())
                {
                    result.Details = detailInfos.Select(x => new Invoice04DetailEntity
                    {
                        Id = x.Id,
                        Amount = x.Amount,
                        Index = x.Index,
                        Note = x.Note,
                        InvoiceHeaderId = x.InvoiceHeaderId,
                        RoundingUnit = x.RoundingUnit,
                        ProductId = x.ProductId,
                        PaymentAmount = x.PaymentAmount,
                        ProductCode = x.ProductCode?.Trim(),
                        ProductName = x.ProductName,
                        Partition = x.Partition,
                        Quantity = x.Quantity,
                        UnitName = x.UnitName?.Trim(),
                        UnitPrice = x.UnitPrice,
                        ExtraProperties = !string.IsNullOrEmpty(x.ExtraProperties) ? new ExtraPropertyDictionary(JsonConvert.DeserializeObject<Dictionary<string, object>>(x.ExtraProperties)) : null
                    }).ToList();
                }
            }

            return result;
        }

        public async Task<GetInvoice04Response> GetByIdElasticAsync(Guid tenantId, long invoiceId)
        {
            var data = (await _elasticClient.SearchAsync<GetInvoice04Response>(s => s
            .Query(q => q
                        .Bool(b => b
                            .Must(
                                bm => bm.Match(p => p
                                .Field(f => f.TenantId)
                                .Query(tenantId.ToString())),
                                bm => bm.Match(p => p
                                .Field(f => f.Id)
                                .Query(invoiceId.ToString()))
                                )
                            ))))
            .Documents.FirstOrDefault();

            return data;
        }

        private async Task<string> GenerateDrawGetListQuery(Guid tenantId, Guid userId, PagingInvoice04Request input)
        {
            var condition = new StringBuilder();
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var rawUserId = OracleExtension.ConvertGuidToRaw(userId);

            if (input.IsNullInvoice)
                condition.Append($@"AND ""Number"" IS NULL ");
            else
                condition.Append($@"AND ""Number"" IS NOT NULL ");

            if (!string.IsNullOrEmpty(input.Keyword))
            {
                var q = input.Keyword.Trim()?.Replace("'", "''").ToLower();
                int.TryParse(q, out int outInvoiceNo);

                condition.Append($@"AND (
                                    LOWER(""UserNameCreator"") LIKE LOWER(N'%{q}%') 
                                    OR LOWER(""BuyerEmail"") LIKE LOWER('%{q}%') 
                                    OR LOWER(""BuyerName"") LIKE LOWER(N'%{q}%') 
                                    OR LOWER(""BuyerFullName"") LIKE LOWER(N'%{q}%') 
                                    OR ""Number"" = {outInvoiceNo} 
                                    OR LOWER(""SerialNo"") LIKE LOWER('%{q}%') 
                                    OR ""ErpId"" LIKE '%{q}%' 
                                    OR ""TransactionId"" LIKE '%{q}%' 
                                    OR ""CreatorErp"" LIKE N'%{q}%' 
                                    OR ""BuyerTaxCode"" LIKE N'%{q}%' 
                                    ) ");

                //$"OR TemplateNo LIKE '%{input.Keyword}%' 
                //$"OR InvoiceStatus LIKE '%{input.Keyword}%' 
                //$"OR SignStatus LIKE '%{input.Keyword}%' 

            }

            if (input.InvoiceTemplateIds != null && input.InvoiceTemplateIds.Any())
                condition.Append($@"AND ""InvoiceTemplateId"" in ({string.Join(",", input.InvoiceTemplateIds)}) ");


            if (input.CreateFromDate.HasValue)
            {
                var createFromDate = input.CreateFromDate.Value.Date.ToUniversalTime();
                condition.Append($@"AND ""InvoiceDate"" >= '{createFromDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' ");
            }

            if (input.CreateToDate.HasValue)
            {
                var createToDate = input.CreateToDate.Value.Date.AddDays(1);
                condition.Append($@"AND ""InvoiceDate"" < '{createToDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' ");
            }

            if (input.IssuedTime.HasValue)
            {
                var issueFromDate = input.IssuedTime.Value.Date;
                condition.Append($@"AND ""IssuedTime"" IS NOT NULL 
                                    AND ""IssuedTime"" = '{issueFromDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' ");
            }

            if (input.IssueFromDate.HasValue)
            {
                var issueFromDate = input.IssueFromDate.Value.Date;
                condition.Append($@"AND ""IssuedTime"" IS NOT NULL 
                                    AND ""IssuedTime"" >= '{issueFromDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' ");
            }

            if (input.IssueToDate.HasValue)
            {
                var issueToDate = input.IssueToDate.Value.Date.AddDays(1);
                condition.Append($@"AND ""IssuedTime"" IS NOT NULL 
                                    AND ""IssuedTime"" < '{issueToDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' ");
            }

            if (input.CancelFromDate.HasValue)
                condition.Append($@"AND 
                                    (
                                        (
                                            ""CancelTime"" IS NOT NULL 
                                            AND ""CancelTime"" >= '{input.CancelFromDate.Value.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' 
                                            AND ""InvoiceStatus"" = {(short)InvoiceStatus.XoaBo}
                                        )
                                        OR 
                                        (
                                            ""DeleteTime"" IS NOT NULL 
                                            AND ""DeleteTime"" >= '{input.CancelFromDate.Value.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' 
                                            AND ""InvoiceStatus"" = {(short)InvoiceStatus.XoaBo}
                                        )
                                    )");

            if (input.CancelToDate.HasValue)
            {
                condition.Append($@"AND 
                                    (
                                        (
                                            ""CancelTime"" IS NOT NULL 
                                            AND ""CancelTime"" < '{input.CancelToDate.Value.Date.AddDays(1).ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' 
                                            AND ""InvoiceStatus"" = {(short)InvoiceStatus.XoaBo}
                                        )
                                        OR 
                                        (
                                            ""DeleteTime"" IS NOT NULL 
                                            AND ""DeleteTime"" < '{input.CancelToDate.Value.Date.AddDays(1).ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' 
                                            AND ""InvoiceStatus"" = {(short)InvoiceStatus.XoaBo}
                                        )
                                    )");
            }

            //if (input.IssuedAt.HasValue)
            //{
            //    var issuedAt = input.IssuedAt.Value.Date;
            //    items = items.Where(x => x.IssuedAt != null && x.IssuedAt.Value.Date == issuedAt);
            //}

            if (!string.IsNullOrEmpty(input.Customers))
            {
                var customers = input.Customers?.Trim()?.Replace("'", "''");
                condition.Append($@"AND (""BuyerTaxCode"" LIKE '%{customers}%'  
                                        OR LOWER(""BuyerEmail"") LIKE LOWER('%{customers}%') 
                                    ) ");
            }

            if (!string.IsNullOrEmpty(input.Customer))
            {
                var customer = input.Customer?.Trim()?.Replace("'", "''");
                condition.Append($@"AND (LOWER(""BuyerFullName"") LIKE LOWER(N'%{customer}%') 
                                        OR LOWER(""BuyerName"") LIKE LOWER('%{customer}%') 
                                        OR LOWER(""BuyerEmail"") LIKE LOWER('%{customer}%') 
                                    ) ");
            }

            // Tìm kiếm theo InvoiceTemplateIds
            var type = VnisType._04HGDL;
            if (input.InvoiceTemplateIds != null && input.InvoiceTemplateIds.Any())
                condition.Append($@" AND ""InvoiceTemplateId"" in ({string.Join(',', input.InvoiceTemplateIds)}) ");
            else
            {
                //lấy danh sách mẫu dc xem
                condition.Append(@$" AND ""InvoiceTemplateId"" in 
                                    (
                                        SELECT  T.""Id"" 
                                        FROM ""InvoiceTemplate"" T
                                        INNER JOIN(
                                            SELECT ""InvoiceTemplateId""
                                            FROM ""UserReadTemplate""
                                            WHERE ""UserId"" = '{rawUserId}' AND ""InvoiceTemplateId"" IS NOT NULL
                                        )
                                        A ON T.""Id"" = A.""InvoiceTemplateId""
                                        WHERE T.""TenantId"" = '{rawTenantId}' AND T.""IsDeleted"" = 0 
                                        AND T.""Type"" = {type.GetHashCode()}
                                    ) ");
            }

            if (!string.IsNullOrEmpty(input.InvoiceNo))
            {
                int.TryParse(input.InvoiceNo, out int outInvoiceNo);
                condition.Append($@"AND ""Number"" = {outInvoiceNo} ");
            }

            if (input.TotalPaymentAmount.HasValue)
            {
                condition.Append($@"AND ""TotalPaymentAmount"" = {input.TotalPaymentAmount.Value} ");
            }

            if (input.InvoiceStatuses != null && input.InvoiceStatuses.Any())
                condition.Append($@"AND ""InvoiceStatus"" IN ({String.Join(",", input.InvoiceStatuses)}) ");

            if (input.IsGetDataForInvoiceError.HasValue && input.IsGetDataForInvoiceError.Value)
            {
                condition.Append($@"AND ""InvoiceStatus"" NOT IN ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) ");
            }

            if (input.SignStatuses != null && input.SignStatuses.Any())
                condition.Append($@"AND ""SignStatus"" IN ({String.Join(",", input.SignStatuses)}) ");

            if (input.VerificationCodeStatuses != null && input.VerificationCodeStatuses.Any())
            {
                if (input.VerificationCodeStatuses.Contains((short)VerificationCodeStatuses.DaCapMa)
                    && !input.VerificationCodeStatuses.Contains((short)VerificationCodeStatuses.ChuaCapMa))
                {
                    condition.Append($@"AND ""VerificationCode"" IS NOT NULL ");
                }
                else if (!input.VerificationCodeStatuses.Contains((short)VerificationCodeStatuses.DaCapMa)
                    && input.VerificationCodeStatuses.Contains((short)VerificationCodeStatuses.ChuaCapMa))
                {
                    condition.Append($@"AND ""VerificationCode"" IS NULL ");
                }
            }

            if (input.ApproveStatuses != null && input.ApproveStatuses.Any())
                condition.Append($@"AND ""ApproveStatus"" IN ({String.Join(",", input.ApproveStatuses)}) ");

            if (!string.IsNullOrEmpty(input.TransactionId))
                condition.Append($@"AND ""TransactionId"" = '{input.TransactionId}' ");

            if (!string.IsNullOrEmpty(input.ErpId))
                condition.Append($@"AND ""ErpId"" = '{input.ErpId}' ");

            if (input.FromNumber.HasValue)
                condition.Append($@"AND ""Number"" >= {input.FromNumber.Value} ");

            if (input.ToNumber.HasValue)
                condition.Append($@"AND ""Number"" <= {input.ToNumber.Value} ");

            if (!string.IsNullOrEmpty(input.UserNameCreator))
                condition.Append($@"AND ""UserNameCreator"" LIKE '%{input.UserNameCreator}%' ");

            if (!string.IsNullOrEmpty(input.UserNameCreatorErp))
                condition.Append($@"AND ""CreatorErp"" LIKE '%{input.UserNameCreatorErp}%' ");

            if (input.PrintedTime.HasValue)
            {
                condition.Append($@"AND ""PrintedTime"" IS NOT NULL 
                                          AND ""PrintedTime"" >= '{input.PrintedTime.Value.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' 
                                          AND ""PrintedTime"" < DATE '{input.PrintedTime.Value.ToString("yyyyy-MM-dd", CultureInfo.GetCultureInfo("en-US"))}' + 1");
            }

            if (!string.IsNullOrEmpty(input.UserNamePrinted))
                condition.Append($@"AND ""UserNamePrinted"" LIKE '%{input.UserNamePrinted}%' ");

            if (!string.IsNullOrEmpty(input.BuyerTaxCode))
                condition.Append($@"AND ""BuyerTaxCode"" LIKE '%{input.BuyerTaxCode}%' ");

            if (input.IsDeclared != null && input.IsDeclared.Any())
            {
                condition.Append($@" AND ""IsDeclared"" IN ({String.Join(",", input.IsDeclared)} ) ");
            }


            var sql = new StringBuilder();

            //  "FileDocumentId",   
            sql.Append($@"SELECT * FROM
                            (SELECT * FROM (
                                SELECT A.*, rownum rn                                                          
                                FROM                                                                           
                                    (                                                                          
                                        SELECT  ""Id"",                                                         
                                                ""ErpId"",                                                       
                                                ""TransactionId"",                                             
                                                ""TemplateNo"",                                                
                                                ""SerialNo"",                                                  
                                                ""InvoiceNo"",                                                 
                                                ""Number"",                                                    
                                                ""InvoiceStatus"",                                             
                                                ""SignStatus"",                                                
                                                ""ApproveStatus"",                                             
                                                ""ApproveCancelStatus"",                                             
                                                ""ApproveDeleteStatus"",                                             
                                                ""InvoiceDate"",                                               
                                                ""TotalAmount"",                                           
                                                ""TotalPaymentAmount"",                                        
                                                ""BuyerName"",                                             
                                                ""BuyerFullName"",                                             
                                                ""BuyerEmail"",                                                
                                                ""BuyerTaxCode"",
                                                ""EconomicContractNumber"",
                                                ""EconomicContractDate"",
                                                ""DeliveryBy"",
                                                ""DeliveryOrderBy"",
                                                ""ContractNumber"",
                                                ""TransportationMethod"",
                                                ""PrintedTime"",                                               
                                                ""FullNameCreator"",                                           
                                                ""UserNameCreator"",                                           
                                                ""CreatorErp"",                                                
                                                ""Note"",                                                      
                                                ""Source"",                                                    
                                                ""IsViewed"",                                                  
                                                ""IsOpened"",   
                                                ""VerificationCode"",   
                                                ""IsDeclared"",                                                  
                                                ""ExtraProperties"",                                                  
                                                COUNT(*) OVER () TotalItems                                   
                                        FROM ""Invoice04Header""                                               
                                        WHERE ""TenantId"" = '{rawTenantId}' AND ""IsDeleted"" = 0 {condition}                     
                                        ORDER BY ""InvoiceDate"" DESC, ""SerialNo"" DESC, ""Number"" DESC, ""CreationTime"" DESC  
                                    ) A                                                                        
                                WHERE rownum <= {input.SkipCount + input.MaxResultCount} 
                                )
                            WHERE rn > {input.SkipCount} 
                        ) 
                        InvoiceHeader 
                        --Reference--
                        LEFT JOIN
                        (
                            SELECT ""InvoiceReferenceId"" as IdReference, ""InvoiceDateReference"", ""InvoiceHeaderId"", ""TemplateNoReference"", ""SerialNoReference"", ""InvoiceNoReference"", ""NumberReference""
                            FROM ""Invoice04Reference""
                            WHERE ""TenantId"" = '{rawTenantId}'
                        ) InvoiceReference
                        ON InvoiceHeader.""Id"" = InvoiceReference.""InvoiceHeaderId""
                        --ReferenceOldDecree--
                        LEFT JOIN
                        (
                            SELECT ""InvoiceDateReference"", ""InvoiceHeaderId"", ""TemplateNoReference"", ""SerialNoReference"", ""InvoiceNoReference"", ""NumberReference""
                            FROM ""Invoice04ReferenceOldDecree""
                            WHERE ""TenantId"" = '{rawTenantId}'
                        ) InvoiceReferenceOldDecree
                        ON InvoiceHeader.""Id"" = InvoiceReferenceOldDecree.""InvoiceHeaderId""
                        --có mã(lỗi 04 - KTDL)
                        LEFT JOIN
                        (
                            SELECT ""Reason"" as TvanInfoInvoiceHasCodeReason , ""InvoiceHeaderId""
                            FROM ""TvanInfoInvoice04HasCode""
                            WHERE ""TenantId"" = '{rawTenantId}' AND (""MessageTypeCode"" = {MLTDiep._204.GetHashCode()} OR ""MessageTypeCode"" = {(int)MLTDiep.Error}) AND ""Reason"" IS NOT NULL AND ""IsDeleted"" = 0
                        ) TvanInfoInvoiceHasCode
                        ON InvoiceHeader.""Id"" = TvanInfoInvoiceHasCode.""InvoiceHeaderId""
                        -- Rà soát
                        LEFT JOIN
                        (
                            SELECT ""Reason"" as TvanInfoCheckInvoiceReason , ""InvoiceHeaderId""
                            FROM ""TvanInfoCheckInvoice04""
                            WHERE ""TenantId"" = '{rawTenantId}' AND ""MessageTypeCode"" = {MLTDiep._302.GetHashCode()} AND ""Reason"" IS NOT NULL AND ""IsDeleted"" = 0
                        ) TvanInfoCheckInvoice
                        ON InvoiceHeader.""Id"" = TvanInfoCheckInvoice.""InvoiceHeaderId""
                        --Không mã(lỗi 04 - KTDL)
                        LEFT JOIN
                        (
                            SELECT ""Reason"" as TvanInfoInvoiceWithoutCodeReason , ""InvoiceHeaderId""
                            FROM ""TvanInfoInvoice04WithoutCode""
                            WHERE ""TenantId"" = '{rawTenantId}'  AND (""MessageTypeCode"" = {MLTDiep._204.GetHashCode()} OR ""MessageTypeCode"" = {(int)MLTDiep.Error}) AND ""Reason"" IS NOT NULL AND ""IsDeleted"" = 0
                        ) TvanInfoInvoiceWithoutCode
                        ON InvoiceHeader.""Id"" = TvanInfoInvoiceWithoutCode.""InvoiceHeaderId""   
                        -- sai sót
                        --LEFT JOIN 
                        --(
                        --    SELECT ""Reason"" as TvanInfoInvoiceErrorReason, ""InvoiceHeaderId"", ""Status"" as TvanInfoInvoiceErrorStatus, ""Action"" as InvoiceErrorType
                        --    FROM ""TvanInfoInvoice04Error""
                        --    WHERE ""TenantId"" = '{rawTenantId}' AND ""MessageTypeCode"" = {MLTDiep._301.GetHashCode()}
                        --) TvanInfoInvoiceError
                        --ON InvoiceHeader.""Id"" = TvanInfoInvoiceError.""InvoiceHeaderId""
                        LEFT JOIN
                        ( SELECT * FROM (
                            SELECT a.""Id"" as InvoiceHeaderId, Invoice04Error.""Action"" as InvoiceErrorType, Invoice04Error.""SignStatus"" as InvoiceErrorSignStatus, Invoice04Error.""TvanStatus"" as InvoiceErrorTvanStatus 
                            FROM ""Invoice04Header"" a 
                            FULL OUTER JOIN ""Invoice04Error"" Invoice04Error
                            ON a.""Id"" = Invoice04Error.""InvoiceHeaderId""
                            WHERE Invoice04Error.""Id"" IS NULL OR Invoice04Error.""Id"" IN 
                                (SELECT ""Id"" FROM ""Invoice04Error"" c 
                                    WHERE c.""InvoiceHeaderId"" = a.""Id"" AND c.""IsDeleted"" = 0 
                                    ORDER BY c.""CreationTime"" DESC FETCH FIRST 1 ROWS ONLY
                                )
                            )
                        ) e
                        ON InvoiceHeader.""Id"" = e.InvoiceHeaderId 
                        ORDER BY ""InvoiceDate"" DESC, ""SerialNo"" DESC, ""Number"" DESC ");

            return sql.ToString();
        }

        public async Task<string> GenerateDrawCreateInvoice(CreateInvoice04HeaderEventSendData input)
        {
            var approveStatus = await _invoiceService.GetApproveStatusAsync(input.TenantId);
            var approveCancelStatus = await _invoiceService.GetApproveCancelStatusAsync(input.TenantId);
            var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusAsync(input.TenantId);

            var rawTenantId = OracleExtension.ConvertGuidToRaw(input.TenantId);
            var rawUserId = OracleExtension.ConvertGuidToRaw(input.UserId);

            var tenantInfoMetadata = _invoiceService.GetTenantInfoMetadata(input.InfosNeedCreateInvoice.TenantInfo);

            var headerId = input.Id;
            var lstDetailId = await GetSEQsNextVal(input.InvoiceDetails.Count, SEQ_Name.SEQ_Invoice04Detail);

            StringBuilder rawSql = new StringBuilder();

            #region HEADER EXTRAS
            var headerExtraProperties = "";
            if (input.InvoiceHeaderExtras != null && input.InvoiceHeaderExtras.Count > 0)
            {
                var extraProperties = GetHeaderExtraProperties(input.InvoiceHeaderExtras.Select(x => new CommonHeaderExtraModel
                {
                    FieldName = x.FieldName,
                    FieldValue = x.FieldValue.Replace("'", "''")
                }).ToList(), input.InfosNeedCreateInvoice.HeaderFields);
                headerExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }

            #endregion


            #region HEADER SQL

            rawSql.Append($@"   BEGIN                                                                     
                                        INSERT INTO ""Invoice04Header"" (                                      
                                            ""Id"",                                                           
                                            ""TenantId"",                                                     
                                            ""CreationTime"",                                                 
                                            ""CreatorId"",                                                    
                                                                                                         
                                            ""Source"",                                                       
                                            ""BatchId"",                                                      
                                            ""ErpId"",                                                        
                                            ""CreatorErp"",                                                   
                                            ""TransactionId"",                                                
                                            ""InvoiceTemplateId"",                                            
                                            ""TemplateNo"",                                                   
                                            ""SerialNo"",                                                     
                                            ""Note"",                                                         
                                            ""InvoiceDate"",                                                  
                                            ""InvoiceStatus"",                                                
                                            ""SignStatus"",                                                   
                                            ""ApproveStatus"",
                                            ""ApproveCancelStatus"",
                                            ""ApproveDeleteStatus"",
                                                                                                         
                                            ""RegistrationHeaderId"",                                         
                                            ""RegistrationDetailId"",                                         
                                            ""Number"",                                                       
                                            ""InvoiceNo"",                                                    
                                                                                                        
                                            ""SellerId"",     
                                            ""SellerCode"", 
                                            ""SellerTaxCode"",                                                
                                            ""SellerAddressLine"",                                            
                                            ""SellerCountryCode"",                                            
                                            ""SellerDistrictName"",                                           
                                            ""SellerCityName"",                                               
                                            ""SellerPhoneNumber"",                                            
                                            ""SellerFaxNumber"",                                              
                                            ""SellerEmail"",                                                  
                                            ""SellerBankName"",                                               
                                            ""SellerBankAccount"",                                            
                                            ""SellerLegalName"",                                              
                                            ""SellerFullName"",                                               
                                                                                                        
                                            ""RoundingCurrency"",                                             
                                            ""FromCurrency"",                                                 
                                            ""CurrencyConversion"",                                           
                                            ""ToCurrency"",                                                   
                                            ""ExchangeRate"",                                                 
                                            ""PaymentDate"",                                                  
                                            ""PaymentAmountWords"",                                           
                                            ""PaymentAmountWordsEn"",                                         
                                            ""TotalAmount"",                                                  
                                            ""TotalPaymentAmount"",                                           
                                            ""FullNameCreator"",                                              
                                            ""UserNameCreator"",                                              
                                                                                                        
                                            ""BuyerId"",                                                      
                                            ""BuyerTaxCode"",                                                    
                                            ""BuyerName"",                                                 
                                            ""BuyerFullName"",                                                 
                                            ""BuyerAddressLine"",                                               
                                            ""BuyerEmail"",     

                                            ""EconomicContractNumber"",                                             
                                            ""EconomicContractDate"",                                             
                                            ""DeliveryBy"",                                             
                                            ""DeliveryOrderBy"",                                             
                                            ""ContractNumber"",                                             
                                            ""TransportationMethod"",                                   
                                                                                                             
                                            ""IsActive"",                                                     
                                            ""InvoiceDateYear"",                                              
                                            ""InvoiceDateQuater"",                                            
                                            ""InvoiceDateMonth"",                                             
                                            ""InvoiceDateWeek"",                                              
                                            ""InvoiceDateNumber"",                                            
                                            ""IsOpened"",                                                     
                                            ""IsViewed"",                                                     
                                            ""Partition"" ,
                                            ""ExtraProperties"",
                                            ""IsDeclared"",
                                            ""InvoiceDeleteSource"",
                                            ""StatusTvan""
                                        )                                                                       
                                        VALUES(                                                                 
                                            {headerId},                                                                                                                                                   
                                            '{ rawTenantId }',                                                                                                                                          
                                            '{ DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")) }',                                                                                               
                                            '{ rawUserId }',                                                                                                                                            
                                            { (short)input.Resource.GetHashCode() },                                                                                                                    
                                            '{ OracleExtension.ConvertGuidToRaw(Guid.NewGuid()) }',                                                                                                     
                                            '{ input.ErpId?.Replace("'", "''") }',                                                                                                                                              
                                            '{ input.CreatorErp?.Replace("'", "''") }',                                                                                                                                         
                                            '{ _invoiceService.GetTransactionId(input.Resource, input.TransactionId) }',                                                                                
                                            { input.InfosNeedCreateInvoice.Template.Id },                                                                                                               
                                            { input.TemplateNo },                                                                                                                                       
                                            '{ input.SerialNo }',                                                                                                                                       
                                            N'{ input.Note?.Replace("'", "''")}',                                                                                                                                          
                                            '{ input.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',                                                                                           
                                            {(short)InvoiceStatus.Goc.GetHashCode()},                                                                                                                   
                                            {(short)SignStatus.ChoKy.GetHashCode()},                                                                                                                    
                                            {(short)approveStatus.GetHashCode()},
                                            {(short)approveCancelStatus.GetHashCode()},
                                            {(short)approveDeleteStatus.GetHashCode()},
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            '{rawTenantId}',     
                                            '{input.InfosNeedCreateInvoice.TenantInfo.Code}',
                                            '{input.InfosNeedCreateInvoice.TenantInfo.TaxCode}',                                                                                                        
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.Address?.Replace("'", "''")}',                                                                                                          
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.Country?.Replace("'", "''")}',                                                                                                          
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.District?.Replace("'", "''")}',                                                                                                         
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.City?.Replace("'", "''")}',                                                                                                             
                                            '{input.InfosNeedCreateInvoice.TenantInfo.Phone}',                                         
                                            '{input.InfosNeedCreateInvoice.TenantInfo.Fax}',                                             
                                            '{input.InfosNeedCreateInvoice.TenantInfo.Emails?.Replace("'", "''")}',                                         
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.BankName?.Replace("'", "''")}',                                     
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.BankAccount}',                            
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.LegalName?.Replace("'", "''")}',                                   
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.FullNameVi?.Replace("'", "''")}',                                                                                                     
                                            { input.InfosNeedCreateInvoice.ToCurrency.Rounding },                                                                                                       
                                            '{ input.InfosNeedCreateInvoice.FromCurrency.CurrencyCode }',                                                                                               
                                            { input.InfosNeedCreateInvoice.ToCurrency.Conversion },                                                                                                     
                                            '{ input.InfosNeedCreateInvoice.ToCurrency.CurrencyCode }',                                                                                                 
                                            { input.ExchangeRate },                                                                                                                                     
                                            '{ input.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")) }',                                                                                          
                                            N'{ (await _invoiceService.ReadMoneyViAsync(input.TenantId, input.InfosNeedCreateInvoice.ToCurrency, input.TotalPaymentAmount, InvoiceStatus.Goc))?.Trim().Replace("'", "''") }',     
                                            N'{ (await _invoiceService.ReadMoneyEnAsync(input.TenantId, input.InfosNeedCreateInvoice.ToCurrency, input.TotalPaymentAmount, InvoiceStatus.Goc))?.Trim().Replace("'", "''") }',       
                                            { input.TotalAmount },                                                                                                                                      
                                            { input.TotalPaymentAmount },                                                                                                                               
                                            '{ input.UserFullName?.Replace("'", "''") }',                                                                                                                                   
                                            '{ input.UserName?.Replace("'", "''") }',                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            '{ input.BuyerTaxCode?.Trim() }',                                                                                                                                
                                            N'{ input.BuyerName?.Replace("'", "''") }',                                                                                                                                  
                                            N'{ input.BuyerFullName?.Replace("'", "''") }',                                                                                                                                 
                                            N'{ input.BuyerAddressLine?.Replace("'", "''") }',                                                                                                                                    
                                            N'{ input.BuyerEmail?.Replace("'", "''") }',                                                                                                                                  
                                            N'{ input.EconomicContractNumber?.Replace("'", "''") }',                                                                                                                                  
                                            '{ input.EconomicContractDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")) }',                                                                                                                                  
                                            N'{ input.DeliveryBy?.Replace("'", "''") }',                                                                                                                                  
                                            N'{ input.DeliveryOrderBy?.Replace("'", "''") }',                                                                                                                                  
                                            '{ input.ContractNumber?.Replace("'", "''") }',                                                                                                                                  
                                            N'{ input.TransportationMethod?.Replace("'", "''") }',                                                                                                                                  
                                            1,                                                                                                                                                          
                                            {input.InvoiceDate.Year},                                                                                                                                   
                                            {input.InvoiceDate.GetQuarter()},                                                                                                                           
                                            {input.InvoiceDate.Month},                                                                                                                                  
                                            {input.InvoiceDate.GetWeek()},                                                                                                                              
                                            {input.InvoiceDate.Day},                                                                                                                                    
                                            0,                                                                                                                                                          
                                            0,                                                                                                                                                          
                                            {long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))},
                                            N'{headerExtraProperties?.Replace("'", "''")}',
                                            0,
                                            0,
                                            0
                                        ); ");

            #endregion

            //StringBuilder sqlValueHeaderExtras = new StringBuilder();
            StringBuilder sqlValueDetails = new StringBuilder();

            #region DETAILS SQL

            var i = 0;
            foreach (var item in input.InvoiceDetails)
            {
                var detailId = lstDetailId[i];

                var unit = (input.InfosNeedCreateInvoice.Units != null && input.InfosNeedCreateInvoice.Units.ContainsKey(item.UnitName?.Trim()))
                        ? input.InfosNeedCreateInvoice.Units[item.UnitName?.Trim()]
                        : null;

                var productCode = item.ProductCode?.Trim();
                var product = string.IsNullOrEmpty(productCode)
                            ? null
                            : ((input.InfosNeedCreateInvoice.Products != null && input.InfosNeedCreateInvoice.Products.ContainsKey(productCode)) ? input.InfosNeedCreateInvoice.Products[productCode] : null);

                ProductTypeEntity productType = null;
                if (product != null && product.ProductTypeId.HasValue)
                    productType = (input.InfosNeedCreateInvoice.ProductTypes != null && input.InfosNeedCreateInvoice.ProductTypes.ContainsKey(product.ProductTypeId.Value))
                                ? input.InfosNeedCreateInvoice.ProductTypes[product.ProductTypeId.Value]
                                : null;

                #region DETAI EXTRAS SQL
                var detailExtraProperties = "";
                if (item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any())
                {
                    var extraProperties = GetDetailExtraProperties(detailId, item.InvoiceDetailExtras.Select(x => new CommonDetailExtraModel
                    {
                        FieldName = x.FieldName,
                        FieldValue = x.FieldValue?.Replace("'", "''"),
                    }).ToList(), input.InfosNeedCreateInvoice.DetailFields);
                    detailExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                }

                #endregion

                sqlValueDetails.Append($@"UNION ALL SELECT                                                                       
                                                        { detailId },                                              
                                                        { item.Index },                                                     
                                                        '{ rawTenantId }',                                                  
                                                        { headerId },                                                           
                                                        { item.Amount },                                                  
                                                        N'{ item.Note?.Replace("'", "''")}',                                                   
                                                        N'{ item.PaymentAmount }',                                          
                                                        { (product == null ? 0 : product.Id) },                             
                                                        N'{ item.ProductName?.Replace("'", "''")}',                                             
                                                        { item.ProductType },                                            
                                                        '{ (String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode) }',                                     
                                                        { item.Quantity },                                                  
                                                        { (unit == null ? 0 : unit.Id) },                                   
                                                        N'{ item.UnitName?.Trim()?.Replace("'", "''")}',                                         
                                                        { item.UnitPrice },                                                 
                                                        { (unit == null ? 4 : unit.Rounding) },                             
                                                        { long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm)) },                                                                   
                                                        N'{detailExtraProperties?.Replace("'", "''")}'                                                           
                                                    FROM DUAL ");

                i++;
            }

            #endregion

            if (sqlValueDetails.Length > 0)
            {
                sqlValueDetails = sqlValueDetails.Remove(0, 9);

                rawSql.Append($@"INSERT INTO ""Invoice04Detail"" (
                                              ""Id"",                                 
                                              ""Index"",                              
                                              ""TenantId"",                           
                                              ""InvoiceHeaderId"",                    
                                              ""Amount"",          
                                              ""Note"",                               
                                              ""PaymentAmount"",                      
                                              ""ProductId"",                          
                                              ""ProductName"",                        
                                              ""ProductType"",                        
                                              ""ProductCode"",                        
                                              ""Quantity"",                           
                                              ""UnitId"",                             
                                              ""UnitName"",                           
                                              ""UnitPrice"",                          
                                              ""RoundingUnit"",                      
                                              ""Partition"",                          
                                              ""ExtraProperties""                        
                                        ) {sqlValueDetails} ; ");
            }

            return rawSql.Append($" END; ").ToString();
        }

        public async Task<string> GenerateDrawUpdateInvoice(UpdateInvoice04HeaderEventSendData input)
        {
            StringBuilder rawSql = new StringBuilder($@"BEGIN ");

            #region HEADER EXTRAPROPERTIES
            var headerExtraProperties = "";
            if (input.InvoiceHeaderExtras != null && input.InvoiceHeaderExtras.Count > 0)
            {
                var extraProperties = GetHeaderExtraProperties(input.InvoiceHeaderExtras.Select(x => new CommonHeaderExtraModel
                {
                    FieldName = x.FieldName,
                    FieldValue = x.FieldValue.Replace("'", "''")
                }).ToList(), input.InfosNeedUpdateInvoice.HeaderFields);
                headerExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }
            #endregion

            #region HEADER SQL

            rawSql.Append($@"   UPDATE ""Invoice04Header""
                                SET 
                                    ""ToCurrency"" = '{input.InfosNeedUpdateInvoice.ToCurrency.CurrencyCode}',
                                    ""InvoiceDate"" = '{input.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                    ""Note"" = N'{input.Note?.Trim().Replace("'", "''") }',  
                                    ""RoundingCurrency"" = {input.InfosNeedUpdateInvoice.ToCurrency.Rounding},
                                    ""CurrencyConversion"" = {input.InfosNeedUpdateInvoice.ToCurrency.Conversion},
                                    ""ExchangeRate"" = {input.ExchangeRate},
                                    ""TotalAmount"" = {input.TotalAmount},
                                    ""TotalPaymentAmount"" = {input.TotalPaymentAmount},
                                    ""PaymentAmountWords"" = N'{(await _invoiceService.ReadMoneyViAsync(input.InfosNeedUpdateInvoice.Invoice.TenantId, input.InfosNeedUpdateInvoice.ToCurrency, input.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(input.InfosNeedUpdateInvoice.Invoice.InvoiceStatus))))?.Trim().Replace("'", "''") }',
                                    ""PaymentAmountWordsEn"" = N'{(await _invoiceService.ReadMoneyEnAsync(input.InfosNeedUpdateInvoice.Invoice.TenantId, input.InfosNeedUpdateInvoice.ToCurrency, input.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(input.InfosNeedUpdateInvoice.Invoice.InvoiceStatus))))?.Trim().Replace("'", "''") }',
                                    ""BuyerTaxCode"" = '{input.BuyerTaxCode?.Trim() }',
                                    ""BuyerEmail"" = '{input.BuyerEmail?.Replace("'", "''")}',
                                    ""BuyerFullName"" = N'{input.BuyerFullName?.Replace("'", "''") }',
                                    ""BuyerName"" = N'{input.BuyerName?.Replace("'", "''")}',
                                    ""BuyerAddressLine"" = N'{input.BuyerAddressLine.Replace("'", "''") }',
                                    ""EconomicContractDate"" = '{input.EconomicContractDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                    ""EconomicContractNumber"" = '{input.EconomicContractNumber?.Replace("'", "''")}',
                                    ""ContractNumber"" = '{input.ContractNumber?.Replace("'", "''")}',
                                    ""DeliveryBy"" = N'{input.DeliveryBy?.Replace("'", "''")}',
                                    ""DeliveryOrderBy"" = N'{input.DeliveryOrderBy?.Replace("'", "''")}',
                                    ""TransportationMethod"" = N'{input.TransportationMethod?.Replace("'", "''")}',
                                    ""CreatorErp"" = '{input.CreatorErp?.Replace("'", "''") }', 
                                    ""ExtraProperties"" = N'{headerExtraProperties?.Replace("'", "''")}'
                                WHERE ""Id"" = {input.Id}; 
                        ");

            #endregion

            var commandInvoiceDetails = new List<Invoice04DetailModel>();
            foreach (var item in input.InvoiceDetails)
            {
                var productCode = item.ProductCode?.Trim();
                var product = string.IsNullOrEmpty(productCode)
                            ? null
                            : ((input.InfosNeedUpdateInvoice.Products != null && input.InfosNeedUpdateInvoice.Products.ContainsKey(productCode)) ? input.InfosNeedUpdateInvoice.Products[productCode] : null);

                commandInvoiceDetails.Add(new Invoice04DetailModel
                {
                    TenantId = input.InfosNeedUpdateInvoice.Invoice.TenantId,
                    InvoiceHeaderId = input.Id,
                    Index = item.Index,
                    Amount = item.Amount,
                    Note = item.Note,
                    PaymentAmount = item.PaymentAmount,
                    ProductId = product != null ? product.Id : 0,
                    ProductCode = String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode,
                    ProductName = item.ProductName,
                    ProductType = item.ProductType,
                    Quantity = item.Quantity,
                    UnitId = 0,
                    UnitName = item.UnitName?.Trim(),
                    UnitPrice = item.UnitPrice,
                    InvoiceDetailExtras = item.InvoiceDetailExtras?.Select(y => new Invoice04DetailExtraModel
                    {
                        TenantId = input.InfosNeedUpdateInvoice.Invoice.TenantId,
                        InvoiceDetailFieldId = input.InfosNeedUpdateInvoice.DetailFields[y.FieldName].Id,
                        FieldValue = y.FieldValue,
                        FieldName = y.FieldName,
                    }).ToList()
                });
            }

            var rawTenantId = OracleExtension.ConvertGuidToRaw(input.TenantId);

            await GenerateUpdateInvoiceDetailAsync(rawSql, rawTenantId, input.InfosNeedUpdateInvoice.Details, input.InfosNeedUpdateInvoice.DetailFields, commandInvoiceDetails, input.InvoiceDate.Date);

            rawSql.Append($@" END; ");
            return rawSql.ToString();
        }

        public async Task<string> GenerateDrawCreateReplaceInvoice(CreateReplaceInvoice04HeaderEventSendData input)
        {
            var approveStatus = await _invoiceService.GetApproveStatusAsync(input.TenantId);
            var approveCancelStatus = await _invoiceService.GetApproveCancelStatusAsync(input.TenantId);
            var approveDeleteStatus = await _invoiceService.GetApproveDeleteStatusAsync(input.TenantId);
            var rawTenantId = OracleExtension.ConvertGuidToRaw(input.TenantId);
            var rawUserId = OracleExtension.ConvertGuidToRaw(input.UserId);
            var rawSellerId = OracleExtension.ConvertGuidToRaw(input.InfosNeedCreateInvoice.InvoiceReference.SellerId);

            var headerId = input.Id;
            var lstDetailId = await GetSEQsNextVal(input.InvoiceDetails.Count, SEQ_Name.SEQ_Invoice04Detail);

            StringBuilder rawSql = new StringBuilder();

            #region HEADER EXTRAPROPERTIES
            var headerExtraProperties = "";
            if (input.InvoiceHeaderExtras != null && input.InvoiceHeaderExtras.Count > 0)
            {
                var extraProperties = GetHeaderExtraProperties(input.InvoiceHeaderExtras.Select(x => new CommonHeaderExtraModel
                {
                    FieldName = x.FieldName,
                    FieldValue = x.FieldValue.Replace("'", "''"),
                }).ToList(), input.InfosNeedCreateInvoice.HeaderFields);
                headerExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }
            #endregion

            #region HEADER SQL

            rawSql.Append($@"   BEGIN                                                                     
                                        INSERT INTO ""Invoice04Header"" (                                      
                                            ""Id"",                                                           
                                            ""TenantId"",                                                     
                                            ""CreationTime"",                                                 
                                            ""CreatorId"",                                                    
                                                                                                         
                                            ""Source"",                                                       
                                            ""BatchId"",                                                      
                                            ""ErpId"",                                                        
                                            ""CreatorErp"",                                                   
                                            ""TransactionId"",                                                
                                            ""InvoiceTemplateId"",                                            
                                            ""TemplateNo"",                                                   
                                            ""SerialNo"",                                                     
                                            ""Note"",                                                         
                                            ""InvoiceDate"",                                                  
                                            ""InvoiceStatus"",                                                
                                            ""SignStatus"",                                                   
                                            ""ApproveStatus"",
                                            ""ApproveCancelStatus"",
                                            ""ApproveDeleteStatus"",                                                
                                                                                                         
                                            ""RegistrationHeaderId"",                                         
                                            ""RegistrationDetailId"",                                         
                                            ""Number"",                                                       
                                            ""InvoiceNo"",                                                    
                                                                                                        
                                            ""SellerId"",     
                                            ""SellerCode"",
                                            ""SellerTaxCode"",                                                
                                            ""SellerAddressLine"",                                            
                                            ""SellerCountryCode"",                                            
                                            ""SellerDistrictName"",                                           
                                            ""SellerCityName"",                                               
                                            ""SellerPhoneNumber"",                                            
                                            ""SellerFaxNumber"",                                              
                                            ""SellerEmail"",                                                  
                                            ""SellerBankName"",                                               
                                            ""SellerBankAccount"",                                            
                                            ""SellerLegalName"",                                              
                                            ""SellerFullName"",                                               
                                                                                                        
                                            ""RoundingCurrency"",                                             
                                            ""FromCurrency"",                                                 
                                            ""CurrencyConversion"",                                           
                                            ""ToCurrency"",                                                   
                                            ""ExchangeRate"",   
                                              
                                            ""PaymentDate"",                                                  
                                            ""PaymentAmountWords"",                                           
                                            ""PaymentAmountWordsEn"",                                         
                                            ""TotalAmount"",                                                  
                                            ""TotalPaymentAmount"",                                           
                                            ""FullNameCreator"",                                              
                                            ""UserNameCreator"",                                              
                                                                                                        
                                            ""BuyerId"",                                                      
                                            ""BuyerTaxCode"",                                                    
                                            ""BuyerName"",                                                 
                                            ""BuyerFullName"",                                                 
                                            ""BuyerAddressLine"",                                              
                                            ""BuyerEmail"",  

                                            ""EconomicContractNumber"",                                             
                                            ""EconomicContractDate"",                                             
                                            ""DeliveryBy"",                                             
                                            ""DeliveryOrderBy"",                                             
                                            ""ContractNumber"",                                             
                                            ""TransportationMethod"",  
                                                                                                               
                                            ""IsActive"",                                                     
                                            ""InvoiceDateYear"",                                              
                                            ""InvoiceDateQuater"",                                            
                                            ""InvoiceDateMonth"",                                             
                                            ""InvoiceDateWeek"",                                              
                                            ""InvoiceDateNumber"",                                            
                                            ""IsOpened"",                                                     
                                            ""IsViewed"",                                                     
                                            ""Partition"",                                                     
                                            ""ExtraProperties"",
                                            ""IsDeclared"",
                                            ""InvoiceDeleteSource"",
                                            ""StatusTvan""                                                     
                                        )                                                                       
                                        VALUES(                                                                 
                                            {headerId},                                                                                                                                                   
                                            '{ rawTenantId }',                                                                                                                                          
                                            '{ DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")) }',                                                                                               
                                            '{ rawUserId }',                                                                                                                                            
                                            { (short)input.Resource.GetHashCode() },                                                                                                                    
                                            '{ OracleExtension.ConvertGuidToRaw(Guid.NewGuid()) }',                                                                                                     
                                            '{ input.ErpId?.Replace("'", "''") }',                                                                                                                                          
                                            '{ input.CreatorErp?.Replace("'", "''") }',                                                                                                                                      
                                            '{ input.InfosNeedCreateInvoice.InvoiceReference.TransactionId }',                                                                                
                                            { input.InfosNeedCreateInvoice.Template.Id },                                                                                                               
                                            { input.TemplateNo },                                                                                                                                       
                                            '{ input.SerialNo }',                                                                                                                                       
                                            N'{ input.Note?.Replace("'", "''") }',                                                                                                                                            
                                            '{ input.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',                                                                                           
                                            {(short)InvoiceStatus.ThayThe.GetHashCode()},                                                                                                                   
                                            {(short)SignStatus.ChoKy.GetHashCode()},                                                                                                                    
                                            {(short)approveStatus.GetHashCode()},
                                            {(short)approveCancelStatus.GetHashCode()},
                                            {(short)approveDeleteStatus.GetHashCode()},                                                                                                    
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                             '{rawSellerId}',    
                                            '{input.InfosNeedCreateInvoice.TenantInfo.Code}',
                                            '{input.InfosNeedCreateInvoice.TenantInfo.TaxCode}',                                                                                                        
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.Address?.Replace("'", "''")}',                                                                                                       
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.Country?.Replace("'", "''")}',                                                                                                       
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.District?.Replace("'", "''")}',                                                                                                      
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.City?.Replace("'", "''")}',                                                                                                          
                                            '{input.InfosNeedCreateInvoice.TenantInfo.Phone}',                                         
                                            '{input.InfosNeedCreateInvoice.TenantInfo.Fax}',                                             
                                            '{input.InfosNeedCreateInvoice.TenantInfo.Emails}',                                         
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.BankName?.Replace("'", "''")}',                                  
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.BankAccount}',                            
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.LegalName?.Replace("'", "''")}',                                
                                            N'{input.InfosNeedCreateInvoice.TenantInfo.FullNameVi?.Replace("'", "''") }',                                                                                                  
                                            { input.InfosNeedCreateInvoice.ToCurrency.Rounding },                                                                                                       
                                            '{ input.InfosNeedCreateInvoice.FromCurrency.CurrencyCode }',                                                                                               
                                            { input.InfosNeedCreateInvoice.ToCurrency.Conversion },                                                                                                     
                                            '{ input.InfosNeedCreateInvoice.ToCurrency.CurrencyCode }',                                                                                                 
                                            { input.ExchangeRate },                                                                                                                                     
                                            '{ input.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")) }',                                                                                          
                                            N'{ (await _invoiceService.ReadMoneyViAsync(input.TenantId, input.InfosNeedCreateInvoice.ToCurrency, input.TotalPaymentAmount, InvoiceStatus.Goc))?.Trim().Replace("'", "''") }',       
                                            N'{ (await _invoiceService.ReadMoneyEnAsync(input.TenantId, input.InfosNeedCreateInvoice.ToCurrency, input.TotalPaymentAmount, InvoiceStatus.Goc))?.Trim().Replace("'", "''") }',        
                                            { input.TotalAmount },                                                                                                                                      
                                            { input.TotalPaymentAmount },                                                                                                                               
                                            '{ input.UserFullName?.Replace("'", "''") }',                                                                                                                                   
                                            '{ input.UserName }',                                                                                                                                       
                                            NULL,                                                                                                                                                       
                                            '{ input.BuyerTaxCode?.Trim() }',                                                                                                                              
                                            '{ input.BuyerName }',                                                                                                                                  
                                            '{ input.BuyerFullName?.Replace("'", "''") }',                                                                                                                                    
                                            '{ input.BuyerAddressLine?.Replace("'", "''") }',                                                                                                                                   
                                            '{ input.BuyerEmail }',                                                                                                                                  
                                            N'{ input.EconomicContractNumber }',                                                                                                                                  
                                            '{ input.EconomicContractDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")) }',                                                                                                                                  
                                            N'{ input.DeliveryBy }',                                                                                                                                  
                                            N'{ input.DeliveryOrderBy }',                                                                                                                                  
                                            '{ input.ContractNumber }',                                                                                                                                  
                                            N'{ input.TransportationMethod }',                                                                                                                                        
                                            1,                                                                                                                                                          
                                            {input.InvoiceDate.Year},                                                                                                                                   
                                            {input.InvoiceDate.GetQuarter()},                                                                                                                           
                                            {input.InvoiceDate.Month},                                                                                                                                  
                                            {input.InvoiceDate.GetWeek()},                                                                                                                              
                                            {input.InvoiceDate.Day},                                                                                                                                    
                                            0,                                                                                                                                                          
                                            0,                                                                                                                                                          
                                            {long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))},                                                                                                   
                                            N'{headerExtraProperties}',
                                            0,
                                            0,
                                            0                                                                                                   
                                        ); ");

            #endregion

            //StringBuilder sqlValueHeaderExtras = new StringBuilder();
            StringBuilder sqlValueDetails = new StringBuilder();
            //StringBuilder sqlValueDetailExtras = new StringBuilder();

            #region DETAILS SQL

            var i = 0;
            foreach (var item in input.InvoiceDetails)
            {
                var detailId = lstDetailId[i];

                var unit = (input.InfosNeedCreateInvoice.Units != null && input.InfosNeedCreateInvoice.Units.ContainsKey(item.UnitName?.Trim()))
                        ? input.InfosNeedCreateInvoice.Units[item.UnitName?.Trim()]
                        : null;

                var productCode = item.ProductCode?.Trim();
                var product = string.IsNullOrEmpty(productCode)
                            ? null
                            : ((input.InfosNeedCreateInvoice.Products != null && input.InfosNeedCreateInvoice.Products.ContainsKey(productCode)) ? input.InfosNeedCreateInvoice.Products[productCode] : null);

                ProductTypeEntity productType = null;
                if (product != null && product.ProductTypeId.HasValue)
                    productType = (input.InfosNeedCreateInvoice.ProductTypes != null && input.InfosNeedCreateInvoice.ProductTypes.ContainsKey(product.ProductTypeId.Value))
                                ? input.InfosNeedCreateInvoice.ProductTypes[product.ProductTypeId.Value]
                                : null;

                #region DETAIL EXTRAPROPERTIES
                var detailExtraProperties = "";
                if (item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any())
                {
                    var extraProperties = GetDetailExtraProperties(detailId, item.InvoiceDetailExtras.Select(x => new CommonDetailExtraModel
                    {
                        FieldValue = x.FieldValue?.Replace("'", "''"),
                        FieldName = x.FieldName,
                    }).ToList(), input.InfosNeedCreateInvoice.DetailFields);
                    detailExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                }
                #endregion


                sqlValueDetails.Append($@"UNION ALL SELECT   
                                                        { detailId },                                              
                                                        { item.Index },                                                     
                                                        '{ rawTenantId }',                                                  
                                                        { headerId },                                                           
                                                        { item.Amount },                                                  
                                                        N'{ item.Note?.Replace("'", "''") }',                                                   
                                                        N'{ item.PaymentAmount }',                                          
                                                        { (product == null ? 0 : product.Id) },                             
                                                        N'{ item.ProductName?.Replace("'", "''") }',                                           
                                                        { item.ProductType },                                            
                                                        '{ (String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode) }',                                      
                                                        { item.Quantity },                                                  
                                                        { (unit == null ? 0 : unit.Id) },                                   
                                                        N'{ item.UnitName?.Trim()?.Replace("'", "''") }',                                       
                                                        { item.UnitPrice },                                                 
                                                        { (unit == null ? 4 : unit.Rounding) },                          
                                                        { long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm)) },
                                                        N'{detailExtraProperties}'
                                                    FROM DUAL ");

                i++;
            }

            #endregion

            if (sqlValueDetails.Length > 0)
            {
                sqlValueDetails = sqlValueDetails.Remove(0, 9);

                rawSql.Append($@"INSERT INTO ""Invoice04Detail"" (
                                        ""Id"",                                 
                                        ""Index"",                              
                                        ""TenantId"",                           
                                        ""InvoiceHeaderId"",                    
                                        ""Amount"",                           
                                        ""Note"",                               
                                        ""PaymentAmount"",                      
                                        ""ProductId"",                          
                                        ""ProductName"",                        
                                        ""ProductType"",                        
                                        ""ProductCode"",                        
                                        ""Quantity"",                           
                                        ""UnitId"",                             
                                        ""UnitName"",                           
                                        ""UnitPrice"",                          
                                        ""RoundingUnit"",                 
                                        ""Partition"",                           
                                        ""ExtraProperties""                         
                                ) {sqlValueDetails} ; ");
            }


            #region INVOICE REFERENCE SQL

            rawSql.Append($@"  INSERT INTO ""Invoice04Reference"" (             
                                                    ""CreationTime"",                           
                                                    ""InvoiceDateReference"",                                  
                                                    ""InvoiceHeaderId"",                                      
                                                    ""InvoiceNoReference"",                                 
                                                    ""InvoiceReferenceId"",                           
                                                    ""NumberReference"",                                
                                                    ""SerialNoReference"",                         
                                                    ""TemplateNoReference"",
                                                    ""TenantId"",
                                                    ""InvoiceStatus"",
                                                    ""Note"",
                                                    ""Partition""
                                                )                                                  
                                                VALUES (
                                                    '{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                                    '{input.InfosNeedCreateInvoice.InvoiceReference.InvoiceDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                                    {input.Id},
                                                    '{input.InfosNeedCreateInvoice.InvoiceReference.InvoiceNo}',
                                                    {input.InfosNeedCreateInvoice.InvoiceReference.Id},
                                                    {input.InfosNeedCreateInvoice.InvoiceReference.Number ?? 0},
                                                    '{input.InfosNeedCreateInvoice.InvoiceReference.SerialNo}',
                                                    {input.InfosNeedCreateInvoice.InvoiceReference.TemplateNo},
                                                    '{rawTenantId}',
                                                    {(short)InvoiceStatus.ThayThe.GetHashCode()},
                                                    N'{input.Content?.Replace("'", "''")}',
                                                    {long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))}
                                                ); ");


            #endregion


            if (input.IdFileDocument.HasValue)
            {
                #region INVOICE DOCUMENTINFO SQL

                rawSql.Append($@"  INSERT INTO ""Invoice04DocumentInfo"" (             
                                                    ""TenantId"",
                                                    ""Partition"",
                                                    ""InvoiceHeaderId"",
                                                    ""FileId"",
                                                    ""DocumentNo"",
                                                    ""DocumentDate"",
                                                    ""DocumentReason"",
                                                    ""Type"",
                                                    ""IsUploadFile""
                                                )                                                  
                                                VALUES (
                                                    '{rawTenantId}',
                                                    {long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))},
                                                    {headerId},
                                                    {input.IdFileDocument.Value},
                                                    N'{input.DocumentNo?.Replace("'", "''")}',
                                                    '{input.DocumentDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                                    N'{input.DocumentReason?.Replace("'", "''")}',
                                                    {(short)DocumentTemplateType.Replace.GetHashCode()},
                                                    {(input.IsUploadFile ? 1 : 0)}
                                                ); ");
                #endregion


                #region INVOICE DOCUMENT SQL

                rawSql.Append($@"  UPDATE ""Invoice04Document"" SET ""InvoiceHeaderId"" = {headerId} WHERE ""Id"" = {input.IdFileDocument.Value}; ");

                #endregion
            }



            return rawSql.Append($" END; ").ToString();
        }

        public async Task<string> GenerateDrawUpdateReplaceInvoice(UpdateReplaceInvoice04HeaderEventSendData input)
        {
            StringBuilder rawSql = new StringBuilder($@"BEGIN ");

            #region HEADER EXTRAPROPERTIES
            var headerExtraProperties = "";
            if (input.InvoiceHeaderExtras != null && input.InvoiceHeaderExtras.Count > 0)
            {
                var extraProperties = GetHeaderExtraProperties(input.InvoiceHeaderExtras.Select(x => new CommonHeaderExtraModel
                {
                    FieldName = x.FieldName,
                    FieldValue = x.FieldValue.Replace("'", "''")
                }).ToList(), input.InfosNeedUpdateInvoice.HeaderFields);
                headerExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            }
            #endregion

            #region HEADER SQL

            rawSql.Append($@"   UPDATE ""Invoice04Header""
                                SET 
                                    ""ToCurrency"" = '{input.InfosNeedUpdateInvoice.ToCurrency.CurrencyCode}',
                                    ""InvoiceDate"" = '{input.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                    ""Note"" = N'{input.Note?.Trim().Replace("'", "''") }', 
                                    ""RoundingCurrency"" = {input.InfosNeedUpdateInvoice.ToCurrency.Rounding},
                                    ""CurrencyConversion"" = {input.InfosNeedUpdateInvoice.ToCurrency.Conversion},
                                    ""ExchangeRate"" = {input.ExchangeRate},
                                    ""TotalAmount"" = {input.TotalAmount},
                                    ""TotalPaymentAmount"" = {input.TotalPaymentAmount},
                                    ""PaymentAmountWords"" = N'{(await _invoiceService.ReadMoneyViAsync(input.InfosNeedUpdateInvoice.Invoice.TenantId, input.InfosNeedUpdateInvoice.ToCurrency, input.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(input.InfosNeedUpdateInvoice.Invoice.InvoiceStatus))))?.Trim().Replace("'", "''")}',
                                    ""PaymentAmountWordsEn"" = '{(await _invoiceService.ReadMoneyEnAsync(input.InfosNeedUpdateInvoice.Invoice.TenantId, input.InfosNeedUpdateInvoice.ToCurrency, input.TotalPaymentAmount, (EnumExtension.ToEnum<InvoiceStatus>(input.InfosNeedUpdateInvoice.Invoice.InvoiceStatus))))?.Trim().Replace("'", "''")}',
                                    ""BuyerTaxCode"" = '{input.BuyerTaxCode}',
                                    ""BuyerEmail"" = '{input.BuyerEmail}',
                                    ""BuyerFullName"" = N'{input.BuyerFullName.Replace("'", "''") }',  
                                    ""BuyerName"" = N'{input.BuyerName}',
                                    ""BuyerAddressLine"" = N'{input.BuyerAddressLine.Replace("'", "''") }',  
                                    ""EconomicContractDate"" = '{input.EconomicContractDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                    ""EconomicContractNumber"" = '{input.EconomicContractNumber}',
                                    ""ContractNumber"" = '{input.ContractNumber}',
                                    ""DeliveryBy"" = N'{input.DeliveryBy}',
                                    ""DeliveryOrderBy"" = N'{input.DeliveryOrderBy}',
                                    ""TransportationMethod"" = N'{input.TransportationMethod}',
                                    ""CreatorErp"" = '{input.CreatorErp?.Replace("'", "''")}',
                                    ""ExtraProperties"" = N'{headerExtraProperties}'
                                WHERE ""Id"" = {input.Id}; 
                        ");

            #endregion

            var commandInvoiceDetails = new List<Invoice04DetailModel>();
            foreach (var item in input.InvoiceDetails)
            {
                var productCode = item.ProductCode?.Trim();
                var product = string.IsNullOrEmpty(productCode)
                            ? null
                            : ((input.InfosNeedUpdateInvoice.Products != null && input.InfosNeedUpdateInvoice.Products.ContainsKey(productCode)) ? input.InfosNeedUpdateInvoice.Products[productCode] : null);

                commandInvoiceDetails.Add(new Invoice04DetailModel
                {
                    Amount = item.Amount,
                    Index = item.Index,
                    InvoiceHeaderId = input.Id,
                    Note = item.Note,
                    PaymentAmount = item.PaymentAmount,
                    ProductId = product != null ? product.Id : 0,
                    ProductCode = String.IsNullOrEmpty(productCode) ? StringExtension.RandomByDateTime() : productCode,
                    ProductName = item.ProductName,
                    ProductType = item.ProductType,
                    Quantity = item.Quantity,
                    TenantId = input.InfosNeedUpdateInvoice.Invoice.TenantId,
                    UnitId = 0,
                    UnitName = item.UnitName?.Trim(),
                    UnitPrice = item.UnitPrice,
                    InvoiceDetailExtras = item.InvoiceDetailExtras?.Select(y => new Invoice04DetailExtraModel
                    {
                        FieldValue = y.FieldValue?.Replace("'", "''"),
                        InvoiceDetailFieldId = input.InfosNeedUpdateInvoice.DetailFields[y.FieldName].Id,
                        TenantId = input.InfosNeedUpdateInvoice.Invoice.TenantId,
                    }).ToList()
                });
            }

            var rawTenantId = OracleExtension.ConvertGuidToRaw(input.TenantId);

            await GenerateUpdateInvoiceDetailAsync(rawSql, rawTenantId, input.InfosNeedUpdateInvoice.Details, input.InfosNeedUpdateInvoice.DetailFields, commandInvoiceDetails, input.InvoiceDate.Date);
            //await GenerateUpdateInvoiceHeaderExtraAsync(rawSql, rawTenantId, input.InfosNeedUpdateInvoice.HeaderExtras, commandHeaderExtras, input.InvoiceDate.Date);

            //update documentInfo
            //TODO: làm có thể vừa insert vừa update trường hợp mà chưa có dữ liệu trong bảng InvoiceDocumentInfo
            if (input.IdFileDocument.HasValue)
            {
                #region INVOICE DOCUMENTINFO SQL

                rawSql.Append($@"  UPDATE ""Invoice04DocumentInfo""
                               SET             
                                ""Partition"" = {long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))},
                                ""FileId"" = {input.IdFileDocument.Value},
                                ""DocumentNo"" = N'{input.DocumentNo?.Replace("'", "''")}',
                                ""DocumentDate"" = '{input.DocumentDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                ""DocumentReason"" = N'{input.DocumentReason?.Replace("'", "''")}',
                                ""IsUploadFile"" = {(input.IsUploadFile ? 1 : 0)}
                               WHERE ""InvoiceHeaderId"" = {input.Id};   ");
                #endregion
            }


            rawSql.Append($@" END; ");
            return rawSql.ToString();
        }

        private async Task GenerateUpdateInvoiceDetailAsync(StringBuilder rawSql, string rawTenantId, IEnumerable<Invoice04DetailEntity> entityDetails, Dictionary<string, Invoice04DetailFieldEntity> entityDetailFields, List<Invoice04DetailModel> modelDetails, DateTime invoiceDate)
        {
            var removeDetails = new List<Invoice04DetailEntity>();

            //group để bỏ trường hợp Index thêm mới
            var duplicate = modelDetails.ToDictionary(x => x.Index, x => x)
                                        .Select(x => x.Key)
                                        .Intersect(entityDetails.Select(x => x.Index));
            var newDetails = modelDetails.Where(x => !duplicate.Contains(x.Index));

            foreach (var item in entityDetails)
            {
                if (duplicate.Contains(item.Index)) // SỬA
                {
                    var detailExtraProperties = "";
                    //lấy dữ liệu hóa đơn ở api để cho vào detail này
                    var detail = modelDetails.FirstOrDefault(x => x.Index == item.Index);

                    if (detail.InvoiceDetailExtras != null && detail.InvoiceDetailExtras.Any())
                    {
                        var extraProperties = GetDetailExtraProperties(item.Id, detail.InvoiceDetailExtras.Select(x => new CommonDetailExtraModel
                        {
                            FieldValue = x.FieldValue.Replace("'", "''"),
                            FieldName = x.FieldName,
                        }).ToList(), entityDetailFields);
                        detailExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                    }

                    rawSql.Append($@"   UPDATE ""Invoice04Detail""
                                        SET
                                            ""Amount"" = {detail.Amount},
                                            ""Index"" = {detail.Index},
                                            ""Note"" = N'{detail.Note?.Replace("'", "''")}',
                                            ""PaymentAmount"" = {detail.PaymentAmount},
                                            ""ProductCode"" = '{detail.ProductCode?.Trim()}',
                                            ""ProductName"" = N'{detail.ProductName.Replace("'", "''")}',
                                            ""ProductType"" = {detail.ProductType},
                                            ""Quantity"" = {detail.Quantity},
                                            ""UnitName"" = N'{detail.UnitName?.Trim().Replace("'", "''")}',
                                            ""UnitPrice"" = {detail.UnitPrice},
                                            ""ExtraProperties"" = N'{detailExtraProperties?.Replace("'", "''")}'
                                        WHERE ""Id"" = {item.Id};
                                    ");

                    //var entityDetailExtra = detailExtras.ContainsKey(item.Id) ? detailExtras[item.Id] : null;
                    //await UpdateInvoiceDetailExtrasAsync(detail.TenantId, item.Id, detail.InvoiceDetailExtras, entityDetailExtra);
                }
                else
                {
                    //k phải thì bỏ qua, check tiếp vì bị xóa
                    removeDetails.Add(item);
                    //if (detailExtras.ContainsKey(item.Id))
                    //    removeDetailExtras.AddRange(detailExtras[item.Id]);
                }
            }

            // THÊM MỚI
            if (newDetails != null && newDetails.Any())
            {
                var lstDetailId = await GetSEQsNextVal(newDetails.Count(), SEQ_Name.SEQ_Invoice04Detail);

                var i = 0;
                foreach (var item in newDetails)
                {
                    var detailId = lstDetailId[i];

                    var detailExtraProperties = "";
                    if (item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any())
                    {
                        var extraProperties = GetDetailExtraProperties(detailId, item.InvoiceDetailExtras.Select(x => new CommonDetailExtraModel
                        {
                            FieldValue = x.FieldValue.Replace("'", "''"),
                            FieldName = x.FieldName,
                        }).ToList(), entityDetailFields);
                        detailExtraProperties = JsonConvert.SerializeObject(extraProperties, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                    }

                    rawSql.Append($@"  INSERT INTO ""Invoice04Detail"" 
                                        (
                                            ""Id"",
                                            ""Amount"",
                                            ""Note"",
                                            ""ProductId"",
                                            ""ProductCode"",
                                            ""ProductName"",
                                            ""ProductType"",
                                            ""UnitName"",
                                            ""UnitPrice"",
                                            ""Index"",
                                            ""Quantity"",
                                            ""PaymentAmount"",
                                            ""InvoiceHeaderId"",
                                            ""TenantId"",
                                            ""UnitId"",
                                            ""RoundingUnit"",
                                            ""Partition"",
                                            ""ExtraProperties""
                                        ) VALUES (
                                            {detailId},
                                            {item.Amount},
                                            N'{item.Note?.Trim().Replace("'", "''")}',
                                            {item.ProductId},
                                            '{item.ProductCode?.Trim()?.Replace("'", "''")}',
                                            N'{item.ProductName?.Trim()?.Replace("'", "''")}',
                                            {item.ProductType},
                                            N'{item.UnitName?.Trim()?.Replace("'", "''")}',
                                            {item.UnitPrice},
                                            {item.Index},
                                            {item.Quantity},
                                            {item.PaymentAmount},
                                            {item.InvoiceHeaderId},
                                            '{rawTenantId}', 
                                            0,
                                            4,
                                            { long.Parse(invoiceDate.ToString(FormatDate._yyyyMMddHHmm)) },
                                            {(string.IsNullOrEmpty(detailExtraProperties) ? "null" : $"N'{detailExtraProperties?.Replace("'", "''")}'")}
                                        ); ");

                    i++;
                }
            }

            // XÓA DETAIL VÀ DETAIL EXTRA (NẾU CÓ)
            if (removeDetails != null && removeDetails.Any())
            {
                rawSql.Append($@"   DELETE FROM ""Invoice04Detail"" WHERE ""Id"" IN ( { String.Join(",", removeDetails.Select(x => x.Id)) } ); ");
            }
        }

        private Dictionary<string, string> GetHeaderExtraProperties(List<CommonHeaderExtraModel> invoiceHeaderExtras, Dictionary<string, Invoice04HeaderFieldEntity> entityHeaderFields)
        {
            var headerExtraProperties = new Dictionary<string, string>();

            var headerExtras = new List<InvoiceHeaderExtraModel>();
            foreach (var item in invoiceHeaderExtras)
            {
                //Nếu ko có HeaderExtra thì bỏ qua, ko insert
                if (!entityHeaderFields.ContainsKey(item.FieldName))
                    continue;

                var field = entityHeaderFields[item.FieldName];
                headerExtras.Add(new InvoiceHeaderExtraModel
                {
                    FieldName = item.FieldName,
                    FieldValue = item.FieldValue,
                    InvoiceHeaderFieldId = field.Id
                });

            }
            headerExtraProperties.Add("invoiceHeaderExtras", JsonConvert.SerializeObject(headerExtras));

            return headerExtraProperties;
        }

        private Dictionary<string, string> GetDetailExtraProperties(long detailId, List<CommonDetailExtraModel> invoiceDetailExtras, Dictionary<string, Invoice04DetailFieldEntity> entityDetailFields)
        {
            var detailExtraProperties = new Dictionary<string, string>();

            var detailExtras = new List<InvoiceDetailExtraModel>();
            foreach (var extra in invoiceDetailExtras)
            {
                //Nếu ko có HeaderExtra thì bỏ qua, ko insert
                if (!entityDetailFields.ContainsKey(extra.FieldName))
                    continue;

                var field = entityDetailFields[extra.FieldName];
                detailExtras.Add(new InvoiceDetailExtraModel
                {
                    InvoiceDetailId = detailId,
                    InvoiceDetailFieldId = field.Id,
                    FieldValue = extra.FieldValue,
                    FieldName = field.FieldName,
                });
            }
            detailExtraProperties.Add("invoiceDetailExtras", JsonConvert.SerializeObject(detailExtras));

            return detailExtraProperties;
        }
    }
}
