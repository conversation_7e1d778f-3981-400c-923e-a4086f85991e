using Core;
using Core.AspNetCore.Mvc;
using Core.Data;
using Core.DependencyInjection;
using Core.EventBus.RabbitMq;
using Core.Host.Shared;
using Core.Modularity;
using Core.Swashbuckle;
using Core.Threading;
using Core.VaultSharp;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.OpenApi.Models;
using System.Collections.Generic;
using VnisCore.PITLetter.Application;

namespace VnisCore.PILetter.Host
{
    [DependsOn(
        typeof(VaultSharpModule),
        typeof(HostSharedModule),
        typeof(VnisCorePITLetterApplicationModule),
        typeof(AbpEventBusRabbitMqModule),
        typeof(AbpSwashbuckleModule)
    )]
    public class VnisCorePITLetterHostModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {

            var configuration = context.Services.GetConfiguration();

            Configure<AbpAspNetCoreMvcOptions>(options =>
            {
                options.ConventionalControllers.Create(typeof(VnisCorePITLetterApplicationModule).Assembly, option =>
                {
                    option.RootPath = configuration["Service:BaseUrl"];
                    option.RemoteServiceName = configuration["Service:Name"];
                });
            });

            ConfigureSwaggerServices(context, configuration);

            //Configure<AbpAuditingOptions>(options =>
            //{
            //    options.IsEnabledForGetRequests = true;
            //    options.ApplicationName = "License.Api";
            //    options.EntityHistorySelectors.AddAllEntities();
            //});
        }

        private static void ConfigureSwaggerServices(ServiceConfigurationContext context, IConfiguration configuration)
        {
            context.Services.AddAbpSwaggerGenWithOAuth(
                configuration["AuthServer:Authority"],
                new Dictionary<string, string>
                {
                    {"einvoice", "PITLetter Api"}
                },
                options =>
                {
                    options.SwaggerDoc("v1", new OpenApiInfo { Title = "PITLetter Api", Version = "v1" });
                    options.DocInclusionPredicate((docName, description) => true);
                    options.CustomSchemaIds(type => type.FullName);
                });
        }

        public override void OnApplicationInitialization(ApplicationInitializationContext context)
        {
            var app = context.GetApplicationBuilder();

            app.UseSwagger();
            app.UseSwaggerUI(options =>
            {
                options.SwaggerEndpoint("/swagger/v1/swagger.json", "PITLetter Api");
                var configuration = context.GetConfiguration();
                options.OAuthClientId(configuration["AuthServer:SwaggerClientId"]);
                options.OAuthClientSecret(configuration["AuthServer:SwaggerClientSecret"]);
                options.OAuthScopes("einvoice");
            });

            SeedData(context);
        }

        private static void SeedData(IServiceProviderAccessor context)
        {
            AsyncHelper.RunSync(async () =>
            {
                using var scope = context.ServiceProvider.CreateScope();
                await scope.ServiceProvider
                    .GetRequiredService<IDataSeeder>()
                    .SeedAsync();
            });
        }
    }
}