using Core;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Dto;
using Core.Shared.Extensions;
using Core.Shared.FileManager.Interfaces;

using MediatR;

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;

using MongoDB.Driver;
using Serilog;
using System;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using VnisCore.Core.MongoDB.IRepository;
using VnisCore.Portal.Application.Interfaces;
using VnisCore.Portal.Application.Models.Events;
using VnisCore.Portal.Application.Models.Requests;

namespace VnisCore.Portal.Application.Handlers.Queries
{
    public class DownloadXmlsPortalQueryHandler : IRequestHandler<DownloadXmlsPortalRequestModel, FileDto>
    {
        private readonly IDownloadXmlsService _invoiceMediaFactory;
        private readonly IFileService _fileService;
        private readonly IPortalService _portalService;
        private readonly IMediator _mediator;
        private readonly ILogger<DownloadXmlsPortalQueryHandler> _logger;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IServiceProvider _serviceProvider;
        private readonly IVnisCoreMongoXmlInvoice01SignedRepository _mongoXmlInvoice01SignedRepository;


        public DownloadXmlsPortalQueryHandler(IDownloadXmlsService invoiceMediaFactory,
            IFileService fileService,
            IPortalService portalService,
            IMediator mediator,
            ILogger<DownloadXmlsPortalQueryHandler> logger,
            IStringLocalizer<CoreLocalizationResource> localizer,
            IServiceProvider serviceProvider,
            IVnisCoreMongoXmlInvoice01SignedRepository mongoXmlInvoice01SignedRepository)
        {
            _invoiceMediaFactory = invoiceMediaFactory;
            _fileService = fileService;
            _portalService = portalService;
            _mediator = mediator;
            _logger = logger;
            _localizer = localizer;
            _serviceProvider = serviceProvider;
            _mongoXmlInvoice01SignedRepository = mongoXmlInvoice01SignedRepository;
        }

        public async Task<FileDto> Handle(DownloadXmlsPortalRequestModel request, CancellationToken cancellationToken)
        {
            var guidId = Guid.NewGuid().ToString();
            var mediaType = GetMediaType(request.InvoiceType);
            var mediaTypeHasCode = GetMediaTypeHasCode(request.InvoiceType);

            var factory = _serviceProvider.GetService<IDownloadXmlsFactory>();
            //lấy service
            var service = factory.GetService(request.InvoiceType.GetHashCode());
            //lấy ds hóa đơn
            var xml = await service.DownloadXmls(request.Invoices);
            var xmlModels = await service.ExportXmlsHasVerificationCode(request.Invoices);

            var configuration = _serviceProvider.GetService<IConfiguration>();
            var pathLocal = Path.Combine(configuration.GetSection("FileDownload:PathFolder").Value.Trim(), guidId);
            var fileName = $"{guidId}";
            byte[] bytes = null;

            if (xml.Count > 1)
            {
                //tạo thư mục
                if (!Directory.Exists(pathLocal))
                    Directory.CreateDirectory(pathLocal);

                foreach (var xmlModel in xmlModels)
                {
                    var filePagingLocalPath = string.Empty;
                    try
                    {
                        var pathFileMinio = $"{mediaTypeHasCode}/{xmlModel.TenantId}/{xmlModel.CreationTime.Year}/{xmlModel.CreationTime.Month:00}/{xmlModel.CreationTime.Day:00}/{xmlModel.CreationTime.Hour:00}/{xmlModel.PhysicalFileName}";
                        bytes = await _fileService.DownloadAsync(pathFileMinio);
                        filePagingLocalPath = Path.Combine(pathLocal, Path.GetFileName(xmlModel.FileName));
                        await File.WriteAllBytesAsync(filePagingLocalPath, bytes);
                        await _mediator.Publish(new AfterDownloadXmlEventModel
                        {
                            Id = xmlModel.InvoiceHeaderId,
                            Type = request.InvoiceType,
                            ViewAt = DateTime.Now,
                            ViewAtUtc = DateTime.UtcNow
                        }, cancellationToken);

                        xml.Remove(xml.FirstOrDefault(r => r.InvoiceHeaderId == xmlModel.InvoiceHeaderId));
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, ex.Message);
                        throw new UserFriendlyException(ex.Message);
                    }
                }

                foreach (var xmlModel in xml)
                {
                    var filePagingLocalPath = string.Empty;
                    try
                    {
                        //download file từ minio về
                        var pathFileMinio = $"{mediaType}/{xmlModel.TenantId}/{xmlModel.CreationTime.Year}/{xmlModel.CreationTime.Month:00}/{xmlModel.CreationTime.Day:00}/{xmlModel.CreationTime.Hour:00}/{xmlModel.PhysicalFileName}";
                        bytes = await _fileService.DownloadAsync(pathFileMinio);

                        filePagingLocalPath = Path.Combine(pathLocal, Path.GetFileName(xmlModel.FileName));
                        await File.WriteAllBytesAsync(filePagingLocalPath, bytes);
                        await _mediator.Publish(new AfterDownloadXmlEventModel
                        {
                            Id = xmlModel.InvoiceHeaderId,
                            Type = request.InvoiceType,
                            ViewAt = DateTime.Now,
                            ViewAtUtc = DateTime.UtcNow
                        }, cancellationToken);

                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, ex.Message);
                        throw new UserFriendlyException(ex.Message);
                    }
                }

                byte[] summaryBytes;

                //zip lại
                string zipPath = pathLocal + ".zip";
                ZipFile.CreateFromDirectory(pathLocal, zipPath);
                summaryBytes = await File.ReadAllBytesAsync(zipPath);
                fileName = $"{guidId}.zip";
                //xóa folder
                if (Directory.Exists(pathLocal))
                    Directory.Delete(pathLocal, true);

                return new FileDto
                {
                    ContentType = ContentType.Zip,
                    FileBytes = summaryBytes,
                    FileName = fileName,
                    FileBase64 = Convert.ToBase64String(summaryBytes, 0, summaryBytes.Length)
                };
            }
            else
            {
                var invoice = xml.FirstOrDefault();
                var invoiceHasCode = xmlModels.FirstOrDefault();


                if (invoiceHasCode != null)
                {
                    try
                    {
                        //download file từ minio về
                        var pathFileMinio = $"{mediaTypeHasCode}/{invoiceHasCode.TenantId}/{invoiceHasCode.CreationTime.Year}/{invoiceHasCode.CreationTime.Month:00}/{invoiceHasCode.CreationTime.Day:00}/{invoiceHasCode.CreationTime.Hour:00}/{invoiceHasCode.PhysicalFileName}";
                        bytes = await _fileService.DownloadAsync(pathFileMinio);
                    }
                    catch (Exception ex)
                    {
                        var invoiceXmlMongo = await _mongoXmlInvoice01SignedRepository.GetByInvoiceHeaderId(invoiceHasCode.InvoiceHeaderId);
                        bytes = Convert.FromBase64String(invoiceXmlMongo.Xml);
                    }

                    return new FileDto
                    {
                        ContentType = ContentType.Xml,
                        FileName = invoiceHasCode.FileName,
                        FileBytes = bytes,
                        FileBase64 = Convert.ToBase64String(bytes, 0, bytes.Length)
                    };
                }
                else
                {
                    //download file từ minio về
                    try
                    {
                        var pathFileMinio = $"{mediaType}/{invoice.TenantId}/{invoice.CreationTime.Year}/{invoice.CreationTime.Month:00}/{invoice.CreationTime.Day:00}/{invoice.CreationTime.Hour:00}/{invoice.PhysicalFileName}";
                        bytes = await _fileService.DownloadAsync(pathFileMinio);
                    }
                    catch (Exception ex)
                    {
                        var invoiceXmlMongo = await _mongoXmlInvoice01SignedRepository.GetByInvoiceHeaderId(invoice.InvoiceHeaderId);
                        bytes = Convert.FromBase64String(invoiceXmlMongo.Xml);
                    }

                    return new FileDto
                    {
                        ContentType = ContentType.Xml,
                        FileName = fileName,
                        FileBytes = bytes,
                        FileBase64 = Convert.ToBase64String(bytes, 0, bytes.Length)
                    };

                }
            }
        }
        private MediaFileType GetMediaType(VnisType invoiceType)
        {
            switch (invoiceType)
            {
                case VnisType._01GTKT:
                    return MediaFileType.Invoice01Xml;
                case VnisType._02GTTT:
                    return MediaFileType.Invoice02Xml;
                case VnisType._03XKNB:
                    return MediaFileType.Invoice03Xml;
                case VnisType._04HGDL:
                    return MediaFileType.Invoice04Xml;
                case VnisType._05TVDT:
                    return MediaFileType.TicketXml;
                default:
                    //throw new UserFriendlyException($"Chưa phát triển tính năng download xml cho loại hóa đơn {templateNo}");
                    throw new UserFriendlyException(_localizer["Vnis.BE.Portal.CannotDowloadXml", new string[] { invoiceType.ToString() }]);//TODO: multilanguage
            }
        }
        private MediaFileType GetMediaTypeHasCode(VnisType invoiceType)
        {
            switch (invoiceType)
            {
                case VnisType._01GTKT:
                    return MediaFileType.Invoice01HasCodeTvanXml;
                case VnisType._02GTTT:
                    return MediaFileType.Invoice02HasCodeTvanXml;
                case VnisType._03XKNB:
                    return MediaFileType.Invoice03HasCodeTvanXml;
                case VnisType._04HGDL:
                    return MediaFileType.Invoice04HasCodeTvanXml;
                case VnisType._05TVDT:
                    return MediaFileType.TicketHasCodeTvanXml;
                default:
                    throw new UserFriendlyException(_localizer["Vnis.BE.Invoice.DownloadXmlInvoiceTypeNotFound", new string[] { $"{invoiceType.GetName()}" }]);
            }
        }
    }
}
