using Core.Shared.Dto;

namespace VnisCore.Report.Application.TaxReport01.Models
{
    public class InvoiceTvanInfoRequestModel : PagedFullRequestDto
    {
        /// <summary>
        /// Id bảng: TaxReport01Header
        /// </summary>
        public long TaxReportHeaderId { get; set; }

        /// <summary>
        /// Id bảng: TaxReport01DetailMapping
        /// </summary>
        public long TaxReportDetailMappingId { get; set; }

        /// <summary>
        /// Loại hoá đơn
        /// </summary>
        public short InvoiceType { get; set; }
    }
}
