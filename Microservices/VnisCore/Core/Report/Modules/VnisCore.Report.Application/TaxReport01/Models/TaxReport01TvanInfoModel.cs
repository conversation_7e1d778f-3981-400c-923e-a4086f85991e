using System;
using System.Collections.Generic;

namespace VnisCore.Report.Application.TaxReport01.Models
{
    public class TaxReport01TvanInfoModel
    {
        /// <summary>
        /// Tổng số hoá đơn không hợp lệ
        /// </summary>
        public int ToltalInvoicesRejected { get; set; }

        /// <summary>
        /// Tổng hoá đơn hợp lệ của kỳ dữ liệu
        /// </summary>
        public int ToltalInvoicesAccepted { get; set; }

        /// <summary>
        /// Tổng hoá đơn của Kỳ dữ liệu
        /// </summary>
        public int ToltalInvoices { get; set; }

        public List<InvoiceTvanInfoModel> InvoiceTvanInfoModels { get; set; }
    }

    public class InvoiceTvanInfoModel
    {
        public int Number { get; set; }

        public string SerialNo { get; set; }

        public DateTime InvoiceDate { get; set; }

        public string InvoiceStatus { get; set; }

        public short TvanStatus { get; set; }

        public string ReasonStr { get; set; }

        public List<ReasonModel> Reasons { get; set; }
    }

    public class ReasonModel
    {
        /// <summary>
        /// Mã lỗi
        /// </summary>
        public string MLoi { get; set; }

        /// <summary>
        /// Mô tả lỗi
        /// </summary>
        public string MTLoi { get; set; }

        /// <summary>
        /// Hướng dẫn xử lý
        /// </summary>
        public string HDXLy { get; set; }

        /// <summary>
        /// Ghi chú
        /// </summary>
        public string GChu { get; set; }
    }
}
