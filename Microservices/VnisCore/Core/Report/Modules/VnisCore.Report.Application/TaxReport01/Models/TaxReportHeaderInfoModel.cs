using System;
using System.Collections.Generic;

namespace VnisCore.Report.Application.TaxReport01.Models
{
    public class TaxReportHeaderInfoModel
    {
        /// <summary>
        /// Tổng số BTH
        /// </summary>
        public int TotalReport { get; set; }

        /// <summary>
        /// K<PERSON> dữ liệu
        /// </summary>
        public string ReportPeriod { get; set; }

        /// <summary>
        /// Tổng hoá đơn bị CQT từ chối
        /// </summary>
        public int ToltalInvoicesRejected { get; set; }

        /// <summary>
        /// Tổng hoá đơn hợp lệ của kỳ dữ liệu
        /// </summary>
        public int ToltalInvoicesAccepted { get; set; }

        /// <summary>
        /// Tổng hoá đơn của Kỳ dữ liệu
        /// </summary>
        public int ToltalInvoices { get; set; }
         
        /// <summary>
        /// chi tiết các detail mapping
        /// </summary>
        public List<TaxReportDetailMappingModel> TaxReportDetailMappingModels { get; set; }
    }

    public class TaxReportDetailMappingModel
    {
        /// <summary>
        /// Số BTH
        /// </summary>
        public int Index { get; set; }

        /// <summary>
        /// Thời gian ký
        /// </summary>
        public DateTime? SignedTime { get; set; }

        /// <summary>
        /// Trạng thái Tvan
        /// </summary>
        public short StatusTvan { get; set; }

        /// <summary>
        /// Thời gian nhận phản hồi
        /// </summary>
        public DateTime? TvanReceivedTime { get; set; }

        /// <summary>
        /// Thời gian gửi CQT
        /// </summary>
        public DateTime? TvanSentTime { get; set; }

        /// <summary>
        /// mã thông điệp
        /// </summary>
        public string MessageCode { get; set; }

        /// <summary>
        /// Tổng hoá đơn
        /// </summary>
        public int InvoiceQuantity { get; set; }

        /// <summary>
        /// Số lượng bị CQT từ chối
        /// </summary>
        public int InvoiceQuantityRejected { get; set; }

        /// <summary>
        /// Số lượng được CQT chấp nhận
        /// </summary>
        public int InvoiceQuantityAccepted { get; set; }

        /// <summary>
        /// danh sách id hoá đơn
        /// </summary>
        public string InvoiceIds { get; set; }

        /// <summary>
        /// trạng thái Tvan của từng hoá đơn
        /// </summary>
        public short InvoiceHeaderTvanStatus { get; set; }

        /// <summary>
        /// Id bảng: TaxReport01Header
        /// </summary>
        public long TaxReportHeaderId { get; set; }

        /// <summary>
        /// Id bảng: TaxReport01DetailMapping
        /// </summary>
        public long TaxReportDetailMappingId { get; set; }

        /// <summary>
        /// Loại hoá đơn
        /// </summary>
        public short InvoiceType { get; set; }
    }
}
