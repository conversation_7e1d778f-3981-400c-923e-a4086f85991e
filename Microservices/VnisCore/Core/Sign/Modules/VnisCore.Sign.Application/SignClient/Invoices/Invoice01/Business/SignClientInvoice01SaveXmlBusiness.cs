using Core;
using Core.Shared.Constants;
using Core.Shared.ElasticSearch;
using Core.Shared.ElasticSearch.Dto.Invoice01;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.FileManager.Interfaces;
using Core.Shared.Services;
using Dapper;
using Elasticsearch.Net;
using Microsoft.Extensions.Configuration;
using Serilog;

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using VnisCore.Core.MongoDB.Entities.Invoices.Invoice01;
using VnisCore.Core.MongoDB.IRepository;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Sign.Application.SignClient.Invoices.Invoice01.Dtos;
using VnisCore.Sign.Application.SignClient.Invoices.Invoice01.Interfaces;

namespace VnisCore.Sign.Application.SignClient.Invoices.Invoice01.Business
{
    public class SignClientInvoice01SaveXmlBusiness : ISignClientInvoice01SaveXmlBusiness
    {
        private readonly IAppFactory _appFactory;

        private readonly IVnisCoreMongoXmlInvoice01SignedRepository _mongoXmlInvoice01SignedRepository;
        private readonly IVnisCoreMongoInvoice01LogRepository _mongoInvoice01LogRepository;
        private readonly IVnisCoreMongoInvoice01Repository _mongoInvoice01Repository;
        private readonly IConfiguration _configuration;
        private readonly ElasticSearch _elasticSearch;

        public SignClientInvoice01SaveXmlBusiness(
            IAppFactory appFactory,
            IVnisCoreMongoXmlInvoice01SignedRepository mongoXmlInvoice01SignedRepository,
            IVnisCoreMongoInvoice01Repository mongoInvoice01Repository,
            IVnisCoreMongoInvoice01LogRepository mongoInvoice01LogRepository,
            IConfiguration configuration,
            ElasticSearch elasticSearch)
        {
            _appFactory = appFactory;

            _mongoXmlInvoice01SignedRepository = mongoXmlInvoice01SignedRepository;
            _mongoInvoice01Repository = mongoInvoice01Repository;
            _mongoInvoice01LogRepository = mongoInvoice01LogRepository;
            _configuration = configuration;
            _elasticSearch = elasticSearch;
        }

        public async Task SaveInvoice01XmlAsync(SignClientInvoice01SaveXml input)
        {
            try
            {
                var invoiceMongo = await _mongoInvoice01Repository.GetById(input.Id);
                if (invoiceMongo == null)
                    throw new Exception("Không tìm thấy hóa đơn");

                if (string.IsNullOrEmpty(input.Xml))
                {
                    invoiceMongo.SignStatus = (short)SignStatus.KyLoi.GetHashCode();
                    invoiceMongo.IsSyncedToElasticSearch = (short)SyncElasticSearchStatus.PendingSyncSign.GetHashCode();
                    invoiceMongo.IsSyncSignTocore = (short)SyncSignedToCoreStatus.UnProcess.GetHashCode();
                    await _mongoInvoice01Repository.UpdateAsync(invoiceMongo, true);

                    return;
                }

                //Lấy cấu hình ngày ký
                var repoSetting = _appFactory.GetServiceDependency<ISettingService>();
                var settingSignDate = await repoSetting.GetByCodeAsync(input.TenantId, SettingKey.SignBackDate.ToString());
                var signBackDate = settingSignDate is { Value: "1" };
                int.TryParse(_appFactory.Configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);

                //lưu vào bảng xml trước
                await SaveInvoice01Xml(input.TenantId, input.Xml, invoiceMongo, signBackDate, isEnableMongoDbLocalTime);

                //if (invoiceMongo.SerialNo.StartsWith('C'))
                //{
                //    await _distributedEventBus.PublishAsync(new SignInvoice01EventTvanData
                //    {
                //        TenantId = input.TenantId,
                //        InvoiceHeaderId = invoiceMongo.Id,
                //    });
                //}
            }
            catch (Exception ex)
            {
                var query = $@"UPDATE  ""{DatabaseExtension<Invoice01HeaderEntity>.GetTableName()}""
                                SET     ""SignStatus"" = {SignStatus.KyLoi.GetHashCode()}
                                WHERE   ""Id""= {input.Id}";

                await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(query);
                Log.Error(ex, ex.Message);
            }
        }

        private async Task SaveInvoice01Xml(Guid tenantId, string xml, MongoInvoice01Entity invoice, bool signBackDate, int isEnableMongoDbLocalTime)
        {
            // lưu vào DB 
            var tenantGroup = _appFactory.CurrentTenant.Group;
            var userFullName = _appFactory.CurrentUser.FullName;
            var idUser = _appFactory.CurrentUser.Id.Value;
            var userName = _appFactory.CurrentUser.UserName;

            var createdAt = DateTime.Now;
            var name = $"{invoice.SellerTaxCode}-{invoice.TemplateNo}-{invoice.SerialNo}-{invoice.InvoiceNo}.xml".Replace("/", "-");
            var fileName = $"{invoice.SellerTaxCode}-{invoice.TemplateNo}-{invoice.SerialNo}-{invoice.InvoiceNo}-{createdAt.Ticks}.xml".Replace("/", "-");

            //lưu file trên minio
            var pathFileMinio = $"{MediaFileType.Invoice01Xml}/{tenantId}/{createdAt.Year}/{createdAt.Month:00}/{createdAt.Day:00}/{createdAt.Hour:00}/{fileName}";

            var fileService = _appFactory.GetServiceDependency<IFileService>();
            await fileService.UploadAsync(pathFileMinio, Encoding.UTF8.GetBytes(xml));

            var date = signBackDate ? invoice.InvoiceDate : DateTime.Now;
            invoice.SignStatus = (short)SignStatus.DaKy;
            invoice.SellerSignedTime = isEnableMongoDbLocalTime > 0 ? date.AddHours(7) : date;
            invoice.SellerFullNameSigned = userFullName;
            invoice.SellerSignedId = idUser;
            invoice.IsSyncedToElasticSearch = (short)SyncElasticSearchStatus.PendingSyncSign.GetHashCode();
            invoice.IsSyncSignTocore = (short)SyncSignedToCoreStatus.Success.GetHashCode();

            await _mongoInvoice01Repository.UpdateAsync(invoice, true);
            await _mongoXmlInvoice01SignedRepository.InsertAsync(new MongoXmlInvoice01SignedEntity
            {
                BuyerEmail = invoice.BuyerEmail,
                CreationTime = isEnableMongoDbLocalTime > 0 ? DateTime.Now.ToLocalTime() : DateTime.Now,
                FileName = name,
                FullNameCreator = userFullName,
                Id = Guid.NewGuid(),
                InvoiceHeaderId = invoice.Id,
                InvoiceNo = invoice.InvoiceNo,
                InvoiceStatus = invoice.InvoiceStatus,
                IsGeneratedContentMail = 0,
                IsSynced = 2,
                PhysicalFileName = fileName,
                SellerTaxCode = invoice.SellerTaxCode,
                SerialNo = invoice.SerialNo,
                TemplateNo = invoice.TemplateNo,
                TenantGroup = tenantGroup,
                TenantId = invoice.TenantId,
                UserId = idUser,
                UserNameCreator = userName,
                Xml = Convert.ToBase64String(Encoding.UTF8.GetBytes(xml))
            });

            // insert log
            var invoice01LogEntity = new MongoInvoice01LogEntity
            {
                InvoiceHeaderId = invoice.Id,
                TenantId = tenantId,
                UserId = idUser,
                UserName = userName,
                Action = (short)ActionLogInvoice.Sign.GetHashCode(),
                CreationTime = isEnableMongoDbLocalTime > 0 ? DateTime.Now.ToLocalTime() : DateTime.Now,
                InvoiceType = (short)VnisType._01GTKT.GetHashCode(),
                Id = Guid.NewGuid(),
                Partition = long.Parse(invoice.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))
            };

            await _mongoInvoice01LogRepository.InsertAsync(invoice01LogEntity);

            try
            {
                //lưu vào bảng xml trước
                var invoiceXml = new Invoice01XmlEntity
                {
                    ContentType = ContentType.Xml,
                    FileName = name,
                    PhysicalFileName = fileName,
                    TenantId = tenantId,
                    InvoiceHeaderId = invoice.Id,
                    CreationTime = createdAt
                };

                var repoInvoiceXml = _appFactory.Repository<Invoice01XmlEntity, long>();
                await repoInvoiceXml.InsertAsync(invoiceXml, autoSave: true);

                var query = $@"UPDATE  ""{DatabaseExtension<Invoice01HeaderEntity>.GetTableName()}""
                                SET     ""SignStatus"" = {SignStatus.DaKy.GetHashCode()}, 
                                        ""SellerSignedTime"" = '{date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}', 
                                        ""SellerFullNameSigned"" = '{userFullName}', 
                                        ""SellerSignedId"" = '{ OracleExtension.ConvertGuidToRaw(idUser) }'
                                WHERE   ""Id""= {invoice.Id}";

                await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(query);

                await UpdateEsAsync(new List<long> { invoice.Id}, (short)SignStatus.DaKy);
            }
            catch (Exception ex)
            {
                throw new UserFriendlyException(ex.Message);
            }

            #region old
            //public async Task SaveInvoice01XmlAsync(SignClientInvoice01SaveXml input)
            //{
            //    try
            //    {
            //        var invoice = await _appFactory.VnisCoreOracle.Connection.QueryFirstOrDefaultAsync<Invoice01HeaderEntity>($"select \"Id\", \"SellerTaxCode\",\"TemplateNo\",\"SerialNo\",\"InvoiceNo\" from \"Invoice01Header\" WHERE \"Id\" = {input.Id}");
            //        if (invoice == null)
            //            throw new Exception("Không tìm thấy hóa đơn");

            //        //lưu vào bảng xml trước
            //        await SaveInvoice01Xml(input.TenantId, input.Xml, invoice);

            //        //Lấy cấu hình ngày ký
            //        var repoSetting = _appFactory.GetServiceDependency<ISettingService>();
            //        var settingSignDate = await repoSetting.GetByCodeAsync(input.TenantId, SettingKey.SignBackDate.ToString());
            //        var signBackDate = settingSignDate is { Value: "1" };

            //        //lưu lại vào hóa đơn
            //        var sellerSignedTime = signBackDate ? invoice.InvoiceDate : DateTime.Now;
            //        var sql = $@"
            //            UPDATE ""Invoice01Header""
            //            SET
            //                ""SignStatus"" = {(short)SignStatus.DaKy},
            //                ""SellerSignedTime"" = '{sellerSignedTime.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
            //                ""SellerFullNameSigned"" = '{input.FullName}',
            //                ""SellerSignedId"" = '{OracleExtension.ConvertGuidToRaw(input.UserId)}'
            //            WHERE
            //                ""Id"" = {input.Id}
            //        ";
            //        await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(sql);
            //        await _appFactory.VnisCoreOracle.Connection.ExecuteAsync("commit");
            //    }
            //    catch (Exception ex)
            //    {
            //        Log.Error(ex, ex.Message);
            //    }
            //}

            //private async Task SaveInvoice01Xml(Guid tenantId, string xml, Invoice01HeaderEntity invoice)
            //{
            //    // lưu vào DB 
            //    var createdAt = DateTime.Now;
            //    var name = $"{invoice.SellerTaxCode}-{invoice.TemplateNo}-{invoice.SerialNo}-{invoice.InvoiceNo}.xml".Replace("/", "-");
            //    var fileName = $"{invoice.SellerTaxCode}-{invoice.TemplateNo}-{invoice.SerialNo}-{invoice.InvoiceNo}-{createdAt.Ticks}.xml".Replace("/", "-");

            //    var invoiceXml = new Invoice01XmlEntity
            //    {
            //        ContentType = ContentType.Xml,
            //        FileName = name,
            //        PhysicalFileName = fileName,
            //        TenantId = tenantId,
            //        InvoiceHeaderId = invoice.Id
            //    };

            //    var repoInvoiceXml = _appFactory.Repository<Invoice01XmlEntity, long>();
            //    await repoInvoiceXml.InsertAsync(invoiceXml, autoSave: true);

            //    //lưu file trên minio
            //    var pathFileMinio = $"{MediaFileType.Invoice01Xml}/{tenantId}/{createdAt.Year}/{createdAt.Month:00}/{createdAt.Day:00}/{createdAt.Hour:00}/{fileName}";

            //    var fileService = _appFactory.GetServiceDependency<IFileService>();
            //    await fileService.UploadAsync(pathFileMinio, Encoding.UTF8.GetBytes(xml));

            //    try
            //    {
            //        if (!invoice.SerialNo.StartsWith("C"))
            //            return;

            //        // check độ ưu tiên của các nhà VAN
            //        var tvanMInvoice = _appFactory.GetServiceDependency<ITvanMInvoiceService>();
            //        var tvanVnpayInvoice = _appFactory.GetServiceDependency<ITvanVnpayService>();

            //        var maxTimesOfTvanDisconnected = short.Parse(_configuration.GetSection("MaxTimesOfTvanDisconnected").Value);

            //        var tvanSupplierModel = _config.Value;
            //        string tvanSupplierJson = JsonConvert.SerializeObject(tvanSupplierModel);
            //        var dict = JsonConvert.DeserializeObject<Dictionary<string, short>>(tvanSupplierJson);
            //        var tvans = new List<TvanModel>();
            //        foreach (var item in dict)
            //        {
            //            tvans.Add(new TvanModel
            //            {
            //                Stt = item.Value,
            //                TvanName = item.Key,
            //            });
            //        }

            //        var tvansOrderByStt = tvans.OrderBy(x => x.Stt);
            //        var registrationInvoiceType = invoice.SerialNo.First();
            //        if (registrationInvoiceType == 'C')
            //        {
            //            var tvanFirst = new TvanModel();
            //            foreach (var tvan in tvansOrderByStt)
            //            {
            //                var getCached = await _cachedService.GetTimesDisconnectedTvanFromCached(tvan.TvanName);
            //                if (getCached != null && getCached.TimesDisconnected <= maxTimesOfTvanDisconnected)
            //                {
            //                    tvanFirst.Stt = tvan.Stt;
            //                    tvanFirst.TvanName = tvan.TvanName;

            //                    break;
            //                }
            //                else
            //                {
            //                    tvanFirst = tvansOrderByStt.First();
            //                }
            //            }

            //            switch (tvanFirst.TvanName)
            //            {
            //                case nameof(tvanSupplierModel.VnPayInvoice):
            //                    var invoiceStatusVnpay = TvanStatus.UnSent;

            //                    try
            //                    {
            //                        Log.Error($"************* invoice-templateNo: {invoice.TemplateNo} *************");
            //                        var responseTvan = await tvanVnpayInvoice.SendInvoice01HasCodeAsync(tenantId, invoice.Id, invoice.SellerTaxCode, invoice.TemplateNo, invoice.SerialNo, invoice.InvoiceNo, xml);
            //                        if (responseTvan == null)
            //                        {
            //                            var getCachedTvanVnpay = await _cachedService.GetTimesDisconnectedTvanFromCached(nameof(tvanSupplierModel.VnPayInvoice));
            //                            if (getCachedTvanVnpay != null)
            //                            {
            //                                var timesDisconnected = getCachedTvanVnpay.TimesDisconnected + 1;
            //                                await _cachedService.SetTimesDisconnectedTvanToCached(nameof(tvanSupplierModel.VnPayInvoice), timesDisconnected);
            //                            }
            //                            else
            //                            {
            //                                await _cachedService.SetTimesDisconnectedTvanToCached(nameof(tvanSupplierModel.VnPayInvoice), 1);
            //                            }
            //                            try
            //                            {
            //                                var responseTvanMInvoice = await tvanMInvoice.SendInvoice01HasCodeAsync(tenantId, invoice.Id, invoice.SellerTaxCode, invoice.TemplateNo, invoice.SerialNo, invoice.InvoiceNo, xml);
            //                                if (responseTvanMInvoice != null && responseTvanMInvoice.Code == "00")
            //                                {
            //                                    invoiceStatusVnpay = TvanStatus.Sended;
            //                                }
            //                                else
            //                                {
            //                                    invoiceStatusVnpay = TvanStatus.SendError;
            //                                }
            //                            }
            //                            catch (Exception ex)
            //                            {
            //                                invoiceStatusVnpay = TvanStatus.SendError;
            //                                Log.Error(ex, ex.Message);
            //                            }
            //                            finally
            //                            {
            //                                var queryUpdateInvoice = @$"UPDATE ""{DatabaseExtension<Invoice01HeaderEntity>.GetTableName()}"" SET ""StatusTvan"" = {(short)invoiceStatusVnpay} where ""Id"" = {invoice.Id}";
            //                                await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(queryUpdateInvoice);
            //                            }
            //                        }
            //                        else
            //                        {
            //                            if (responseTvan != null && responseTvan.Code == "00")
            //                            {
            //                                invoiceStatusVnpay = TvanStatus.Sended;
            //                            }
            //                            else
            //                            {
            //                                invoiceStatusVnpay = TvanStatus.SendError;
            //                            }
            //                        }
            //                    }
            //                    catch (Exception ex)
            //                    {
            //                        invoiceStatusVnpay = TvanStatus.SendError;
            //                        Log.Error(ex, ex.Message);
            //                    }
            //                    finally
            //                    {
            //                        var queryUpdateInvoice = @$"UPDATE ""{DatabaseExtension<Invoice01HeaderEntity>.GetTableName()}"" SET ""StatusTvan"" = {(short)invoiceStatusVnpay} where ""Id"" = {invoice.Id}";
            //                        await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(queryUpdateInvoice);
            //                    }
            //                    break;

            //                case nameof(tvanSupplierModel.MInvoice):
            //                    var invoiceStatusMInvoice = TvanStatus.UnSent;
            //                    try
            //                    {
            //                        Log.Error($"************* invoice-templateNo: {invoice.TemplateNo}*************");
            //                        var responseTvan = await tvanMInvoice.SendInvoice01HasCodeAsync(tenantId, invoice.Id, invoice.SellerTaxCode, invoice.TemplateNo, invoice.SerialNo, invoice.InvoiceNo, xml);
            //                        if (responseTvan == null)
            //                        {
            //                            var getCachedTvanVnpay = await _cachedService.GetTimesDisconnectedTvanFromCached(nameof(tvanSupplierModel.MInvoice));
            //                            if (getCachedTvanVnpay != null)
            //                            {
            //                                var timesDisconnected = getCachedTvanVnpay.TimesDisconnected + 1;
            //                                await _cachedService.SetTimesDisconnectedTvanToCached(nameof(tvanSupplierModel.MInvoice), timesDisconnected);
            //                            }
            //                            else
            //                            {
            //                                await _cachedService.SetTimesDisconnectedTvanToCached(nameof(tvanSupplierModel.MInvoice), 1);
            //                            }

            //                            try
            //                            {
            //                                var responseTvanVnpay = await tvanVnpayInvoice.SendInvoice01HasCodeAsync(tenantId, invoice.Id, invoice.SellerTaxCode, invoice.TemplateNo, invoice.SerialNo, invoice.InvoiceNo, xml);
            //                                if (responseTvanVnpay != null && responseTvanVnpay.Code == "00")
            //                                {
            //                                    invoiceStatusMInvoice = TvanStatus.Sended;
            //                                }
            //                                else
            //                                {
            //                                    invoiceStatusMInvoice = TvanStatus.SendError;
            //                                }
            //                            }
            //                            catch (Exception ex)
            //                            {
            //                                invoiceStatusMInvoice = TvanStatus.SendError;
            //                                Log.Error(ex, ex.Message);
            //                            }
            //                            finally
            //                            {
            //                                var queryUpdateInvoice = @$"UPDATE ""{DatabaseExtension<Invoice01HeaderEntity>.GetTableName()}"" SET ""StatusTvan"" = {(short)invoiceStatusMInvoice} where ""Id"" = {invoice.Id}";
            //                                await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(queryUpdateInvoice);
            //                            }
            //                        }
            //                        else
            //                        {
            //                            if (responseTvan != null && responseTvan.Code == "00")
            //                            {
            //                                invoiceStatusMInvoice = TvanStatus.Sended;
            //                            }
            //                            else
            //                            {
            //                                invoiceStatusMInvoice = TvanStatus.SendError;
            //                            }
            //                        }
            //                    }
            //                    catch (Exception ex)
            //                    {
            //                        invoiceStatusMInvoice = TvanStatus.SendError;
            //                        Log.Error(ex, ex.Message);
            //                    }
            //                    finally
            //                    {
            //                        var queryUpdateInvoice = @$"UPDATE ""{DatabaseExtension<Invoice01HeaderEntity>.GetTableName()}"" SET ""StatusTvan"" = {(short)invoiceStatusMInvoice} where ""Id"" = {invoice.Id}";
            //                        await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(queryUpdateInvoice);
            //                    }

            //                    break;
            //                default:
            //                    var statusTvan = TvanStatus.UnSent;
            //                    try
            //                    {
            //                        var responseTvan = await tvanVnpayInvoice.SendInvoice01HasCodeAsync(tenantId, invoice.Id, invoice.SellerTaxCode, invoice.TemplateNo, invoice.SerialNo, invoice.InvoiceNo, xml);
            //                        if (responseTvan != null && responseTvan.Code == "00")
            //                        {
            //                            statusTvan = TvanStatus.Sended;
            //                        }
            //                        else
            //                        {
            //                            statusTvan = TvanStatus.SendError;
            //                        }
            //                    }
            //                    catch (Exception ex)
            //                    {
            //                        statusTvan = TvanStatus.SendError;
            //                        Log.Error(ex, ex.Message);
            //                    }
            //                    finally
            //                    {
            //                        var queryUpdateInvoice = @$"UPDATE ""{DatabaseExtension<Invoice01HeaderEntity>.GetTableName()}"" SET ""StatusTvan"" = {(short)statusTvan} where ""Id"" = {invoice.Id}";
            //                        await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(queryUpdateInvoice);
            //                    }
            //                    break;
            //            }
            //        }


            //    }
            //    catch (Exception ex)
            //    {
            //        Log.Error(ex, ex.Message);
            //    }
            //}
            #endregion
        }

        private async Task UpdateEsAsync(List<long> ids, short signStatus)
        {
            var tenantGroupSetting = _configuration["Settings:TenantGroupsPrivate"];

            var groups = !string.IsNullOrEmpty(tenantGroupSetting) ? tenantGroupSetting.Split(",").ToList() : new List<string>();
            if (!groups.Any())
            {
                Log.Error("Chưa có cấu hình TenantGroupsPrivate");
                return;
            }

            var group = _appFactory.CurrentTenant.Group;

            var tenantGroupIndexEs = "group-x";
            var tenantGroup = group.ToString().Replace(",", ".");
            if (groups.Contains(tenantGroup))
                tenantGroupIndexEs = $"group-{tenantGroup}";

            var client = _elasticSearch.Client("invoice01", tenantGroupIndexEs);
            try
            {
                foreach (var id in ids)
                {
                    var res = await client.UpdateAsync<object>(id, u => u
                            .Script(s => s
                                .Source($"ctx._source.{nameof(Invoice01HeaderEsDto.SignStatus).FirstCharToLowerCase()} = params.{nameof(Invoice01HeaderEsDto.SignStatus).FirstCharToLowerCase()}")
                                .Params(p => p
                                    .Add($"{nameof(Invoice01HeaderEsDto.SignStatus).FirstCharToLowerCase()}", signStatus)
                                )
                            ).Refresh(Refresh.True)
                        );

                    if (res.IsValid)
                    {
                        await _mongoInvoice01Repository.UpdateIsSyncedToElasticSearchAsync(id, (short)SyncElasticSearchStatus.Synced);
                    }
                    else
                    {
                        await _mongoInvoice01Repository.UpdateIsSyncedToElasticSearchAsync(id, (short)SyncElasticSearchStatus.PendingSyncSign);
                    }

                    Log.Information(@$"UPDATE SIGN ES RESPONSE: Id: {id} - " + res.IsValid.ToString() + "-" + res.Result + "-" + res.DebugInformation);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
            }
        }
    }
}
