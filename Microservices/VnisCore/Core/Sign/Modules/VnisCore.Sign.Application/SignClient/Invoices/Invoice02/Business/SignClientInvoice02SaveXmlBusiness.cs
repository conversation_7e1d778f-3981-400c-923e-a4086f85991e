using Core;
using Core.Shared.Constants;
using Core.Shared.ElasticSearch;
using Core.Shared.ElasticSearch.Dto.Invoice02;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.FileManager.Interfaces;
using Core.Shared.Services;

using Dapper;

using Elasticsearch.Net;

using Microsoft.Extensions.Configuration;

using Serilog;

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using VnisCore.Core.MongoDB.Entities.Invoices.Invoice02;
using VnisCore.Core.MongoDB.IRepository.Invoices.Invoice02;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
using VnisCore.Sign.Application.SignClient.Invoices.Invoice02.Dtos;
using VnisCore.Sign.Application.SignClient.Invoices.Invoice02.Interfaces;

namespace VnisCore.Sign.Application.SignClient.Invoices.Invoice02.Business
{
    public class SignClientInvoice02SaveXmlBusiness : ISignClientInvoice02SaveXmlBusiness
    {
        private readonly IAppFactory _appFactory;
        private readonly IVnisCoreMongoXmlInvoice02SignedRepository _mongoXmlInvoice02SignedRepository;
        private readonly IVnisCoreMongoInvoice02LogRepository _mongoInvoice02LogRepository;
        private readonly IVnisCoreMongoInvoice02Repository _mongoInvoice02Repository;
        private readonly IConfiguration _configuration;
        private readonly ElasticSearch _elasticSearch;

        public SignClientInvoice02SaveXmlBusiness(
            IAppFactory appFactory,
            IVnisCoreMongoXmlInvoice02SignedRepository mongoXmlInvoice02SignedRepository,
            IVnisCoreMongoInvoice02Repository mongoInvoice02Repository,
            IVnisCoreMongoInvoice02LogRepository mongoInvoice02LogRepository,
            IConfiguration configuration,
            ElasticSearch elasticSearch)
        {
            _appFactory = appFactory;
            _mongoXmlInvoice02SignedRepository = mongoXmlInvoice02SignedRepository;
            _mongoInvoice02Repository = mongoInvoice02Repository;
            _mongoInvoice02LogRepository = mongoInvoice02LogRepository;
            _configuration = configuration;
            _elasticSearch = elasticSearch;
        }

        public async Task SaveInvoice02XmlAsync(SignClientInvoice02SaveXml input)
        {
            try
            {
                var invoiceMongo = await _mongoInvoice02Repository.GetById(input.Id);
                if (invoiceMongo == null)
                    throw new Exception("Không tìm thấy hóa đơn");

                if (string.IsNullOrEmpty(input.Xml))
                {
                    invoiceMongo.SignStatus = (short)SignStatus.KyLoi.GetHashCode();
                    invoiceMongo.IsSyncedToElasticSearch = (short)SyncElasticSearchStatus.PendingSyncSign.GetHashCode();
                    invoiceMongo.IsSyncSignTocore = (short)SyncSignedToCoreStatus.UnProcess.GetHashCode();
                    await _mongoInvoice02Repository.UpdateAsync(invoiceMongo, true);

                    return;
                }

                //Lấy cấu hình ngày ký
                var repoSetting = _appFactory.GetServiceDependency<ISettingService>();
                var settingSignDate = await repoSetting.GetByCodeAsync(input.TenantId, SettingKey.SignBackDate.ToString());
                var signBackDate = settingSignDate is { Value: "1" };
                int.TryParse(_appFactory.Configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);

                //lưu vào bảng xml trước
                await SaveInvoice02Xml(input.TenantId, input.Xml, invoiceMongo, signBackDate, isEnableMongoDbLocalTime);  
            }
            catch (Exception ex)
            {
                var query = $@"UPDATE  ""{DatabaseExtension<Invoice02HeaderEntity>.GetTableName()}""
                                SET     ""SignStatus"" = {SignStatus.KyLoi.GetHashCode()}
                                WHERE   ""Id""= {input.Id}";

                await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(query);
                Log.Error(ex, ex.Message);
            }
        }

        private async Task SaveInvoice02Xml(Guid tenantId, string xml, MongoInvoice02Entity invoice, bool signBackDate, int isEnableMongoDbLocalTime)
        {
            // lưu vào DB 
            var tenantGroup = _appFactory.CurrentTenant.Group;
            var userFullName = _appFactory.CurrentUser.FullName;
            var idUser = _appFactory.CurrentUser.Id.Value;
            var userName = _appFactory.CurrentUser.UserName;

            var createdAt = DateTime.Now;
            var name = $"{invoice.SellerTaxCode}-{invoice.TemplateNo}-{invoice.SerialNo}-{invoice.InvoiceNo}.xml".Replace("/", "-");
            var fileName = $"{invoice.SellerTaxCode}-{invoice.TemplateNo}-{invoice.SerialNo}-{invoice.InvoiceNo}-{createdAt.Ticks}.xml".Replace("/", "-");

            //lưu file trên minio
            var pathFileMinio = $"{MediaFileType.Invoice02Xml}/{tenantId}/{createdAt.Year}/{createdAt.Month:00}/{createdAt.Day:00}/{createdAt.Hour:00}/{fileName}";

            var fileService = _appFactory.GetServiceDependency<IFileService>();
            await fileService.UploadAsync(pathFileMinio, Encoding.UTF8.GetBytes(xml));

            var date = signBackDate ? invoice.InvoiceDate : DateTime.Now;
            invoice.SignStatus = (short)SignStatus.DaKy;
            invoice.SellerSignedTime = isEnableMongoDbLocalTime > 0 ? date.AddHours(7) : date;
            invoice.SellerFullNameSigned = userFullName;
            invoice.SellerSignedId = idUser;
            invoice.IsSyncedToElasticSearch = (short)SyncElasticSearchStatus.PendingSyncSign.GetHashCode();
            invoice.IsSyncSignTocore = (short)SyncSignedToCoreStatus.UnProcess.GetHashCode();

            await _mongoInvoice02Repository.UpdateAsync(invoice, true);
            await _mongoXmlInvoice02SignedRepository.InsertAsync(new MongoXmlInvoice02SignedEntity
            {
                BuyerEmail = invoice.BuyerEmail,
                CreationTime = isEnableMongoDbLocalTime > 0 ? DateTime.Now.ToLocalTime() : DateTime.Now,
                FileName = name,
                FullNameCreator = userFullName,
                Id = Guid.NewGuid(),
                InvoiceHeaderId = invoice.Id,
                InvoiceNo = invoice.InvoiceNo,
                InvoiceStatus = invoice.InvoiceStatus,
                IsGeneratedContentMail = 0,
                IsSynced = 2,
                PhysicalFileName = fileName,
                SellerTaxCode = invoice.SellerTaxCode,
                SerialNo = invoice.SerialNo,
                TemplateNo = invoice.TemplateNo,
                TenantGroup = tenantGroup,
                TenantId = invoice.TenantId,
                UserId = idUser,
                UserNameCreator = userName,
                Xml = Convert.ToBase64String(Encoding.UTF8.GetBytes(xml))
            });

            // insert log
            var invoice02LogEntity = new MongoInvoice02LogEntity
            {
                InvoiceHeaderId = invoice.Id,
                TenantId = tenantId,
                UserId = idUser,
                UserName = userName,
                Action = (short)ActionLogInvoice.Sign.GetHashCode(),
                CreationTime = isEnableMongoDbLocalTime > 0 ? DateTime.Now.ToLocalTime() : DateTime.Now,
                InvoiceType = (short)VnisType._02GTTT.GetHashCode(),
                Id = Guid.NewGuid(),
                Partition = long.Parse(invoice.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm))
            };

            await _mongoInvoice02LogRepository.InsertAsync(invoice02LogEntity);

            try
            {
                //lưu vào bảng xml trước
                var invoiceXml = new Invoice02XmlEntity
                {
                    ContentType = ContentType.Xml,
                    FileName = name,
                    PhysicalFileName = fileName,
                    TenantId = tenantId,
                    InvoiceHeaderId = invoice.Id,
                    CreationTime = createdAt
                };

                var repoInvoiceXml = _appFactory.Repository<Invoice02XmlEntity, long>();
                await repoInvoiceXml.InsertAsync(invoiceXml, autoSave: true);

                var query = $@"UPDATE  ""{DatabaseExtension<Invoice02HeaderEntity>.GetTableName()}""
                                SET     ""SignStatus"" = {SignStatus.DaKy.GetHashCode()}, 
                                        ""SellerSignedTime"" = '{date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}', 
                                        ""SellerFullNameSigned"" = '{userFullName}', 
                                        ""SellerSignedId"" = '{ OracleExtension.ConvertGuidToRaw(idUser) }'
                                WHERE   ""Id""= {invoice.Id}";

                await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);
                await UpdateEsAsync(new List<long> { invoice.Id}, (short)SignStatus.DaKy);
            }
            catch (Exception ex)
            {
                throw new UserFriendlyException(ex.Message);
            }
        }

        private async Task UpdateEsAsync(List<long> ids, short signStatus)
        {
            var tenantGroupSetting = _configuration["Settings:TenantGroupsPrivate"];

            var groups = !string.IsNullOrEmpty(tenantGroupSetting) ? tenantGroupSetting.Split(",").ToList() : new List<string>();
            if (!groups.Any())
            {
                Log.Error("Chưa có cấu hình TenantGroupsPrivate");
                return;
            }

            var group = _appFactory.CurrentTenant.Group;

            var tenantGroupIndexEs = "group-x";
            var tenantGroup = group.ToString().Replace(",", ".");
            if (groups.Contains(tenantGroup))
                tenantGroupIndexEs = $"group-{tenantGroup}";

            var client = _elasticSearch.Client("invoice02", tenantGroupIndexEs);
            try
            {
                foreach (var id in ids)
                {
                    var res = await client.UpdateAsync<object>(id, u => u
                            .Script(s => s
                                .Source($"ctx._source.{nameof(Invoice02HeaderEsDto.SignStatus).FirstCharToLowerCase()} = params.{nameof(Invoice02HeaderEsDto.SignStatus).FirstCharToLowerCase()}")
                                .Params(p => p
                                    .Add($"{nameof(Invoice02HeaderEsDto.SignStatus).FirstCharToLowerCase()}", signStatus)
                                )
                            ).Refresh(Refresh.True)
                        );

                    if (res.IsValid)
                    {
                        await _mongoInvoice02Repository.UpdateIsSyncedToElasticSearchAsync(new List<long> { id }, (short)SyncElasticSearchStatus.Synced);
                    }
                    else
                    {
                        await _mongoInvoice02Repository.UpdateIsSyncedToElasticSearchAsync(new List<long> { id }, (short)SyncElasticSearchStatus.PendingSyncSign);
                    }

                    Log.Information(@$"UPDATE SIGN ES RESPONSE: Id: {id} - " + res.IsValid.ToString() + "-" + res.Result + "-" + res.DebugInformation);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
            }
        }


        #region old
        //public async Task SaveInvoice02XmlAsync(SignClientInvoice02SaveXml input)
        //{
        //    try
        //    {
        //        var invoice = await _appFactory.VnisCoreOracle.Connection.QueryFirstOrDefaultAsync<Invoice02HeaderEntity>($"select \"Id\", \"SellerTaxCode\",\"TemplateNo\",\"SerialNo\",\"InvoiceNo\" from \"Invoice02Header\" WHERE \"Id\" = {input.Id}");
        //        if (invoice == null)
        //            throw new Exception("Không tìm thấy hóa đơn");

        //        //lưu vào bảng xml trước
        //        await SaveInvoice02Xml(input.TenantId, input.Xml, invoice);

        //        //Lấy cấu hình ngày ký
        //        var repoSetting = _appFactory.GetServiceDependency<ISettingService>();
        //        var settingSignDate = await repoSetting.GetByCodeAsync(input.TenantId, SettingKey.SignBackDate.ToString());
        //        var signBackDate = settingSignDate is { Value: "1" };

        //        //lưu lại vào hóa đơn
        //        var sellerSignedTime = signBackDate ? invoice.InvoiceDate : DateTime.Now;
        //        var sql = $@"
        //            UPDATE ""Invoice02Header""
        //            SET
        //                ""SignStatus"" = {(short)SignStatus.DaKy},
        //                ""SellerSignedTime"" = '{sellerSignedTime.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
        //                ""SellerFullNameSigned"" = '{input.FullName}',
        //                ""SellerSignedId"" = '{OracleExtension.ConvertGuidToRaw(input.UserId)}'
        //            WHERE
        //                ""Id"" = {input.Id}
        //        ";
        //        await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(sql);
        //        await _appFactory.VnisCoreOracle.Connection.ExecuteAsync("commit");
        //    }
        //    catch (Exception ex)
        //    {
        //        Log.Error(ex, ex.Message);
        //    }
        //}

        //private async Task SaveInvoice02Xml(Guid tenantId, string xml, Invoice02HeaderEntity invoice)
        //{
        //    // lưu vào DB 
        //    var createdAt = DateTime.Now;
        //    var name = $"{invoice.SellerTaxCode}-{invoice.TemplateNo}-{invoice.SerialNo}-{invoice.InvoiceNo}.xml".Replace("/", "-");
        //    var fileName = $"{invoice.SellerTaxCode}-{invoice.TemplateNo}-{invoice.SerialNo}-{invoice.InvoiceNo}-{createdAt.Ticks}.xml".Replace("/", "-");

        //    var invoiceXml = new Invoice02XmlEntity
        //    {
        //        ContentType = ContentType.Xml,
        //        FileName = name,
        //        PhysicalFileName = fileName,
        //        TenantId = tenantId,
        //        InvoiceHeaderId = invoice.Id
        //    };

        //    var repoInvoiceXml = _appFactory.Repository<Invoice02XmlEntity, long>();
        //    await repoInvoiceXml.InsertAsync(invoiceXml, autoSave: true);

        //    //lưu file trên minio
        //    var pathFileMinio = $"{MediaFileType.Invoice02Xml}/{tenantId}/{createdAt.Year}/{createdAt.Month:00}/{createdAt.Day:00}/{createdAt.Hour:00}/{fileName}";

        //    var fileService = _appFactory.GetServiceDependency<IFileService>();
        //    await fileService.UploadAsync(pathFileMinio, Encoding.UTF8.GetBytes(xml));

        //    #region comment send Tvan
        //    //try
        //    //{
        //    //    if (!invoice.SerialNo.StartsWith("C"))
        //    //        return;

        //    //    var tvanVnpayInvoice = _appFactory.GetServiceDependency<ITvanVnpayService>();

        //    //    var registrationInvoiceType = invoice.SerialNo.First();
        //    //    if (registrationInvoiceType == 'C')
        //    //    {
        //    //        var statusTvan = TvanStatus.UnSent;
        //    //        try
        //    //        {
        //    //            var responseTvan = await tvanVnpayInvoice.SendInvoice02HasCodeAsync(tenantId, invoice.Id, invoice.SellerTaxCode, invoice.TemplateNo, invoice.SerialNo, invoice.InvoiceNo, xml);
        //    //            if (responseTvan != null && responseTvan.Code == "00")
        //    //            {
        //    //                statusTvan = TvanStatus.Sended;
        //    //            }
        //    //            else
        //    //            {
        //    //                statusTvan = TvanStatus.SendError;
        //    //            }
        //    //        }
        //    //        catch (Exception ex)
        //    //        {
        //    //            statusTvan = TvanStatus.SendError;
        //    //            Log.Error(ex, ex.Message);
        //    //        }
        //    //        finally
        //    //        {
        //    //            var queryUpdateInvoice = @$"UPDATE ""{DatabaseExtension<Invoice02HeaderEntity>.GetTableName()}"" SET ""StatusTvan"" = {(short)statusTvan} where ""Id"" = {invoice.Id}";
        //    //            await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(queryUpdateInvoice);
        //    //        }
        //    //    }
        //    //}
        //    //catch (Exception ex)
        //    //{
        //    //    Log.Error(ex, ex.Message);
        //    //}
        //    #endregion
        //}
        #endregion
    }
}
