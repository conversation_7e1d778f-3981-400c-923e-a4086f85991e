using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.FileManager.Interfaces;
using Core.Shared.Services;

using Dapper;

using Serilog;

using System;
using System.Globalization;
using System.Text;
using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;
using VnisCore.Sign.Application.SignClient.Invoices.Ticket.Dtos;
using VnisCore.Sign.Application.SignClient.Invoices.Ticket.Interfaces;

namespace VnisCore.Sign.Application.SignClient.Invoices.Ticket.Business
{
    public class SignClientTicketSaveXmlBusiness : ISignClientTicketSaveXmlBusiness
    {
        private readonly IAppFactory _appFactory;

        public SignClientTicketSaveXmlBusiness(
            IAppFactory appFactory)
        {
            _appFactory = appFactory;
        }

        public async Task SaveTicketXmlAsync(SignClientTicketSaveXml input)
        {
            try
            {
                var invoice = await _appFactory.VnisCoreOracle.Connection.QueryFirstOrDefaultAsync<TicketHeaderEntity>($"select \"Id\", \"SellerTaxCode\",\"TemplateNo\",\"SerialNo\",\"InvoiceNo\" from \"TicketHeader\" WHERE \"Id\" = {input.Id}");
                if (invoice == null)
                    throw new Exception("Không tìm thấy hóa đơn");

                //lưu vào bảng xml trước
                await SaveTicketXml(input.TenantId, input.Xml, invoice);

                //Lấy cấu hình ngày ký
                var repoSetting = _appFactory.GetServiceDependency<ISettingService>();
                var settingSignDate = await repoSetting.GetByCodeAsync(input.TenantId, SettingKey.SignBackDate.ToString());
                var signBackDate = settingSignDate is { Value: "1" };

                //lưu lại vào hóa đơn
                var sellerSignedTime = signBackDate ? invoice.InvoiceDate : DateTime.Now;
                var sql = $@"
                    UPDATE ""TicketHeader""
                    SET
                        ""SignStatus"" = {(short)SignStatus.DaKy},
                        ""SellerSignedTime"" = '{sellerSignedTime.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                        ""SellerFullNameSigned"" = '{input.FullName}',
                        ""SellerSignedId"" = '{OracleExtension.ConvertGuidToRaw(input.UserId)}'
                    WHERE
                        ""Id"" = {input.Id}
                ";
                await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(sql);
                await _appFactory.VnisCoreOracle.Connection.ExecuteAsync("commit");
            }
            catch (Exception ex)
            {
                var query = $@"UPDATE  ""{DatabaseExtension<TicketHeaderEntity>.GetTableName()}""
                                SET     ""SignStatus"" = {SignStatus.KyLoi.GetHashCode()}
                                WHERE   ""Id""= {input.Id}";

                await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(query);
                await _appFactory.VnisCoreOracle.Connection.ExecuteAsync("commit");
                Log.Error(ex, ex.Message);
            }
        }

        private async Task SaveTicketXml(Guid tenantId, string xml, TicketHeaderEntity invoice)
        {
            // lưu vào DB 
            var createdAt = DateTime.Now;
            var name = $"{invoice.SellerTaxCode}-{invoice.TemplateNo}-{invoice.SerialNo}-{invoice.InvoiceNo}.xml".Replace("/", "-");
            var fileName = $"{invoice.SellerTaxCode}-{invoice.TemplateNo}-{invoice.SerialNo}-{invoice.InvoiceNo}-{createdAt.Ticks}.xml".Replace("/", "-");

            var invoiceXml = new TicketXmlEntity
            {
                ContentType = ContentType.Xml,
                FileName = name,
                PhysicalFileName = fileName,
                TenantId = tenantId,
                InvoiceHeaderId = invoice.Id
            };

            var repoInvoiceXml = _appFactory.Repository<TicketXmlEntity, long>();
            await repoInvoiceXml.InsertAsync(invoiceXml, autoSave: true);

            //lưu file trên minio
            var pathFileMinio = $"{MediaFileType.TicketXml}/{tenantId}/{createdAt.Year}/{createdAt.Month:00}/{createdAt.Day:00}/{createdAt.Hour:00}/{fileName}";

            var fileService = _appFactory.GetServiceDependency<IFileService>();
            await fileService.UploadAsync(pathFileMinio, Encoding.UTF8.GetBytes(xml));

            #region comment Send Tvan
            //try
            //{
            //    if (!invoice.SerialNo.StartsWith("C"))
            //        return;

            //    var tvanVnpayInvoice = _appFactory.GetServiceDependency<ITvanVnpayService>();
            //    var registrationInvoiceType = invoice.SerialNo.First();
            //    if (registrationInvoiceType == 'C')
            //    {
            //        var statusTvan = TvanStatus.UnSent;
            //        try
            //        {
            //            var responseTvan = await tvanVnpayInvoice.SendTicketHasCodeAsync(tenantId, invoice.Id, invoice.SellerTaxCode, invoice.TemplateNo, invoice.SerialNo, invoice.InvoiceNo, xml);
            //            if (responseTvan != null && responseTvan.Code == "00")
            //            {
            //                statusTvan = TvanStatus.Sended;
            //            }
            //            else
            //            {
            //                statusTvan = TvanStatus.SendError;
            //            }
            //        }
            //        catch (Exception ex)
            //        {
            //            statusTvan = TvanStatus.SendError;
            //            Log.Error(ex, ex.Message);
            //        }
            //        finally
            //        {
            //            var queryUpdateInvoice = @$"UPDATE ""{DatabaseExtension<TicketHeaderEntity>.GetTableName()}"" SET ""StatusTvan"" = {(short)statusTvan} where ""Id"" = {invoice.Id}";
            //            await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(queryUpdateInvoice);
            //        }
            //    }


            //}
            //catch (Exception ex)
            //{
            //    Log.Error(ex, ex.Message);
            //}
            #endregion
        }
    }
}
