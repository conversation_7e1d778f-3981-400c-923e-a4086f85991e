using Core.Domain.Repositories.EntityFrameworkCore;
using Core.EntityFrameworkCore;
using Core.SettingManagement;
using System;
using VnisCore.AuthDatabase.Oracle.EntityFrameworkCore.EntityFrameworkCore;
using ISettingRepository = VnisCore.System.Infrastructure.IRepository.ISettingRepository;

namespace VnisCore.System.Infrastructure.Repository
{
    public class SettingRepository : EfCoreRepository<VnisCoreAuthDatabaseOracleDbContext, Setting, Guid>, ISettingRepository
    {
        public SettingRepository(IDbContextProvider<VnisCoreAuthDatabaseOracleDbContext> dbContextProvider) : base(dbContextProvider)
        {
        }

        public Setting GetByCodeAndTenant(string code, Guid tenantId)
        {
            throw new NotImplementedException();
        }
    }
}
