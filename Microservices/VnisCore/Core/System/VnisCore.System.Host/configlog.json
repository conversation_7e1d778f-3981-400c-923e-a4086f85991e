{
  "Serilog": {
    "Using": [ "Serilog.Sinks.PersistentFile" ],
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Information",
        "Microsoft.EntityFrameworkCore": "Warning"
      }
    },
    "Enrich": [
      "FromLogContext",
      "WithCorrelationId"
    ],
    "WriteTo": [
      {
        "Name": "PersistentFile",
        "Args": {
          "path": "D:\\Logs\\system\\log.txt", // "D:\\Logs\\backoffice\\log.txt", // Đường dẫn file log
          "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff} [{Level:u3}] [{CorrelationId}] {Message:lj}{NewLine}{Exception:lj}", // Mẫu template
          "rollingInterval": "Day", // Xác định khoảng thời gian file log mới sẽ được tạo (ví dụ: "Day" - h<PERSON>ng ng<PERSON>y, "Hour" - hàng giờ)
          "fileSizeLimitBytes": 52428800, // Giới hạn kích thước của file log trước khi nó được roll sang file mới. Ví dụ: 10485760 bytes (10 MB).
          "retainedFileCountLimit": null, // Số lượng file log cũ sẽ được giữ lại trước khi xóa
          "rollOnFileSizeLimit": true, // Nếu true, khi kích thước file log vượt quá fileSizeLimitBytes, một file log mới sẽ được tạo
          "preserveLogFilename": true, // Nếu true, tên file log ban đầu sẽ được giữ nguyên, và các file roll sẽ được thêm chỉ số (ví dụ: log-001.txt)
          "shared": true, // Nếu true, nhiều tiến trình có thể ghi vào cùng một file log
          "flushToDiskInterval": "00:00:10" // Khoảng thời gian giữa các lần ghi log ra đĩa. Ví dụ: "00:00:10" là 10 giây.
        }
      },
      {
        "Name": "PersistentFile",
        "Args": {
          "path": "D:\\Logs\\system\\log.json",
          "formatter": "Serilog.Formatting.Json.JsonFormatter, Serilog",
          "persistentFileRollingInterval": "Day",
          "rollingInterval": "Day",
          "fileSizeLimitBytes": 52428800,
          "retainedFileCountLimit": null,
          "preserveLogFilename": true,
          "rollOnFileSizeLimit": true,
          "shared": true
        }
      }
    ]
  }
}
