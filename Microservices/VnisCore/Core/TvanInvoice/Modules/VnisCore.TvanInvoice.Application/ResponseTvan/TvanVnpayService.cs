using Core;
using Core.AspNetCore.Mvc;
using Core.Tvan.Constants;
using Core.Tvan.Enums;
using Core.Tvan.Interfaces.InvoiceHasCode;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;

using Newtonsoft.Json;

using Serilog;

using System;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;

using VnisCore.TvanInvoice.Application.ResponseTvan.Interfaces;
using VnisCore.TvanInvoice.Application.ResponseTvan.Models;
using VnisCore.TvanInvoice.Application.ResponseTvan.Services;
using VnisCore.TvanInvoice.Application.ResponseTvan.Services.InvoiceErrors;
using VnisCore.TvanInvoice.Application.ResponseTvan.Services.PITDeductionDocument;
using VnisCore.TvanInvoice.Application.ResponseTvan.Services.ResponseUndefinedTCT;
using VnisCore.TvanInvoice.Application.ResponseTvan.Services.TvanCheckInvoice;

namespace VnisCore.TvanInvoice.Application.ResponseTvan
{
    [ApiController]
    public class TvanVnpayService : AbpController
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IInvoiceHasCodeFactory _invoiceHasCodeFactory;

        public TvanVnpayService(IServiceProvider serviceProvider,
                                   IInvoiceHasCodeFactory invoiceHasCodeFactory)
        {
            _serviceProvider = serviceProvider;
            _invoiceHasCodeFactory = invoiceHasCodeFactory;
        }

        [AllowAnonymous]
        [HttpPost(Utilities.ApiUrlBase + "Response")]
        public async Task<IActionResult> TvanResponseAsync([FromBody] TvanVnpayReceiverRequestModel requestModel)
        {
            var result = new TvanVnpayReceiverResponseModel
            {
                Code = "00"
            };

            await Task.Delay(500);

            try
            {
                var xml = Encoding.UTF8.GetString( Convert.FromBase64String(requestModel.Data));
                //var request = content.XmlDeserialize<TDiepModel<object, object>>();

                var fileBytes = Encoding.UTF8.GetBytes(xml);
                if (fileBytes.Length == 0)
                    throw new UserFriendlyException("File xml không được để trống");

                // Create a new XML document.
                XmlDocument document = new XmlDocument();
                document.PreserveWhitespace = true;
                document.LoadXml(xml);

                var nodeTTChung = document.DocumentElement.ChildNodes[0];

                if (nodeTTChung == null)
                    throw new UserFriendlyException("Không tìm thấy thẻ dữ liệu thông tin chung");

                var valueMLTDiep = nodeTTChung["MLTDiep"].InnerText;
                var valueMTDiep = nodeTTChung["MTDiep"].InnerText;

                using var scope = _serviceProvider.CreateScope();

                var responseServices = scope.ServiceProvider.GetServices<IReceiveXmlFromTvanService>();

                switch (int.Parse(valueMLTDiep))
                {
                    case (int)MLTDiep._102:
                        var registrationService = responseServices.FirstOrDefault(x => x.GetType().Name == typeof(RegistrationReceivedResponseService).Name);
                        await registrationService.ReceiveXmlFromTvanAsync(xml, TransmissionPartnerEnum.Vnpay);
                        break;
                    case (int)MLTDiep._103:
                        var service = responseServices.FirstOrDefault(x => x.GetType().Name == typeof(RegistrationAcceptedResponseService).Name);
                        await service.ReceiveXmlFromTvanAsync(xml, TransmissionPartnerEnum.Vnpay);
                        break;
                    case (int)MLTDiep._104:
                        break;
                    case (int)MLTDiep._105:
                        break;
                    case (int)MLTDiep._110:
                        var pitReceiveService = responseServices.FirstOrDefault(x => x.GetType().Name == typeof(PITDeductionDocumentDeclarationReceivedResponseService).Name);
                        await pitReceiveService.ReceiveXmlFromTvanAsync(xml, TransmissionPartnerEnum.Vnpay);
                        break;
                    case (int)MLTDiep._111:
                        var pitAcceptService = responseServices.FirstOrDefault(x => x.GetType().Name == typeof(PITDeductionDocumentDeclarationAcceptedResponseService).Name);
                        await pitAcceptService.ReceiveXmlFromTvanAsync(xml, TransmissionPartnerEnum.Vnpay);
                        break;
                    case (int)MLTDiep._202:
                        var invoiceHasCodeService = await _invoiceHasCodeFactory.GetService(xml);
                        await invoiceHasCodeService.HandleResponseTvan(xml, TransmissionPartnerEnum.Vnpay);
                        break;
                    case (int)MLTDiep._204:
                        var notificationTemplate01TBKTDLService = responseServices.FirstOrDefault(x => x.GetType().Name == typeof(NotificationTemplate01TBKTDLService).Name);
                        await notificationTemplate01TBKTDLService.ReceiveXmlFromTvanAsync(xml, TransmissionPartnerEnum.Vnpay);
                        break;
                    case (int)MLTDiep._205:
                        break;
                    case (int)MLTDiep._213:
                        var pITDeductionDocumentReceivedResponseService = responseServices.FirstOrDefault(x => x.GetType().Name == typeof(PITDeductionDocumentReceivedResponseService).Name);
                        await pITDeductionDocumentReceivedResponseService.ReceiveXmlFromTvanAsync(xml, TransmissionPartnerEnum.Vnpay);
                        break;
                    case (int)MLTDiep._301:
                        var invoiceErrorResponseService = responseServices.FirstOrDefault(x => x.GetType().Name == typeof(InvoiceErrorResponseService).Name);
                        await invoiceErrorResponseService.ReceiveXmlFromTvanAsync(xml, TransmissionPartnerEnum.Vnpay);
                        break;
                    case (int)MLTDiep._302:
                        var tvanCheckInvoice01Service = responseServices.FirstOrDefault(x => x.GetType().Name == typeof(TvanCheckInvoiceService).Name);
                        await tvanCheckInvoice01Service.ReceiveXmlFromTvanAsync(xml, TransmissionPartnerEnum.Vnpay);
                        break;
                    case (int)MLTDiep._999:
                        //TODO: THUVT lam phan nay
                        break;
                    case (int)MLTDiep.Error:
                        var responseUndefinedService = responseServices.FirstOrDefault(x => x.GetType().Name == typeof(ResponseUndefinedTCTService).Name);
                        await responseUndefinedService.ReceiveXmlFromTvanAsync(xml, TransmissionPartnerEnum.Vnpay);
                        break;
                    default:
                        result.Code = "03";
                        result.Message = "MTDiep không đúng";
                        break;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);

                if (!string.IsNullOrEmpty(ex.InnerException?.StackTrace))
                    Log.Error(ex.InnerException?.StackTrace);

                if (result.Code == "00")
                {
                    result.Code = "-1";
                    result.Message = ex.Message;
                }
            }

            Log.Information($"------------- ContentResponse TvanVnpay: {JsonConvert.SerializeObject(result)}");
            return Ok(result);
        }

    }
}
