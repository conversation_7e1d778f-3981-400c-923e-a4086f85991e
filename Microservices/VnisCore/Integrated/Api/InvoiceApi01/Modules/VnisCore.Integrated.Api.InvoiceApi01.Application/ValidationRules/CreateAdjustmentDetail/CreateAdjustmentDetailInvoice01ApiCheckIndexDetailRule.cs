using Core.Shared.Validations;
using System.Linq;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Requests.Commands;

namespace VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.CreateAdjustmentDetail
{
    /// <summary>
    /// kiểm tra Index của detail
    /// </summary>
    public class CreateAdjustmentDetailInvoice01ApiCheckIndexDetailRule : IValidationRule<CreateAdjustmentDetailInvoice01ApiRequestModel, ValidationResult>
    {
        public CreateAdjustmentDetailInvoice01ApiCheckIndexDetailRule()
        {
        }

        public ValidationResult Handle(CreateAdjustmentDetailInvoice01ApiRequestModel input)
        {
            if (input.InvoiceDetails == null || !input.InvoiceDetails.Any())
                return new ValidationResult(true);

            foreach (var detail in input.InvoiceDetails)
            {
                if (detail.Index > 9999)
                {
                    return new ValidationResult(false, $"Số thứ tự hàng hóa phải nhỏ hơn hoặc bằng 9999");
                }
                if (detail.Index <= 0)
                {
                    return new ValidationResult(false, $"Số thứ tự hàng hóa phải lớn hơn 0");
                }
            }

            return new ValidationResult(true);
        }
    }
}
