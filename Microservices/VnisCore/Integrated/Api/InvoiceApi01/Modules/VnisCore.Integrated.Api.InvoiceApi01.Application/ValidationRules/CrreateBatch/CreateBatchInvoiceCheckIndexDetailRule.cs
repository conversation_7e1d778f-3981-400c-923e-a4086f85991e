using Core.Shared.Validations;
using System.Linq;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Requests.Commands;

namespace VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.CreateBatch
{
    /// <summary>
    /// kiểm tra Index của detail
    /// </summary>
    public class CreateBatchInvoiceCheckIndexDetailRule : IValidationRule<CreateBatchInvoice01RequestModel, ValidationResult>
    {
        public CreateBatchInvoiceCheckIndexDetailRule()
        {
        }

        public ValidationResult Handle(CreateBatchInvoice01RequestModel input)
        {
            foreach (var invoice in input.Datas)
            {
                if (invoice.Invoice.InvoiceDetails == null || !invoice.Invoice.InvoiceDetails.Any())
                    continue;

                foreach (var detail in invoice.Invoice.InvoiceDetails)
                {
                    if (detail.Index > 9999)
                    {
                        return new ValidationResult(false, $"Số thứ tự hàng hóa {detail.ProductName} phải nhỏ hơn hoặc bằng 9999");
                    }
                    if (detail.Index <= 0)
                    {
                        return new ValidationResult(false, $"Số thứ tự hàng hóa {detail.ProductName} phải lớn hơn 0");
                    }
                }
            }

            return new ValidationResult(true);
        }
    }
}
