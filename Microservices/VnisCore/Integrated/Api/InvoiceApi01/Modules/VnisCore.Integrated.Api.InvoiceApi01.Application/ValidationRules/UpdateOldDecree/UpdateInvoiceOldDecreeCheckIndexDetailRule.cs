using Core.Dto.Shared;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Requests.Commands;

namespace VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.UpdateOldDecree
{
    /// <summary>
    /// check các fieldname của headerextra có đúng không
    /// </summary>
    public class UpdateInvoiceOldDecreeCheckIndexDetailRule : IValidationRule<UpdateInvoice01OldDecreeRequestModel, ValidationResult>
    {
        private readonly IValidationContext _validationContext;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;

        public UpdateInvoiceOldDecreeCheckIndexDetailRule(
            IValidationContext validationContext,
            IStringLocalizer<CoreLocalizationResource> localizier)
        {
            _localizier = localizier;
            _validationContext = validationContext;
        }

        public ValidationResult Handle(UpdateInvoice01OldDecreeRequestModel input)
        {
            if (input.InvoiceDetails == null || !input.InvoiceDetails.Any())
                return new ValidationResult(true);

            foreach (var detail in input.InvoiceDetails)
            {
                if (detail.Index > 9999)
                {
                    return new ValidationResult(false, $"Số thứ tự hàng hóa phải nhỏ hơn hoặc bằng 9999");
                }
                if (detail.Index <= 0)
                {
                    return new ValidationResult(false, $"Số thứ tự hàng hóa phải lớn hơn 0");
                }
            }

            if (input.InvoiceHeaderExtras == null || !input.InvoiceHeaderExtras.Any())
                return new ValidationResult(true);

            var tenantId = _validationContext.GetItem<Guid>("TenantId");

            //lấy các header field

            var dataInfoToCreate = _validationContext.GetItem<List<CreateInvoice01GetDataValidDto>>("DataInfoToCreate");
            var headerFieldNames = dataInfoToCreate.Where(x => x.Type == ValidateDataType.HeaderField.GetHashCode()).Select(x => x.FieldName).ToList();

            var commandHeaderFieldNames = input.InvoiceHeaderExtras.Select(x => x.FieldName).ToList();
            var expects = commandHeaderFieldNames.Except(headerFieldNames);

            if (expects.Any())
                return new ValidationResult(false, _localizier["Vnis.BE.Invoice01.Api.Intergration.InvoiceHeaderFieldNotFound", new string[] { string.Join(",", expects) }]);

            input.InvoiceHeaderExtras.RemoveAll(x => string.IsNullOrEmpty(x.FieldValue));

            return new ValidationResult(true);
        }
    }
}
