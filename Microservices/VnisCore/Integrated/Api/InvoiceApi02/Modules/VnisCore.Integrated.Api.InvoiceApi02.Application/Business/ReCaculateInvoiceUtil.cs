using Core.Shared.Constants;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VnisCore.Core.MongoDB.Entities.Invoices.Invoice02;

namespace VnisCore.Integrated.Api.InvoiceApi02.Application.Business
{
    public class ReCaculateInvoiceUtil
    {

        #region Function hỗ trợ tính toán
        /// <summary>
        /// Kiểm tra hóa đơn chỉ có duy nhất sản phẩm CKTM hay không
        /// true: Phải
        /// false: Không
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public static bool IsOnlyCKTM(MongoInvoice02Entity request)
        {
            if (!request.InvoiceDetails.IsNullOrEmpty())
            {
                var countCKTM = request.InvoiceDetails.Where(d => d.ProductType == ProductType.ChietKhauThuongMai.GetHashCode()).Count();
                var countDetail = request.InvoiceDetails.Count();

                if (countCKTM == countDetail && countDetail == 1)
                {
                    return true;
                }
            }
            return false;
        }
        /// <summary>
        /// Xác định tự động loại chiết khấu
        /// </summary>
        public static DiscountType GetAutoDiscountType(MongoInvoice02Entity input)
        {
            if (input.TotalDiscountAmount != 0
                    && input.InvoiceDetails.Where(detail => detail.DiscountAmount == 0).Count() == input.InvoiceDetails.Count
                    && !input.InvoiceDetails.Where(detail => detail.ProductType == ProductType.ChietKhauThuongMai.GetHashCode()).Any())
            {
                return DiscountType.TongChietKhau;
            }
            if ((input.TotalDiscountAmount != 0
                && input.InvoiceDetails.Where(detail => detail.DiscountAmount != 0).Any() || input.InvoiceDetails.Where(detail => detail.ProductType == ProductType.ChietKhauThuongMai.GetHashCode()).Any()))
            {
                return DiscountType.ChietKhauHangHoa;
            }
            if (input.TotalDiscountAmount == 0
                && !input.InvoiceDetails.Where(detail => detail.DiscountAmount != 0).Any())
            {
                return DiscountType.KhongChietKhau;
            }
            return DiscountType.KhongChietKhau;
        }

        /// <summary>
        /// Kiểm tra thuế suất chi tiết hóa đơn và chi tiết thuế suất
        /// </summary>
        /// <param name="array1"></param>
        /// <param name="array2"></param>
        /// <returns></returns>
        private static bool IsSameVatPercent(List<decimal> array1, List<decimal> array2)
        {
            if (array1.Count != array2.Count)
            {
                return false;
            }
            for (int i = 0; i < array1.Count; i++)
            {
                if (array1[i] != array2[i])
                {
                    return false;
                }
            }
            return true;
        }

        private static bool IsChietKhauTong(MongoInvoice02Entity request)
        {
            if (request.TotalDiscountAmount != 0
                            && request.InvoiceDetails.Where(detail => detail.DiscountAmount == 0).Count() == request.InvoiceDetails.Count
                            && !request.InvoiceDetails.Where(detail => detail.ProductType == ProductType.ChietKhauThuongMai.GetHashCode()).Any())
            {
                return true;
            }
            return false;
        }
        #endregion
    }
}
