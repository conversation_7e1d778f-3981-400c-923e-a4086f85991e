using Core.Shared.Validations;
using System;
using System.Collections.Generic;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Requests.Commands;

namespace VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.Create
{
    /// <summary>
    /// kiểm tra field name detail Extra
    /// </summary>
    public class CreateInvoiceCheckBuyerInformationRule : IValidationRule<CreateInvoice02RequestModel, ValidationResult>
    {

        public CreateInvoiceCheckBuyerInformationRule()
        {
        }

        public ValidationResult Handle(CreateInvoice02RequestModel input)
        {
            // Nếu MST người bán nhập
            // C<PERSON>n nhập thông tin Tên đơn vị và Địa chỉ mua
            if (!input.BuyerTaxCode.IsNullOrEmpty())
            {
                if (input.BuyerFullName.IsNullOrEmpty() || input.BuyerAddressLine.IsNullOrEmpty())
                {
                    return new ValidationResult(false, "<PERSON><PERSON> nhập thông tin Mã số thuế người mua, yê<PERSON> c<PERSON><PERSON> bắt buộc nhập Tên đơn vị và Địa chỉ. Vui lòng kiểm tra lại");
                }
            }

            return new ValidationResult(true);
        }
    }
}
