using Core.Shared.Validations;
using System;
using System.Collections.Generic;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Requests.Commands;

namespace VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.CreateAdjustmentHeader
{
    /// <summary>
    /// kiểm tra field name detail Extra
    /// </summary>
    public class CreateAdjustmentHeaderInvoiceCheckBuyerInformationRule : IValidationRule<CreateAdjustmentHeaderInvoice02ApiRequestModel, ValidationResult>
    {

        public CreateAdjustmentHeaderInvoiceCheckBuyerInformationRule()
        {
        }

        public ValidationResult Handle(CreateAdjustmentHeaderInvoice02ApiRequestModel input)
        {
            // Nếu MST người bán nhập
            // Cần nhập thông tin Tên đơn vị và Địa chỉ mua
            if (!input.BuyerTaxCode.IsNullOrEmpty())
            {
                if (input.BuyerFullName.IsNullOrEmpty() || input.BuyerAddressLine.IsNullOrEmpty())
                {
                    return new ValidationResult(false, "<PERSON><PERSON> nhập thông tin Mã số thuế người mua, yêu cầu bắt buộc nhập Tên đơn vị và Địa chỉ. Vui lòng kiểm tra lại");
                }
            }

            return new ValidationResult(true);
        }
    }
}
