using Core.Shared.Validations;
using System;
using System.Collections.Generic;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Requests.Commands;

namespace VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.CreateBatch
{
    /// <summary>
    /// kiểm tra field name detail Extra
    /// </summary>
    public class CreateBatchInvoiceCheckBuyerInformationRule : IValidationRule<CreateBatchInvoice02RequestModel, ValidationResult>
    {

        public CreateBatchInvoiceCheckBuyerInformationRule()
        {
        }

        public ValidationResult Handle(CreateBatchInvoice02RequestModel input)
        {
            foreach (var invoice in input.Datas)
            {
                // Nếu MST người bán nhập
                // Cần nhập thông tin Tên đơn vị và Địa chỉ mua
                if (!invoice.Invoice.BuyerTaxCode.IsNullOrEmpty())
                {
                    if (invoice.Invoice.BuyerFullName.IsNullOrEmpty() || invoice.Invoice.BuyerAddressLine.IsNullOrEmpty())
                    {
                        return new ValidationResult(false, "<PERSON>hi nhập thông tin Mã số thuế người mua, yêu cầu bắt buộc nhập Tên đơn vị và Địa chỉ. Vui lòng kiểm tra lại");
                    }
                }
            }

            return new ValidationResult(true);
        }
    }
}
