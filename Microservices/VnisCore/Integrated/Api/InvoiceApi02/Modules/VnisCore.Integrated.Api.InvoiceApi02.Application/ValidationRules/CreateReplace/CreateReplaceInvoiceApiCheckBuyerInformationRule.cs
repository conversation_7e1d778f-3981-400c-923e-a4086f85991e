using Core.Shared.Validations;
using System;
using System.Collections.Generic;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Requests.Commands;

namespace VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.CreateReplace
{
    /// <summary>
    /// Ki<PERSON>m tra thông tin người mua
    /// </summary>
    public class CreateReplaceInvoiceApiCheckBuyerInformationRule : IValidationRule<CreateReplaceInvoice02ApiRequestModel, ValidationResult>
    {

        public CreateReplaceInvoiceApiCheckBuyerInformationRule()
        {
        }

        public ValidationResult Handle(CreateReplaceInvoice02ApiRequestModel input)
        {
            // Nếu MST người bán nhập
            // Cần nhập thông tin Tên đơn vị và Địa chỉ mua
            if (!input.BuyerTaxCode.IsNullOrEmpty())
            {
                if (input.BuyerFullName.IsNullOrEmpty() || input.BuyerAddressLine.IsNullOrEmpty())
                {
                    return new ValidationResult(false, "<PERSON><PERSON> nhập thông tin Mã số thuế người mua, yêu cầu bắt buộc nhập Tên đơn vị và Địa chỉ. Vui lòng kiểm tra lại");
                }
            }

            return new ValidationResult(true);
        }
    }
}
