using Core.Shared.Validations;
using System.Linq;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Requests.Commands;

namespace VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.UpdateAdjustmentDetail
{
    /// <summary>
    /// kiểm tra Index của detail
    /// </summary>
    public class UpdateAdjustmentDetailInvoice02ApiCheckIndexDetailRule : IValidationRule<UpdateAdjustmentDetailInvoice02ApiRequestModel, ValidationResult>
    {
        public UpdateAdjustmentDetailInvoice02ApiCheckIndexDetailRule()
        {
        }

        public ValidationResult Handle(UpdateAdjustmentDetailInvoice02ApiRequestModel input)
        {
            if (input.InvoiceDetails == null || !input.InvoiceDetails.Any())
                return new ValidationResult(true);

            foreach (var detail in input.InvoiceDetails)
            {
                if (detail.Index > 9999)
                {
                    return new ValidationResult(false, $"Số thứ tự hàng hóa phải nhỏ hơn hoặc bằng 9999");
                }
                if (detail.Index <= 0)
                {
                    return new ValidationResult(false, $"Số thứ tự hàng hóa phải lớn hơn 0");
                }
            }

            return new ValidationResult(true);
        }
    }
}
