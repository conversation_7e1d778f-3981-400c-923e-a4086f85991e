using Core;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Cached;
using Core.Shared.Cached.Dtos;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Messages;
using Core.Shared.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;
using VnisCore.Integrated.Api.TicketApi.Application.Interfaces;
using VnisCore.Integrated.Api.TicketApi.Application.Models.Requests.Commands;
using VnisCore.Integrated.Api.TicketApi.Application.RabbitClient.Abstractions;
using VnisCore.Integrated.Api.TicketApi.Application.Repositories;
using VnisCore.Integrated.Api.TicketApi.Application.Services;

namespace VnisCore.Integrated.Api.TicketApi.Application.RabbitClient.Services
{
    public class UpdateRootInvoice05Service : IInvoiceHandler
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IInvoiceService<TicketHeaderEntity, TicketHeaderFieldEntity, TicketDetailFieldEntity> _invoiceService;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;
        private readonly IAppFactory _appFactory;
        private readonly ITicketCacheBusiness _ticketCacheBusiness;
        private readonly ICachingCatalogBusiness _cachingCatalogBusiness;

        public UpdateRootInvoice05Service(
            IServiceProvider serviceProvider,
            IStringLocalizer<CoreLocalizationResource> localizier,
            IInvoiceService<TicketHeaderEntity, TicketHeaderFieldEntity, TicketDetailFieldEntity> invoiceService,
            IAppFactory appFactory,
            ITicketCacheBusiness ticketCacheBusiness,
            ICachingCatalogBusiness cachingCatalogBusiness)
        {
            _localizier = localizier;
            _invoiceService = invoiceService;
            _serviceProvider = serviceProvider;
            _appFactory = appFactory;
            _ticketCacheBusiness = ticketCacheBusiness;
            _cachingCatalogBusiness = cachingCatalogBusiness;
        }

        public async Task<InvoiceCommandResponseModel> HandleRequestAsync(InvoiceCommandRequestModel message)
        {
            var request = JsonConvert.DeserializeObject<UpdateTicketApiRequestModel>(CompressionExtensions.Unzip(message.Data));

            var repoTicketHeader = _serviceProvider.GetService<IInvoiceHeaderRepository<TicketHeaderEntity>>();
            var invoice = await repoTicketHeader.GetByIdRawAsync(message.TenantId, message.InvoiceId.Value);
            if (invoice == null)
                throw new UserFriendlyException(_localizier["Vnis.BE.Ticket.Api.Intergration.InvoiceNotFound"]);

            var repoDetail = _serviceProvider.GetService<IInvoiceDetailRepository<TicketDetailEntity>>();
            var details = await repoDetail.QueryByIdInvoiceHeaderRawAsync(message.TenantId, invoice.Id);

            var repoTaxBreakDown = _serviceProvider.GetService<ITicketTaxBreakdownRepository>();
            var taxbreakdowns = await repoTaxBreakDown.QueryByInvoiceHeaderIdRawAsync(invoice.Id);

            #region Loại tiền tệ
            var currencyCache = await _cachingCatalogBusiness.GetOrSetCachedCurrencyAsync(message.TenantId);
            var currencies = _appFactory.ObjectMapper.Map<List<CurrencyCachingDto>, List<CurrencyEntity>>(currencyCache);
            var fromCurrency = currencies.Where(x => x.IsDefault == true).FirstOrDefault();
            var toCurrency = currencies.Where(x => x.CurrencyCode == request.Currency).FirstOrDefault();
            #endregion

            #region Loại thuế suất
            var taxeCaches = await _cachingCatalogBusiness.GetOrSetCachedTaxsAsync(message.TenantId);
            var taxes = new Dictionary<decimal, Tuple<string, string>>();
            foreach (var item in taxeCaches)
            {
                taxes.Add(item.Value, new Tuple<string, string>(item.Name, item.Display));
            }
            #endregion

            #region Hàng hóa
            // Không cần thiết lấy dữ liệu ra
            // Do chỉ dùng đẻ gán ProductId bảng Detail
            // Trường này ko dùng cho nghiệp vụ gì
            //var productCodes = request.InvoiceDetails.Select(x => x.ProductCode?.Trim()).Distinct().ToList();
            var products = new Dictionary<string, ProductEntity>();
            //if (productCodes.Any())
            //{
            //    var productCaches = await _cachingCatalogBusiness.GetOrSetCachedProductAsync(message.TenantId);
            //    var convertData = _appFactory.ObjectMapper.Map<List<ProductCachingDto>, List<ProductEntity>>(productCaches);
            //    products = convertData.Where(x => productCodes.Contains(x.ProductCode)).ToDictionary(x => x.ProductCode, x => x);
            //}
            #endregion

            #region Loại hàng hóa
            var productTypeIds = products.Values.Where(x => x.ProductTypeId.HasValue).Select(x => x.ProductTypeId.Value).Distinct().ToList();
            var productTypes = new Dictionary<long, ProductTypeEntity>();
            if (productTypeIds.Any())
            {
                productTypes = await _ticketCacheBusiness.GetProductTypesFromCacheAsync(message.TenantId, productTypeIds);
            }
            #endregion

            #region Đơn vị tính
            var unitNames = request.InvoiceDetails.Select(x => x.UnitName?.Trim()).Distinct().ToList();
            var units = new Dictionary<string, UnitEntity>();
            if (unitNames.Any())
            {
                var unitCaches = await _cachingCatalogBusiness.GetOrSetCachedUnitAsync(message.TenantId);
                var convertData = _appFactory.ObjectMapper.Map<List<UnitCachingDto>, List<UnitEntity>>(unitCaches);
                units = convertData.Where(x => unitNames.Contains(x.Name)).ToDictionary(x => x.Name, x => x);
            }
            #endregion

            #region Trường mở rộng
            var headerFields = (await _ticketCacheBusiness.GetHeaderFieldsFromCacheAsync(message.TenantId)).ToDictionary(x => x.FieldName, x => x);
            var detailFields = (await _ticketCacheBusiness.GetDetailFieldsFromCacheAsync(message.TenantId)).ToDictionary(x => x.FieldName, x => x);
            #endregion

            var invoiceDate = invoice.InvoiceDate;
            var totalAmount = invoice.TotalAmount;
            var totalPaymentAmount = invoice.TotalPaymentAmount;
            var totalVatAmount = invoice.TotalVatAmount;

            var TicketService = _serviceProvider.GetService<ITicketService>();
            await TicketService.UpdateRawAsync(
                request,
                invoice,
                details,
                message.TenantId,
                message.UserId,
                taxbreakdowns,
                toCurrency,
                headerFields,
                detailFields,
                taxes,
                products,
                productTypes);

            //nếu có số hóa đơn thì mới update lại ngày hóa đơn cuối cùng
            if (invoice.Number.HasValue)
                await _invoiceService.UpdateLastInvoiceDateRawAsync(invoice.TenantId, invoice.InvoiceTemplateId, invoice.Number.Value);

            message.InvoiceId = invoice.Id;

            var result = new InvoiceCommandResponseModel
            {
                //ProcessCode = message.ProcessCode.Value,
                Id = message.InvoiceId.Value,
                TenantId = message.TenantId,
                Type = message.Type,
                UserId = message.UserId,
                UserFullName = message.UserFullName,
                UserName = message.UserName,
                Method = HubMethod.UpdateInvoice,
                InvoiceStatus = EnumExtension.ToEnum<InvoiceStatus>(invoice.InvoiceStatus),
                SignStatus = invoice.ApproveStatus == ApproveStatus.ChoDuyet.GetHashCode() ? SignStatus.ChoDuyet : EnumExtension.ToEnum<SignStatus>(invoice.SignStatus),
                ApproveStatus = EnumExtension.ToEnum<ApproveStatus>(invoice.ApproveStatus),
                ApproveCancelStatus = EnumExtension.ToEnum<ApproveStatus>(invoice.ApproveCancelStatus),
                ApproveDeleteStatus = EnumExtension.ToEnum<ApproveStatus>(invoice.ApproveDeleteStatus),
                Number = invoice.Number,
                InvoiceNo = invoice.InvoiceNo,
                TemplateNo = invoice.TemplateNo,
                SerialNo = invoice.SerialNo,
                Resource = message.Resource,
                State = InvoiceActionState.UpdateRoot,
                ActionLogInvoice = ActionLogInvoice.Update,
                Action = InvoiceAction.UpdateRoot,
                ActionAt = DateTime.Now,
                ActionAtUtc = DateTime.UtcNow,
                ConnectionId = message.ConnectionId,
                InvoiceSummaryStatistic = new InvoiceSummaryStatisticResponseModel
                {
                    NewInvoiceDate = request.InvoiceDate,
                    NewTotalAmount = request.TotalAmount,
                    NewTotalPaymentAmount = request.TotalPaymentAmount,
                    NewTotalVatAmount = request.TotalVatAmount,
                    OldInvoiceDate = invoiceDate,
                    OldTotalAmount = totalAmount,
                    OldTotalPaymentAmount = totalPaymentAmount,
                    OldTotalVatAmount = totalVatAmount
                }
            };

            return result;
        }
    }

}
