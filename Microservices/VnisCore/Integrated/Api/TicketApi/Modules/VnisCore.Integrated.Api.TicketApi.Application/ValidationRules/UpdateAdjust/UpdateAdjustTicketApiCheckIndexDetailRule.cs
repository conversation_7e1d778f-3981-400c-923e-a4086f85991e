using Core.Shared.Validations;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VnisCore.Integrated.Api.TicketApi.Application.Models.Requests.Commands;

namespace VnisCore.Integrated.Api.TicketApi.Application.ValidationRules.UpdateAdjust
{
    /// <summary>
    /// kiểm tra Index của detail
    /// </summary>
    public class UpdateAdjustTicketApiCheckIndexDetailRule : IValidationRule<UpdateAdjustTicketApiRequestModel, ValidationResult>
    {
        public UpdateAdjustTicketApiCheckIndexDetailRule()
        {
        }

        public ValidationResult Handle(UpdateAdjustTicketApiRequestModel input)
        {
            if (input.InvoiceDetails == null || !input.InvoiceDetails.Any())
                return new ValidationResult(true);

            foreach (var detail in input.InvoiceDetails)
            {
                if (detail.Index > 9999)
                {
                    return new ValidationResult(false, $"<PERSON><PERSON> thứ tự hàng hóa {detail.ProductName} phải nhỏ hơn hoặc bằng 9999");
                }
                if (detail.Index <= 0)
                {
                    return new ValidationResult(false, $"Số thứ tự hàng hóa {detail.ProductName} phải lớn hơn 0");
                }
            }

            return new ValidationResult(true);
        }
    }
}
