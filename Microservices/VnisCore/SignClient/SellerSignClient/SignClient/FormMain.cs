using Newtonsoft.Json;

using NLog;

using SignClient.Models;

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Configuration;
using System.IO;
using System.Net;
using System.Security.Cryptography.X509Certificates;
using System.Security.Cryptography.Xml;
using System.Text;
using System.Web;
using System.Windows.Forms;
using System.Xml;
using System.Xml.Serialization;

namespace SignClient
{
    public partial class FormMain : Form
    {
        private List<CertificateModel> _certificates = new List<CertificateModel>();
        private CertificateModel _certificate = null;
        private int _type;
        private bool _isRegistration = false;
        private readonly Logger _logger = LogManager.GetCurrentClassLogger();

        public FormMain()
        {
            InitializeComponent();
        }

        private void Main_Load(object sender, EventArgs e)
        {
            //L<PERSON>y danh sách chứng thư số trên máy và check những cái đã đăng ký
            _certificates = GetCertificates();
            var registed = CheckCertificates();
            foreach (var local in _certificates)
            {
                var index = DgvCertificates.Rows.Add();
                DgvCertificates.Rows[index].Cells["SubjectName"].Value = local.SubjectName;
                DgvCertificates.Rows[index].Cells["SerialNumber"].Value = local.SerialNumber;
                DgvCertificates.Rows[index].Cells["FromDate"].Value = local.StartDate.ToString("dd/MM/yyyy");
                DgvCertificates.Rows[index].Cells["ToDate"].Value = local.EndDate.ToString("dd/MM/yyyy");

                var server = registed.Find(x => x.SubjectName == local.SubjectName && x.SerialNumber == local.SerialNumber);
                if (server != null)
                {
                    local.Registed = true;
                    DgvCertificates.Rows[index].Cells["Status"].Value = "Đã đăng ký";
                }
                else
                {
                    local.Registed = false;
                    DgvCertificates.Rows[index].Cells["Status"].Value = "Chưa đăng ký";
                }
            }


            //Tự động chọn chứng thư số đầu tiên đã đăng ký
            _certificate = _certificates.Find(x => x.Registed);
            if (_certificate != null)
            {
                _certificate.Selected = true;
                var index = _certificates.IndexOf(_certificate);
                DgvCertificates.Rows[index].Cells["Select"].Value = true;
            }

            ToggleButtons();
        }



        private void Main_Closed(object sender, EventArgs e)
        {
            Application.Exit();
        }


        private void BtnCancel_Click(object sender, EventArgs e)
        {
            Application.Exit();
        }

        private void DgvCertificates_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            foreach (DataGridViewRow row in DgvCertificates.Rows)
            {
                _certificates[row.Index].Selected = false;
                row.Cells["Select"].Value = false;
            }

            var cell = DgvCertificates.CurrentRow.Cells["Select"];
            var select = bool.Parse(cell.EditedFormattedValue.ToString());
            _certificates[e.RowIndex].Selected = select;

            if (select)
                _certificate = _certificates[e.RowIndex];
            else
                _certificate = null;

            ToggleButtons();
        }

        private void Main_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Control && e.KeyCode == Keys.F1)
            {
                e.SuppressKeyPress = true;
                if (Btn01.Enabled)
                {
                    if (BgwSigning.IsBusy)
                        return;

                    _type = TypeActionModel.Invoice01.GetHashCode();
                    ToggleLoading(false);
                    BgwSigning.RunWorkerAsync();
                }
            }

            if (e.Control && e.KeyCode == Keys.F2)
            {
                e.SuppressKeyPress = true;
                if (Btn02.Enabled)
                {
                    if (BgwSigning.IsBusy)
                        return;

                    _type = TypeActionModel.Invoice02.GetHashCode();
                    ToggleLoading(false);
                    BgwSigning.RunWorkerAsync();
                }
            }

            if (e.Control && e.KeyCode == Keys.F3)
            {
                e.SuppressKeyPress = true;
                if (Btn03.Enabled)
                {
                    if (BgwSigning.IsBusy)
                        return;

                    _type = TypeActionModel.Invoice03.GetHashCode();
                    ToggleLoading(false);
                    BgwSigning.RunWorkerAsync();
                }
            }

            if (e.Control && e.KeyCode == Keys.F4)
            {
                e.SuppressKeyPress = true;
                if (Btn04.Enabled)
                {
                    if (BgwSigning.IsBusy)
                        return;

                    _type = TypeActionModel.Invoice04.GetHashCode();
                    ToggleLoading(false);
                    BgwSigning.RunWorkerAsync();
                }
            }

            if (e.Control && e.KeyCode == Keys.F5)
            {
                e.SuppressKeyPress = true;
                if (Btn04.Enabled)
                {
                    if (BgwSigning.IsBusy)
                        return;

                    _type = TypeActionModel.Ticket.GetHashCode();
                    ToggleLoading(false);
                    BgwSigning.RunWorkerAsync();
                }
            }

            //if (e.Control && e.KeyCode == Keys.F11)
            //{
            //    e.SuppressKeyPress = true;
            //    if (Btn05.Enabled)
            //    {
            //        if (BgwSigning.IsBusy)
            //            return;

            //        _type = 11;
            //        ToggleLoading(false);
            //        BgwSigning.RunWorkerAsync();
            //    }
            //}


            //if (e.Control && e.KeyCode == Keys.F9)
            //{
            //    e.SuppressKeyPress = true;
            //    if (BtnBc26.Enabled)
            //        MessageBox.Show("Sign BC26");
            //}
        }

        private void Btn01_Click(object sender, EventArgs e)
        {
            if (BgwSigning.IsBusy)
                return;

            _type = TypeActionModel.Invoice01.GetHashCode();
            _isRegistration = false;
            ToggleLoading(false);
            BgwSigning.RunWorkerAsync();
        }

        private void Btn02_Click(object sender, EventArgs e)
        {
            if (BgwSigning.IsBusy)
                return;

            _type = TypeActionModel.Invoice02.GetHashCode();
            _isRegistration = false;
            ToggleLoading(false);
            BgwSigning.RunWorkerAsync();
        }

        private void Btn03_Click(object sender, EventArgs e)
        {
            if (BgwSigning.IsBusy)
                return;

            _type = TypeActionModel.Invoice03.GetHashCode();
            _isRegistration = false;
            ToggleLoading(false);
            BgwSigning.RunWorkerAsync();
        }

        private void Btn04_Click(object sender, EventArgs e)
        {
            if (BgwSigning.IsBusy)
                return;

            _type = TypeActionModel.Invoice04.GetHashCode();
            _isRegistration = false;
            ToggleLoading(false);
            BgwSigning.RunWorkerAsync();
        }

        private void Btn05_Click(object sender, EventArgs e)
        {
            if (BgwSigning.IsBusy)
                return;

            _type = TypeActionModel.Ticket.GetHashCode();
            _isRegistration = false;
            ToggleLoading(false);
            BgwSigning.RunWorkerAsync();
        }

        private void Btn06_Click(object sender, EventArgs e)
        {
            if (BgwSigning.IsBusy)
                return;

            _type = TypeActionModel.PITDocument.GetHashCode();
            _isRegistration = false;
            ToggleLoading(false);
            BgwSigning.RunWorkerAsync();
        }

        private void Btn07_Click(object sender, EventArgs e)
        {
            if (BgwSigning.IsBusy)
                return;

            _type = TypeActionModel.PITLetter.GetHashCode();
            _isRegistration = false;
            ToggleLoading(false);
            BgwSigning.RunWorkerAsync();
        }

        private void btnSignRegistration_Click(object sender, EventArgs e)
        {
            if (BgwSigning.IsBusy)
                return;

            _isRegistration = true;
            ToggleLoading(false);
            BgwSigning.RunWorkerAsync();
        }

        private void BtnSignPITDeductionDocumentDeclaration_Click(object sender, EventArgs e)
        {
            if (BgwSigning.IsBusy)
                return;

            _type = TypeActionModel._52DKSDCT.GetHashCode();
            _isRegistration = false;
            ToggleLoading(false);
            BgwSigning.RunWorkerAsync();
        }

        private void btnSignInvoice01Error_Click(object sender, EventArgs e)
        {
            if (BgwSigning.IsBusy)
                return;

            _type = TypeActionModel.Invoice01Error.GetHashCode();
            _isRegistration = false;
            ToggleLoading(false);
            BgwSigning.RunWorkerAsync();
        }

        private void btnSignInvoice02Error_Click(object sender, EventArgs e)
        {
            if (BgwSigning.IsBusy)
                return;

            _type = TypeActionModel.Invoice02Error.GetHashCode();
            _isRegistration = false;
            ToggleLoading(false);
            BgwSigning.RunWorkerAsync();
        }

        private void btnSignInvoice03Error_Click(object sender, EventArgs e)
        {
            if (BgwSigning.IsBusy)
                return;

            _type = TypeActionModel.Invoice03Error.GetHashCode();
            _isRegistration = false;
            ToggleLoading(false);
            BgwSigning.RunWorkerAsync();
        }

        private void btnSignInvoice04Error_Click(object sender, EventArgs e)
        {
            if (BgwSigning.IsBusy)
                return;

            _type = TypeActionModel.Invoice04Error.GetHashCode();
            _isRegistration = false;
            ToggleLoading(false);
            BgwSigning.RunWorkerAsync();
        }

        private void btnSignInvoice05Error_Click(object sender, EventArgs e)
        {
            if (BgwSigning.IsBusy)
                return;

            _type = TypeActionModel.TicketError.GetHashCode();
            _isRegistration = false;
            ToggleLoading(false);
            BgwSigning.RunWorkerAsync();
        }

        private void BtnSignTaxReport01_Click(object sender, EventArgs e)
        {
            if (BgwSigning.IsBusy)
                return;

            _type = TypeActionModel.TaxReport01.GetHashCode();
            _isRegistration = false;
            ToggleLoading(false);
            BgwSigning.RunWorkerAsync();
        }

        private void BtnSignTaxReport03_Click(object sender, EventArgs e)
        {
            if (BgwSigning.IsBusy)
                return;

            _type = TypeActionModel.TaxReport03.GetHashCode();
            _isRegistration = false;
            ToggleLoading(false);
            BgwSigning.RunWorkerAsync();
        }

        private void BtnSignInvoiceError32_Click(object sender, EventArgs e)
        {
            if (BgwSigning.IsBusy)
                return;

            _type = TypeActionModel.InvoiceError32.GetHashCode();
            _isRegistration = false;
            ToggleLoading(false);
            BgwSigning.RunWorkerAsync();
        }

        private void BgwSigning_DoWork(object sender, DoWorkEventArgs e)
        {
            Invoke(new MethodInvoker(delegate
            {
                LblResult.Items.Clear();
                LblResult.Items.Add("Bắt đầu");
                DgvCertificates.Enabled = false;
            }));

            var worker = sender as BackgroundWorker;
            while (true)
            {
                if (worker != null && worker.CancellationPending)
                {
                    e.Cancel = true;
                    break;
                }

                if (!_isRegistration)
                {
                    if (_type == TypeActionModel._52DKSDCT.GetHashCode())
                    {
                        #region Ký đăng ký sử dụng chứng từ
                        Invoke(new MethodInvoker(delegate
                        {
                            LblResult.Items.Add("Đang tìm đăng ký sử dụng chứng từ để ký");
                            LblResult.SelectedIndex = LblResult.Items.Count - 1;
                        }));

                        var registration = GetInvoice();
                        if (registration == null)
                            return;

                        //Ký đăng ký sử dụng chứng từ
                        Invoke(new MethodInvoker(delegate
                        {
                            LblResult.Items.Add($"Đang ký đăng ký sử dụng chứng từ Id-{registration.Id}");
                            LblResult.SelectedIndex = LblResult.Items.Count - 1;
                        }));
                        var xml = SellerSign(registration.Xml, registration.IdData, registration.IdObject, registration.IdSignature);

                        //Kiểm tra XML sau khi ký
                        if (!ValidationAfterSign(xml, out string messageAfter))
                        {
                            Invoke(new MethodInvoker(delegate
                            {
                                LblResult.Items.Add($"Đăng ký sử dụng chứng từ Id-{registration.Id} sau khi ký lỗi: {messageAfter}");
                                LblResult.SelectedIndex = LblResult.Items.Count - 1;
                            }));
                            return;
                        }

                        //Lưu hóa đơn đã ký
                        Invoke(new MethodInvoker(delegate
                        {
                            LblResult.Items.Add($"Đang lưu đăng ký sử dụng chứng từ Id-{registration.Id}");
                            LblResult.SelectedIndex = LblResult.Items.Count - 1;
                        }));
                        PostInvoice(registration.Id, xml);
                        #endregion
                    }
                    else
                    {
                        #region Ký hóa đơn
                        //Lấy hóa đơn chờ ký
                        Invoke(new MethodInvoker(delegate
                        {
                            LblResult.Items.Add("Đang tìm hóa đơn để ký");
                            LblResult.SelectedIndex = LblResult.Items.Count - 1;
                        }));

                        // Luồng ký thông báo sai sót
                        if (_type == TypeActionModel.Invoice01Error.GetHashCode()
                            || _type == TypeActionModel.Invoice02Error.GetHashCode()
                            || _type == TypeActionModel.Invoice03Error.GetHashCode()
                            || _type == TypeActionModel.Invoice04Error.GetHashCode()
                            || _type == TypeActionModel.TicketError.GetHashCode()
                            || _type == TypeActionModel.PITDocumentError.GetHashCode()
                            || _type == TypeActionModel.InvoiceError32.GetHashCode()
                            )
                        {
                            var invoices = GetInvoicesError();
                            if (invoices == null || invoices.Count == 0)
                                return;

                            // Ký từng Thông báo sai sót
                            foreach (var invoice in invoices)
                            {
                                var validateBeforeSign = true;
                                string messageBefore = string.Empty;

                                switch (_type)
                                {
                                    case (int)TypeActionModel.PITDocumentError:
                                        validateBeforeSign = ValidationPITErrorBeforeSign(invoice.Xml, out messageBefore);
                                        break;
                                    default:
                                        validateBeforeSign = ValidationInvoiceErrorBeforeSign(invoice.Xml, out messageBefore);
                                        break;
                                }

                                //Kiểm tra XML trc khi ký
                                if (!validateBeforeSign)
                                {
                                    Invoke(new MethodInvoker(delegate
                                    {
                                        if (_type == TypeActionModel.InvoiceError32.GetHashCode())
                                        {
                                            LblResult.Items.Add($"Thông báo sai sót TBSS {invoice.TbssHeaderId} trước khi ký lỗi: {messageBefore}");
                                        }
                                        else
                                        {
                                            LblResult.Items.Add($"Thông báo sai sót GroupCode {invoice.GroupCode} trước khi ký lỗi: {messageBefore}");
                                        }

                                        LblResult.SelectedIndex = LblResult.Items.Count - 1;

                                    }));
                                    return;
                                }

                                //Ký hóa đơn
                                Invoke(new MethodInvoker(delegate
                                {
                                    if (_type == TypeActionModel.InvoiceError32.GetHashCode())
                                    {
                                        LblResult.Items.Add($"Đang ký thông báo sai sót Id: {invoice.TbssHeaderId}");
                                    }
                                    else
                                    {
                                        LblResult.Items.Add($"Đang ký thông báo sai sót GroupCode: {invoice.GroupCode}");
                                    }

                                    LblResult.SelectedIndex = LblResult.Items.Count - 1;
                                }));

                                var xml = SellerSignInvoiceError(invoice.Xml, invoice.IdData, invoice.IdObject, invoice.IdSignature);

                                //Kiểm tra XML sau khi ký
                                if (!ValidationAfterSign(xml, out string messageAfter))
                                {
                                    Invoke(new MethodInvoker(delegate
                                    {
                                        if (_type == TypeActionModel.InvoiceError32.GetHashCode())
                                        {
                                            LblResult.Items.Add($"Thông báo sai sót Id: sau khi ký lỗi: {messageAfter}");
                                        }
                                        else
                                        {
                                            LblResult.Items.Add($"Thông báo sai sót GroupCode: sau khi ký lỗi: {messageAfter}");
                                        }

                                        LblResult.SelectedIndex = LblResult.Items.Count - 1;
                                    }));
                                    return;
                                }

                                //Lưu hóa đơn đã ký
                                Invoke(new MethodInvoker(delegate
                                {
                                    if (_type == TypeActionModel.InvoiceError32.GetHashCode())
                                    {
                                        LblResult.Items.Add($"Đang lưu thông báo sai sót Id: {invoice.TbssHeaderId}");
                                    }
                                    else
                                    {
                                        LblResult.Items.Add($"Đang lưu thông báo sai sót GroupCode: {invoice.GroupCode}");
                                    }

                                    LblResult.SelectedIndex = LblResult.Items.Count - 1;
                                }));

                                PostInvoiceError(invoice.GroupCode, xml, invoice.TbssHeaderId);
                            }
                        }
                        else if (_type == TypeActionModel.TaxReport01.GetHashCode())
                        {
                            // báo cáo thuế 01
                            Invoke(new MethodInvoker(delegate
                            {
                                LblResult.Items.Add("Đang tìm báo cáo thuế 01/TH-HDDT để ký");
                                LblResult.SelectedIndex = LblResult.Items.Count - 1;
                            }));

                            var taxReport = GetTaxReport();
                            if (taxReport == null)
                                return;

                            // Ký từng Thông báo sai sót
                            foreach (var partOfTaxReportXml in taxReport.XmlDatas)
                            {
                                //Kiểm tra XML trc khi ký
                                if (!ValidationTaxReport01BeforeSign(partOfTaxReportXml.Xml, out string messageBefore))
                                {
                                    Invoke(new MethodInvoker(delegate
                                    {
                                        LblResult.Items.Add($"Báo cáo thuế 01/TH-HDDT id: {taxReport.Id} lần bổ sung thứ: {partOfTaxReportXml.AdditionalTimes} trước khi ký lỗi: {messageBefore}");
                                        LblResult.SelectedIndex = LblResult.Items.Count - 1;
                                    }));
                                    return;
                                }

                                //Ký báo cáo thuế
                                Invoke(new MethodInvoker(delegate
                                {
                                    LblResult.Items.Add($"Đang ký báo cáo thuế 01/TH-HDDT id: {taxReport.Id} lần bổ sung thứ: {partOfTaxReportXml.AdditionalTimes}");
                                    LblResult.SelectedIndex = LblResult.Items.Count - 1;
                                }));

                                var xml = SellerSign(partOfTaxReportXml.Xml, partOfTaxReportXml.IdData, partOfTaxReportXml.IdObject, partOfTaxReportXml.IdSignature);

                                //Kiểm tra XML sau khi ký
                                if (!ValidationAfterSign(xml, out string messageAfter))
                                {
                                    Invoke(new MethodInvoker(delegate
                                    {
                                        LblResult.Items.Add($"Báo cáo thuế 01/TH-HDDT id: {taxReport.Id} lần bổ sung thứ: {partOfTaxReportXml.AdditionalTimes} sau khi ký lỗi: {messageAfter}");
                                        LblResult.SelectedIndex = LblResult.Items.Count - 1;
                                    }));
                                    return;
                                }

                                partOfTaxReportXml.Xml = xml;
                            }

                            //Lưu báo cáo thuế đã ký
                            Invoke(new MethodInvoker(delegate
                            {
                                LblResult.Items.Add($"Đang lưu báo cáo thuế 01/TH-HDDT id: {taxReport.Id}");
                                LblResult.SelectedIndex = LblResult.Items.Count - 1;
                            }));

                            PostTaxReportAfterSign(taxReport);
                        }
                        else if (_type == TypeActionModel.TaxReport03.GetHashCode())
                        {
                            // báo cáo thuế 03
                            //Lấy báo cáo thuế chờ ký
                            Invoke(new MethodInvoker(delegate
                            {
                                LblResult.Items.Add("Đang tìm báo cáo thuế 03/DL-HDDT để ký");
                                LblResult.SelectedIndex = LblResult.Items.Count - 1;
                            }));

                            var taxReport = GetTaxReport();
                            if (taxReport == null)
                                return;

                            // Ký từng Thông báo sai sót
                            foreach (var partOfTaxReportXml in taxReport.XmlDatas)
                            {
                                //Kiểm tra XML trc khi ký
                                if (!ValidationTaxReport03BeforeSign(partOfTaxReportXml.Xml, out string messageBefore))
                                {
                                    Invoke(new MethodInvoker(delegate
                                    {
                                        LblResult.Items.Add($"Báo cáo thuế 03/DL-HDDT id: {taxReport.Id} lần bổ sung thứ: {partOfTaxReportXml.AdditionalTimes} trước khi ký lỗi: {messageBefore}");
                                        LblResult.SelectedIndex = LblResult.Items.Count - 1;
                                    }));
                                    return;
                                }

                                //Ký báo cáo thuế
                                Invoke(new MethodInvoker(delegate
                                {
                                    LblResult.Items.Add($"Đang ký báo cáo thuế 03/DL-HDDT id: {taxReport.Id} lần bổ sung thứ: {partOfTaxReportXml.AdditionalTimes}");
                                    LblResult.SelectedIndex = LblResult.Items.Count - 1;
                                }));

                                var xml = SellerSign(partOfTaxReportXml.Xml, partOfTaxReportXml.IdData, partOfTaxReportXml.IdObject, partOfTaxReportXml.IdSignature);

                                //Kiểm tra XML sau khi ký
                                if (!ValidationAfterSign(xml, out string messageAfter))
                                {
                                    Invoke(new MethodInvoker(delegate
                                    {
                                        LblResult.Items.Add($"Báo cáo thuế 03/DL-HDDT id: {taxReport.Id} lần bổ sung thứ: {partOfTaxReportXml.AdditionalTimes} sau khi ký lỗi: {messageAfter}");
                                        LblResult.SelectedIndex = LblResult.Items.Count - 1;
                                    }));
                                    return;
                                }

                                partOfTaxReportXml.Xml = xml;
                            }

                            //Lưu báo cáo thuế đã ký
                            Invoke(new MethodInvoker(delegate
                            {
                                LblResult.Items.Add($"Đang lưu báo cáo thuế 03/DL-HDDT id: {taxReport.Id}");
                                LblResult.SelectedIndex = LblResult.Items.Count - 1;
                            }));

                            PostTaxReportAfterSign(taxReport);
                        }
                        else if (_type == TypeActionModel.PITDocument.GetHashCode())
                        {
                            var invoice = GetInvoice();

                            if (invoice == null)
                                return;

                            //Kiểm tra XML trc khi ký
                            if (!ValidationPITDocumentBeforeSign(invoice.Xml, out string messageBefore))
                            {
                                Invoke(new MethodInvoker(delegate
                                {
                                    LblResult.Items.Add($"Chứng từ mẫu số: {invoice.TemplateNoPitDocument} - ký hiệu: {invoice.SerialNo} - số: {invoice.InvoiceNo} trước khi ký lỗi: {messageBefore}");
                                    LblResult.SelectedIndex = LblResult.Items.Count - 1;
                                }));
                                return;
                            }

                            //Ký hóa đơn
                            Invoke(new MethodInvoker(delegate
                            {
                                LblResult.Items.Add($"Đang ký chứng từ mẫu số: {invoice.TemplateNoPitDocument} - ký hiệu: {invoice.SerialNo} - số: {invoice.InvoiceNo}");
                                LblResult.SelectedIndex = LblResult.Items.Count - 1;
                            }));
                            var xml = SellerSignPITDocument(invoice.Xml, invoice.IdData, invoice.IdObject, invoice.IdSignature);

                            //Kiểm tra XML sau khi ký
                            if (!ValidationAfterSign(xml, out string messageAfter))
                            {
                                Invoke(new MethodInvoker(delegate
                                {
                                    LblResult.Items.Add($"Chứng từ mẫu số: {invoice.TemplateNoPitDocument} - ký hiệu: {invoice.SerialNo} - số: {invoice.InvoiceNo} sau khi ký lỗi: {messageAfter}");
                                    LblResult.SelectedIndex = LblResult.Items.Count - 1;
                                }));
                                return;
                            }

                            //Lưu hóa đơn đã ký
                            Invoke(new MethodInvoker(delegate
                            {
                                LblResult.Items.Add($"Đang lưu chứng từ mẫu số: {invoice.TemplateNoPitDocument} - ký hiệu: {invoice.SerialNo} - số: {invoice.InvoiceNo}");
                                LblResult.SelectedIndex = LblResult.Items.Count - 1;
                            }));

                            PostInvoice(invoice.Id, xml);
                        }
                        else if (_type == TypeActionModel.PITLetter.GetHashCode())
                        {
                            var invoice = GetInvoice();

                            if (invoice == null)
                                return;

                            //Kiểm tra XML trc khi ký
                            if (!ValidationPITLetterBeforeSign(invoice.Xml, out string messageBefore))
                            {
                                Invoke(new MethodInvoker(delegate
                                {
                                    LblResult.Items.Add($"Chứng từ mẫu số: {invoice.TemplateNoPitDocument} - Id: {invoice.Id} trước khi ký lỗi: {messageBefore}");
                                    LblResult.SelectedIndex = LblResult.Items.Count - 1;
                                }));
                                return;
                            }

                            //Ký hóa đơn
                            Invoke(new MethodInvoker(delegate
                            {
                                LblResult.Items.Add($"Đang ký chứng từ mẫu số: {invoice.TemplateNoPitDocument} - Id: {invoice.Id}");
                                LblResult.SelectedIndex = LblResult.Items.Count - 1;
                            }));
                            var xml = SellerSignPITLetter(invoice.Xml, invoice.IdData, invoice.IdObject, invoice.IdSignature);

                            //Kiểm tra XML sau khi ký
                            if (!ValidationAfterSign(xml, out string messageAfter))
                            {
                                Invoke(new MethodInvoker(delegate
                                {
                                    LblResult.Items.Add($"Chứng từ mẫu số: {invoice.TemplateNoPitDocument} - Id: {invoice.Id} sau khi ký lỗi: {messageAfter}");
                                    LblResult.SelectedIndex = LblResult.Items.Count - 1;
                                }));
                                return;
                            }

                            //Lưu hóa đơn đã ký
                            Invoke(new MethodInvoker(delegate
                            {
                                LblResult.Items.Add($"Đang lưu chứng từ mẫu số: {invoice.TemplateNoPitDocument} - Id: {invoice.Id}");
                                LblResult.SelectedIndex = LblResult.Items.Count - 1;
                            }));

                            PostInvoice(invoice.Id, xml);
                        }
                        else
                        {
                            var invoice = GetInvoice();

                            //var invoice = new SignModel
                            //{
                            //    Id = 1,
                            //    InvoiceNo = "000000012",
                            //    SerialNo = "K21TYY",
                            //    TemplateNo = "1",
                            //    Xml = "<?xml version=\"1.0\" encoding=\"UTF - 8\"?><HDon><DLHDon id=\"data\"><TTChung><PBan>2.0.0</PBan><THDon>HÓA ĐƠN GIÁ TRỊ GIA TĂNG</THDon><KHMSHDon>1</KHMSHDon><KHHDon>C21TBA</KHHDon><SHDon>00000028</SHDon><NLap>2021-11-04</NLap><HDXKhau>0</HDXKhau><HDXKPTQuan>0</HDXKPTQuan><DVTTe>VND</DVTTe><TGia>1</TGia><HTTToan>1</HTTToan><MSTTCGP>0101352495</MSTTCGP></TTChung><NDHDon><NBan><Ten>0101352495</Ten><MST>0101352495</MST><DChi>0101352495</DChi></NBan><NMua><Ten>NGÂN HÀNG THƯƠNG MẠI CỔ PHẦN NGOẠI THƯƠNG VIỆT NAM</Ten><MST>0101352495-998</MST><DChi>198 Trần Quang Khải, Phường Lý Thái Tổ, Quận Hoàn Kiếm, Thành phố Hà Nội</DChi><MKHang>0101352495-998</MKHang><HVTNMHang>NGÂN HÀNG THƯƠNG MẠI CỔ PHẦN NGOẠI THƯƠNG VIỆT NAM</HVTNMHang></NMua><DSHHDVu><HHDVu><TChat>1</TChat><STT>1</STT><MHHDVu>8bd9ea0b</MHHDVu><THHDVu>Hàng hóa 1</THHDVu><DVTinh>dvt</DVTinh><SLuong>1</SLuong><DGia>100000</DGia><TLCKhau>0</TLCKhau><STCKhau>0</STCKhau><ThTien>100000</ThTien><TSuat>10%</TSuat></HHDVu></DSHHDVu><TToan><TgTCThue>100000</TgTCThue><TgTThue>10000</TgTThue><TTCKTMai>0</TTCKTMai><TgTTTBSo>110000</TgTTTBSo><TgTTTBChu>110000</TgTTTBChu><DSLPhi><LPhi><TPhi>0</TPhi></LPhi></DSLPhi><THTTLTSuat><LTSuat><TSuat>10%</TSuat><ThTien>100000</ThTien><TThue>10000</TThue></LTSuat></THTTLTSuat></TToan></NDHDon></DLHDon><DLQRCode>00020101021202142657895426548904141568265489515426280010A0000007750110010700172952045499530370454061100005802VN5905VNPAY6005HANOI62270102280307CTYVNIS0706XYZ00199710010A00000077501100101352495020110306C21TBA04022805082021110406061100006304289E</DLQRCode><DSCKS><NBan><Signature id=\"NBanSignature\" xmlns=\"http://www.w3.org/2000/09/xmldsig#\"><Object><SignatureProperties><SignatureProperty id=\"NBanSignTimeStamp\" Target=\"#NBanSignature\"><SigningTime>2021-11-04T05:47:42</SigningTime></SignatureProperty></SignatureProperties></Object><SignedInfo><CanonicalizationMethod Algorithm=\"http://www.w3.org/TR/2001/REC-xml-c14n-20010315\" /><SignatureMethod Algorithm=\"http://www.w3.org/2000/09/xmldsig#rsa-sha1\" /><Reference URI=\"#data\"><Transforms><Transform Algorithm=\"http://www.w3.org/2000/09/xmldsig#enveloped-signature\" /><Transform Algorithm=\"http://www.w3.org/TR/2001/REC-xml-c14n-20010315\" /></Transforms><DigestMethod Algorithm=\"http://www.w3.org/2000/09/xmldsig#sha1\" /><DigestValue>/tqgZlQMNb9+zkgljMy0DBcodv4=</DigestValue></Reference><Reference URI=\"#NBanSignTimeStamp\" Type=\"https://vninvoice.vn/2021/xmldsig#SignatureProperties\"><Transforms><Transform Algorithm=\"http://www.w3.org/2001/10/xml-exc-c14n#\" /><Transform Algorithm=\"http://www.w3.org/TR/2001/REC-xml-c14n-20010315\" /></Transforms><DigestMethod Algorithm=\"http://www.w3.org/2000/09/xmldsig#sha1\" /><DigestValue>xC7ykb00jdcZ8h10OTvlYHhFkj0=</DigestValue></Reference></SignedInfo><SignatureValue>oD2otIoN+bydK5QEqGZB0IhxEoqY+W0ErzD+7RZ/23wCd2wC3RpHCDANpMqPzr4mLZOH8l2txLQdnODG/tQyue0XNJ/1dwnBpakIpbdNT+oKakllwyQZRvIcIYqjry1sBI6ETXaiIhsbAikObYYxTu0pZ7hdTS6n6zYrPy9kZnJb+ZCtqc9CHxBtB5GA519eOxWrcPzJ7i4UOSi1tR9mNXsd3yxAnH8W+sI8nIrSS24KG2j1MnyYlOReXMpZYXQ83Z7UC4nqTuyfVgEixvSBofGmFOiU1FsFnU0F2O70DWTu/NQMtj+ZxxObPbbB/bTJSuUSJfzcgEm3uEDK0b43tQ==</SignatureValue><KeyInfo><X509Data><X509SubjectName>OU=MST:0101352495, O=CÔNG TY CỔ PHẦN GIẢI PHÁP HÓA ĐƠN ĐIỆN TỬ VIỆT NAM TEST, L=Thanh Xuân, S=Hà Nội, C=VN</X509SubjectName><X509Certificate>MIIEJzCCAxGgAwIBAgIUUjM3KAX/YrC2de4yDLY4d+hpQNYwCwYJKoZIhvcNAQELMIGkMQswCQYDVQQGEwJWTjESMBAGA1UECAwJSMOgIE7hu5lpMRQwEgYDVQQHDAtUaGFuaCBYdcOibjFSMFAGA1UECgxJQ8OUTkcgVFkgQ+G7lCBQSOG6pk4gR0nhuqJJIFBIw4FQIEjDk0EgxJDGoE4gxJBJ4buGTiBU4busIFZJ4buGVCBOQU0gVEVTVDEXMBUGA1UECwwOTVNUOjAxMDEzNTI0OTUwHhcNMjExMDA2MDEzMzE2WhcNNDEwNjIzMDEzMzE2WjCBpDELMAkGA1UEBhMCVk4xEjAQBgNVBAgMCUjDoCBO4buZaTEUMBIGA1UEBwwLVGhhbmggWHXDom4xUjBQBgNVBAoMSUPDlE5HIFRZIEPhu5QgUEjhuqZOIEdJ4bqiSSBQSMOBUCBIw5NBIMSQxqBOIMSQSeG7hk4gVOG7rCBWSeG7hlQgTkFNIFRFU1QxFzAVBgNVBAsMDk1TVDowMTAxMzUyNDk1MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAxleLABM2YJ5P1+8BtySoD+9oxtpGW7xLyPUjZlhioQgwRjlWgkmHDdOY6eyYFTZQSSbu0AT40Ww4oYXF7yw4fmoRNUcIZOA13SJeBGYJ2E3w961elAEUWqLsY4iqlh3JxQF3C4GjAh+4vUQR9IF6Wh/QfWiS0Ej9T6Q1DvhNd6HXIVTUK2v5Ez92KDSHWkjj6lYpiHrmEVyKoYZaTfqKnIWg30N5ZPooenxgfQsEiDJl4ldFS0AngzQyi8EoZjpabfuzvuaFOirfo2IkopGgLAge3/dkN2Hi/V9lQ66utnetmd0ARSvLQ+i5SJLXjMzrX18m9q4+Mo6Nwq1amH0p+QIDAQABo1MwUTAdBgNVHQ4EFgQUQBxyUrl92Wuq9VfXkMUAyGdi+t8wHwYDVR0jBBgwFoAUQBxyUrl92Wuq9VfXkMUAyGdi+t8wDwYDVR0TAQH/BAUwAwEB/zALBgkqhkiG9w0BAQsDggEBAFChE1pVslH4uXATIvxaCrO3Z4TUr/8jJmUjplkH0PeCfN0syhLJ7UtvTZpbO63aUL6jYMSxL2bkcithfDj/g7dVT+AQ32GRcxEQdQjttrRFAqWEI09ZeHXgCQEo+Kxuzs+P+DArwO2+fXmbWoRhRT7F/3u85AZaHlHOfjBqMpktBVuhXTXDkAIxoe3n/wGDKN4VZqv6J1S5syiO/e+ntKt6FoOx02e2EA/uI7OohvR54Ca6AD+aa9e7cw4c6d2cqBwrXqNVJv9bhcd7J4sZehWPNV8bJFuLSUeU+ZQb9OcQYRvmQcFFCpYgOla9BicpWcpLE9ooABYf09kVtMDs4qg=</X509Certificate></X509Data></KeyInfo></Signature></NBan></DSCKS></HDon>"
                            //};

                            if (invoice == null)
                                return;

                            //Kiểm tra XML trc khi ký
                            if (!ValidationBeforeSign(invoice.Xml, out string messageBefore))
                            {
                                Invoke(new MethodInvoker(delegate
                                {
                                    LblResult.Items.Add($"Hóa đơn mẫu số: {invoice.TemplateNo} - ký hiệu: {invoice.SerialNo} - số: {invoice.InvoiceNo} trước khi ký lỗi: {messageBefore}");
                                    LblResult.SelectedIndex = LblResult.Items.Count - 1;
                                }));
                                UnlockInvoice(invoice.Id);
                                return;
                            }

                            //Ký hóa đơn
                            Invoke(new MethodInvoker(delegate
                            {
                                LblResult.Items.Add($"Đang ký hóa đơn mẫu số: {invoice.TemplateNo} - ký hiệu: {invoice.SerialNo} - số: {invoice.InvoiceNo}");
                                LblResult.SelectedIndex = LblResult.Items.Count - 1;
                            }));
                            var xml = SellerSign(invoice.Xml, invoice.IdData, invoice.IdObject, invoice.IdSignature);

                            //Kiểm tra XML sau khi ký
                            if (!ValidationAfterSign(xml, out string messageAfter))
                            {
                                Invoke(new MethodInvoker(delegate
                                {
                                    LblResult.Items.Add($"Hóa đơn mẫu số: {invoice.TemplateNo} - ký hiệu: {invoice.SerialNo} - số: {invoice.InvoiceNo} sau khi ký lỗi: {messageAfter}");
                                    LblResult.SelectedIndex = LblResult.Items.Count - 1;
                                }));
                                UnlockInvoice(invoice.Id);
                                return;
                            }

                            //Lưu hóa đơn đã ký
                            Invoke(new MethodInvoker(delegate
                            {
                                LblResult.Items.Add($"Đang lưu hóa đơn mẫu số: {invoice.TemplateNo} - ký hiệu: {invoice.SerialNo} - số: {invoice.InvoiceNo}");
                                LblResult.SelectedIndex = LblResult.Items.Count - 1;
                            }));

                            PostInvoice(invoice.Id, xml);
                        }
                        #endregion
                    }
                }
                else
                {
                    #region Ký đăng ký sử dụng
                    Invoke(new MethodInvoker(delegate
                    {
                        LblResult.Items.Add("Đang tìm đăng ký sử dụng để ký");
                        LblResult.SelectedIndex = LblResult.Items.Count - 1;
                    }));

                    var registration = GetInvoice();
                    if (registration == null)
                        return;

                    //Ký đăng ký sử dụng
                    Invoke(new MethodInvoker(delegate
                    {
                        LblResult.Items.Add($"Đang ký đăng ký sử dụng Id-{registration.Id}");
                        LblResult.SelectedIndex = LblResult.Items.Count - 1;
                    }));
                    var xml = SellerSign(registration.Xml, registration.IdData, registration.IdObject, registration.IdSignature);

                    //Kiểm tra XML sau khi ký
                    if (!ValidationAfterSign(xml, out string messageAfter))
                    {
                        Invoke(new MethodInvoker(delegate
                        {
                            LblResult.Items.Add($"Đăng ký sử dụng Id-{registration.Id} sau khi ký lỗi: {messageAfter}");
                            LblResult.SelectedIndex = LblResult.Items.Count - 1;
                        }));
                        return;
                    }

                    //Lưu hóa đơn đã ký
                    Invoke(new MethodInvoker(delegate
                    {
                        LblResult.Items.Add($"Đang lưu đăng ký sử dụng Id-{registration.Id}");
                        LblResult.SelectedIndex = LblResult.Items.Count - 1;
                    }));
                    PostInvoice(registration.Id, xml);
                    #endregion
                }
            }

            Invoke(new MethodInvoker(delegate { DgvCertificates.Enabled = true; }));
        }

        private void BgwSigning_ProgressChanged(object sender, ProgressChangedEventArgs e)
        {

        }

        private void BgwSigning_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
        {
            LblResult.Items.Add("Hoàn thành");
            ToggleLoading(true);
        }

        private string GetEndpoint()
        {
            if (_isRegistration)
                return $"{Config.UrlGetXmlRegistration}";
            else
                switch (_type)
                {
                    case (int)TypeActionModel.Invoice01:
                        return $"{Config.UrlGetXmlInvoice01}";
                    case (int)TypeActionModel.Invoice02:
                        return $"{Config.UrlGetXmlInvoice02}";
                    case (int)TypeActionModel.Invoice03:
                        return $"{Config.UrlGetXmlInvoice03}";
                    case (int)TypeActionModel.Invoice04:
                        return $"{Config.UrlGetXmlInvoice04}";
                    case (int)TypeActionModel.Ticket:
                        return $"{Config.UrlGetXmlTicket}";
                    case (int)TypeActionModel.PITDocument:
                        return $"{Config.UrlGetXmlPITDocument}";
                    case (int)TypeActionModel.PITLetter:
                        return $"{Config.UrlGetXmlPITLetter}";
                    case (int)TypeActionModel._52DKSDCT:
                        return $"{Config.UrlGetXmlPITDeductionDocumentDeclaration}";
                    default:
                        MessageBox.Show("Loại hóa đơn không đúng");
                        break;
                }

            return null;
        }

        private string GetEndpointInvoiceError()
        {
            switch (_type)
            {
                case (int)TypeActionModel.Invoice01Error:
                    return $"{Config.UrlGetXmlInvoice01Error}";
                case (int)TypeActionModel.Invoice02Error:
                    return $"{Config.UrlGetXmlInvoice02Error}";
                case (int)TypeActionModel.Invoice03Error:
                    return $"{Config.UrlGetXmlInvoice03Error}";
                case (int)TypeActionModel.Invoice04Error:
                    return $"{Config.UrlGetXmlInvoice04Error}";
                case (int)TypeActionModel.TicketError:
                    return $"{Config.UrlGetXmlInvoice05Error}";
                case (int)TypeActionModel.PITDocumentError:
                    return $"{Config.UrlGetXmlPITDocumentError}";
                case (int)TypeActionModel.InvoiceError32:
                    return $"{Config.UrlGetXmlInvoiceError32}";
                default:
                    MessageBox.Show("Loại hóa đơn không đúng");
                    break;
            }

            return null;
        }

        private string PostEndpoint()
        {
            if (_isRegistration)
                return $"{Config.UrlSaveXmlRegistration}";
            else
                switch (_type)
                {
                    case (int)TypeActionModel.Invoice01:
                        return $"{Config.UrlSaveXmlInvoice01}";
                    case (int)TypeActionModel.Invoice02:
                        return $"{Config.UrlSaveXmlInvoice02}";
                    case (int)TypeActionModel.Invoice03:
                        return $"{Config.UrlSaveXmlInvoice03}";
                    case (int)TypeActionModel.Invoice04:
                        return $"{Config.UrlSaveXmlInvoice04}";
                    case (int)TypeActionModel.Ticket:
                        return $"{Config.UrlSaveXmlTicket}";
                    case (int)TypeActionModel.PITDocument:
                        return $"{Config.UrlSaveXmlPITDocument}";
                    case (int)TypeActionModel.PITLetter:
                        return $"{Config.UrlSaveXmlPITLetter}";
                    case (int)TypeActionModel._52DKSDCT:
                        return $"{Config.UrlSaveXmlPITDeductionDocumentDeclaration}";
                    default:
                        MessageBox.Show("Loại hóa đơn không đúng");
                        break;
                }
            return null;
        }

        private string PostEndpointInvoiceError()
        {
            if (_isRegistration)
                return $"{Config.UrlSaveXmlRegistration}";
            else
                switch (_type)
                {
                    case (int)TypeActionModel.Invoice01Error:
                        return $"{Config.UrlSaveXmlInvoice01Error}";
                    case (int)TypeActionModel.Invoice02Error:
                        return $"{Config.UrlSaveXmlInvoice02Error}";
                    case (int)TypeActionModel.Invoice03Error:
                        return $"{Config.UrlSaveXmlInvoice03Error}";
                    case (int)TypeActionModel.Invoice04Error:
                        return $"{Config.UrlSaveXmlInvoice04Error}";
                    case (int)TypeActionModel.TicketError:
                        return $"{Config.UrlSaveXmlInvoice05Error}";
                    case (int)TypeActionModel.PITDocumentError:
                        return $"{Config.UrlSaveXmlPITDocumentError}";
                    case (int)TypeActionModel.InvoiceError32:
                        return $"{Config.UrlSaveXmlInvoiceError32}";
                    case (int)TypeActionModel._52DKSDCT:
                        return $"{Config.UrlSaveXmlPITDeductionDocumentDeclaration}";
                    default:
                        MessageBox.Show("Loại hóa đơn không đúng");
                        break;
                }
            return null;
        }

        private int GetInvoiceTypeError()
        {
            switch (_type)
            {
                case (int)TypeActionModel.Invoice01Error:
                    return 1;
                case (int)TypeActionModel.Invoice02Error:
                    return 2;
                case (int)TypeActionModel.Invoice03Error:
                    return 3;
                case (int)TypeActionModel.Invoice04Error:
                    return 4;
                case (int)TypeActionModel.TicketError:
                    return 5;
                case (int)TypeActionModel.InvoiceError32:
                    return 11; // [Display(ShortName = "11", Name = "Hóa đơn ngoài hệ thống")] - _11HĐNHT = 11
                case (int)TypeActionModel.PITDocumentError:
                    return 6;
                default:
                    MessageBox.Show("Loại hóa đơn không đúng");
                    return 0;
            }
        }

        private SignModel GetInvoice()
        {
            var client = new WebClient();
            client.BaseAddress = Config.Endpoint;
            client.Encoding = Encoding.UTF8;
            HttpClientService.IgnoreBadCertificates();
            client.Headers.Add("Authorization", $"{Config.Token.TokenType ?? "Bearer"} {Config.Token.AccessToken}");
            client.Headers.Add(HttpRequestHeader.ContentType, "application/json");

            try
            {
                var response = client.DownloadString($"{GetEndpoint()}?serialNumber={_certificate.SerialNumber}&subjectName={HttpUtility.UrlEncode(_certificate.SubjectName)}");
                var model = JsonConvert.DeserializeObject<SignModel>(response);
                //model.Xml =
                //    "<TKhai><DLTKhai Id=\"Id-1\"><TTChung><PBan>2.0.0</PBan><MSo>01/ĐKTĐ-HĐĐT</MSo><Ten>Tờ khai đăng ký/thay đổi thông tin sử dụng HĐĐT theo quy định tại Nghị định 123/2020/NĐ-CP</Ten><HThuc>2</HThuc><TNNT>0101352495</TNNT><MST>0101352495</MST><CQTQLy>Cục thuế Hà Nội</CQTQLy><MCQTQLy>HNA</MCQTQLy><NLHe>Hường</NLHe><DCLHe>0101352495</DCLHe><DCTDTu><EMAIL></DCTDTu><DTLHe>0987654321</DTLHe><DDanh>Hà Nội</DDanh><NLap>2021-11-05</NLap></TTChung><NDTKhai><HTHDon><CMa>0</CMa><KCMa>1</KCMa></HTHDon><HTGDLHDDT><NNTDBKKhan>0</NNTDBKKhan><NNTKTDNUBND>0</NNTKTDNUBND><CDLTTDCQT>0</CDLTTDCQT><CDLQTVAN>1</CDLQTVAN></HTGDLHDDT><PThuc><CDDu>0</CDDu><CBTHop>1</CBTHop></PThuc><LHDSDung><HDGTGT>1</HDGTGT><HDBHang>1</HDBHang><HDBTSCong>0</HDBTSCong><HDBHDTQGia>0</HDBHDTQGia><HDKhac>1</HDKhac><CTu>1</CTu></LHDSDung><DSCTSSDung><CTS><STT>1</STT><TTChuc>OU=MST:0101352495, O=CÔNG TY CỔ PHẦN GIẢI PHÁP HÓA ĐƠN ĐIỆN TỬ VIỆT NAM TEST, L=Thanh Xuân, S=Hà Nội, C=VN</TTChuc><Seri>5233372805FF62B0B675EE320CB63877E86940D6</Seri><TNgay>2021-10-06T00:00:00</TNgay><DNgay>2041-06-23T00:00:00</DNgay><HThuc>1</HThuc></CTS></DSCTSSDung></NDTKhai></DLTKhai><DSCKS><NNT><Signature Id=\"NNT-1\" xmlns=\"http://www.w3.org/2000/09/xmldsig#\"><Object Id=\"SigningTime-8CB85A94ECAE894083427E9CC31CB525\"><SignatureProperties><SignatureProperty Target=\"signatureProperties\"><SigningTime>2021-12-01T11:52:36</SigningTime></SignatureProperty></SignatureProperties></Object></Signature></NNT></DSCKS></TKhai>";
                //model.IdData = "Id-1";
                //model.IdObject = "Id-1";
                //model.IdSignature = "Id-1";
                return model;
            }
            catch (WebException ex)
            {
                var responseBody = new StreamReader(ex.Response.GetResponseStream()).ReadToEnd();
                if (((HttpWebResponse)ex.Response).StatusCode == HttpStatusCode.NotFound)
                    MessageBox.Show("Không còn hóa đơn để ký");
                else
                {
                    MessageBox.Show($"{responseBody}");
                    _logger.Error(ex, ex.Message, ex.StackTrace);
                    _logger.Error(responseBody);
                }
            }
            return null;
        }

        private List<SignInvoiceErrorModel> GetInvoicesError()
        {
            var client = new WebClient();
            client.BaseAddress = Config.Endpoint;
            client.Encoding = Encoding.UTF8;
            HttpClientService.IgnoreBadCertificates();
            client.Headers.Add("Authorization", $"{Config.Token.TokenType ?? "Bearer"} {Config.Token.AccessToken}");
            client.Headers.Add(HttpRequestHeader.ContentType, "application/json");

            try
            {
                var response = client.DownloadString($"{GetEndpointInvoiceError()}?serialNumber={_certificate.SerialNumber}&subjectName={HttpUtility.UrlEncode(_certificate.SubjectName)}");
                return JsonConvert.DeserializeObject<List<SignInvoiceErrorModel>>(response);
            }
            catch (WebException ex)
            {
                var responseBody = new StreamReader(ex.Response.GetResponseStream()).ReadToEnd();
                if (((HttpWebResponse)ex.Response).StatusCode == HttpStatusCode.NotFound)
                    MessageBox.Show("Không còn hóa đơn để ký");
                else
                {
                    MessageBox.Show($"{responseBody}");
                    _logger.Error(ex, ex.Message, ex.StackTrace);
                    _logger.Error(responseBody);
                }
            }

            return null;
        }

        private bool ValidationInvoiceErrorBeforeSign(string xml, out string message)
        {
            XmlDocument doc = new XmlDocument();
            doc.LoadXml(xml);

            var nBanNode = doc.SelectNodes(@"//TBao/DLTBao");
            if (nBanNode == null || nBanNode.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ thông tin người bán: TBao/DLTBao";
                return false;
            }

            var tags = doc.SelectNodes(@"//TBao/DLTBao/MST");
            if (tags.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ thông tin mã số thuế người bán: TBao/DLTBao/MST";
                return false;
            }

            var sellerTaxCode = tags[0].InnerText;

            tags = doc.SelectNodes(@"//DSHDon/HDon/SHDon");
            if (tags.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ thông tin số hóa đơn: DSHDon/HDon/SHDon";
                return false;
            }
            var invoiceNo = tags[0].InnerText;

            tags = doc.SelectNodes(@"//DSHDon/HDon/KHMSHDon");
            if (tags.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ DSHDon/HDon/KHMSHDon";
                return false;
            }
            var templateNo = tags[0].InnerText;

            tags = doc.SelectNodes(@"//DSHDon/HDon/KHHDon");
            if (tags.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ DSHDon/HDon/KHHDon";
                return false;
            }
            var serialNo = tags[0].InnerText;

            if (_certificate.SubjectName.IndexOf(sellerTaxCode, StringComparison.Ordinal) < 0)
            {
                message = $"Mã số thuế người bán trong USB với hóa đơn {templateNo}{serialNo}-{invoiceNo} không giống nhau";
                return false;
            }

            //check có chữ ký người bán chưa
            tags = doc.SelectNodes(@"//DSCKS/NNT/Signature/SignedInfo");
            if (tags.Count != 0)
            {
                message = "Dữ liệu đã chứa thẻ DSCKS/NNT/Signature/SignedInfo";
                return false;
            }

            message = null;
            return true;
        }

        private bool ValidationPITErrorBeforeSign(string xml, out string message)
        {
            XmlDocument doc = new XmlDocument();
            doc.LoadXml(xml);

            var nBanNode = doc.SelectNodes(@"//TBao/DLTBao");
            if (nBanNode == null || nBanNode.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ thông tin người bán: TBao/DLTBao";
                return false;
            }

            var tags = doc.SelectNodes(@"//TBao/DLTBao/MST");
            if (tags.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ thông tin mã số thuế người bán: TBao/DLTBao/MST";
                return false;
            }

            var sellerTaxCode = tags[0].InnerText;

            tags = doc.SelectNodes(@"//DSCTu/CTu/SCTu");
            if (tags.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ thông tin số chứng từ: DSCTu/CTu/SCTu";
                return false;
            }
            var invoiceNo = tags[0].InnerText;

            tags = doc.SelectNodes(@"//DSCTu/CTu/KHMSCTu");
            if (tags.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ DSCTu/CTu/KHMSCTu";
                return false;
            }
            var templateNo = tags[0].InnerText;

            tags = doc.SelectNodes(@"//DSCTu/CTu/KHCTu");
            if (tags.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ DSCTu/CTu/KHCTu";
                return false;
            }
            var serialNo = tags[0].InnerText;

            if (_certificate.SubjectName.IndexOf(sellerTaxCode, StringComparison.Ordinal) < 0)
            {
                message = $"Mã số thuế người bán trong USB với hóa đơn {templateNo}{serialNo}-{invoiceNo} không giống nhau";
                return false;
            }

            //check có chữ ký người bán chưa
            tags = doc.SelectNodes(@"//DSCKS/NNT/Signature/SignedInfo");
            if (tags.Count != 0)
            {
                message = "Dữ liệu đã chứa thẻ DSCKS/NNT/Signature/SignedInfo";
                return false;
            }

            message = null;
            return true;
        }

        private bool ValidationBeforeSign(string xml, out string message)
        {
            XmlDocument doc = new XmlDocument();
            doc.LoadXml(xml);

            var nBanNode = doc.SelectNodes(@"//NDHDon/NBan");
            if (nBanNode == null || nBanNode.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ thông tin người bán: NDHDon/NBan";
                return false;
            }

            var tags = doc.SelectNodes(@"//NDHDon/NBan/MST");
            if (tags.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ thông tin mã số thuế người bán: NDHDon/NBan/MST";
                return false;
            }

            var sellerTaxCode = tags[0].InnerText;

            tags = doc.SelectNodes(@"//DLHDon/TTChung/SHDon");
            if (tags.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ thông tin số hóa đơn: DLHDon/TTChung/SHDon";
                return false;
            }
            var invoiceNo = tags[0].InnerText;

            tags = doc.SelectNodes(@"//DLHDon/TTChung/KHMSHDon");
            if (tags.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ DLHDon/TTChung/KHMSHDon";
                return false;
            }
            var templateNo = tags[0].InnerText;

            tags = doc.SelectNodes(@"//DLHDon/TTChung/KHHDon");
            if (tags.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ DLHDon/TTChung/KHHDon";
                return false;
            }
            var serialNo = tags[0].InnerText;

            if (_certificate.SubjectName.IndexOf(sellerTaxCode, StringComparison.Ordinal) < 0)
            {
                message = $"Mã số thuế người bán trong USB với hóa đơn {templateNo}{serialNo}-{invoiceNo} không giống nhau";
                return false;
            }

            //check có chữ ký người bán chưa
            tags = doc.SelectNodes(@"//DSCKS/NBan/Signature/SignedInfo");
            if (tags.Count != 0)
            {
                message = "Dữ liệu đã chứa thẻ DSCKS/NBan/Signature/SignedInfo";
                return false;
            }

            message = null;
            return true;
        }

        private bool ValidationPITDocumentBeforeSign(string xml, out string message)
        {
            XmlDocument doc = new XmlDocument();
            doc.LoadXml(xml);

            var nBanNode = doc.SelectNodes(@"//NDCTu/TCTTNhap");
            if (nBanNode == null || nBanNode.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ thông tin tổ chức trả thu nhập, người nộp thuế, thông tin thuế thu nhập cá nhân khấu trừ: NDCTu/TCTTNhap";
                return false;
            }

            var tags = doc.SelectNodes(@"//NDCTu/TCTTNhap/MST");
            if (tags.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ thông tin mã số thuế tổ chức trả thu nhập, người nộp thuế, thông tin thuế thu nhập cá nhân khấu trừ: NDCTu/TCTTNhap/MST";
                return false;
            }

            var sellerTaxCode = tags[0].InnerText;

            tags = doc.SelectNodes(@"//DLCTu/TTChung/SCTu");
            if (tags.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ thông tin số chứng từ: DLCTu/TTChung/SCTu";
                return false;
            }
            var invoiceNo = tags[0].InnerText;

            tags = doc.SelectNodes(@"//DLCTu/TTChung/MSCTu");
            if (tags.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ DLCTu/TTChung/MSCTu";
                return false;
            }
            var templateNo = tags[0].InnerText;

            tags = doc.SelectNodes(@"//DLCTu/TTChung/KHCTu");
            if (tags.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ DLCTu/TTChung/KHCTu";
                return false;
            }

            var serialNo = tags[0].InnerText;

            if (_certificate.SubjectName.IndexOf(sellerTaxCode, StringComparison.Ordinal) < 0)
            {
                message = $"Mã số thuế tổ chức trả thu nhập, người nộp thuế, thông tin thuế thu nhập cá nhân khấu trừ trong USB với chứng từ {templateNo}{serialNo}-{invoiceNo} không giống nhau";
                return false;
            }

            //check có chữ ký người bán chưa
            tags = doc.SelectNodes(@"//DSCKS/TCTTNhap/Signature/SignedInfo");
            if (tags.Count != 0)
            {
                message = "Dữ liệu đã chứa thẻ DSCKS/TCTTNhap/Signature/SignedInfo";
                return false;
            }

            message = null;
            return true;
        }

        private bool ValidationPITLetterBeforeSign(string xml, out string message)
        {
            XmlDocument doc = new XmlDocument();
            doc.LoadXml(xml);

            var tags = doc.SelectNodes(@"//DLTXNTNhap/TTChung/PBan");
            if (tags.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ thông tin phiên bản XML: DLTXNTNhap/TTChung/PBan";
                return false;
            }

            tags = doc.SelectNodes(@"//DLTXNTNhap/TTChung/THDon");
            if (tags.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ tên chứng từ: DLTXNTNhap/TTChung/THDon";
                return false;
            }

            tags = doc.SelectNodes(@"//DLTXNTNhap/TTChung/MSTXNTNhap");
            if (tags.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ mẫu số chứng từ: DLTXNTNhap/TTChung/MSTXNTNhap";
                return false;
            }

            tags = doc.SelectNodes(@"//DLTXNTNhap/TTChung/NLap");
            if (tags.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ ngày lập: DLTXNTNhap/TTChung/NLap";
                return false;
            }

            tags = doc.SelectNodes(@"//DLTXNTNhap/NDTXNTNhap/TCTTNhap/Ten");
            if (tags.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ tên đơn vị, tổ chức chi trả thu nhập: DLTXNTNhap/NDTXNTNhap/TCTTNhap/Ten";
                return false;
            }

            tags = doc.SelectNodes(@"//DLTXNTNhap/NDTXNTNhap/TCTTNhap/MST");
            if (tags.Count != 0)
            {
                var sellerTaxCode = tags[0].InnerText;
                if (_certificate.SubjectName.IndexOf(sellerTaxCode, StringComparison.Ordinal) < 0)
                {
                    message = $"Mã số thuế tổ chức trả thu nhập, người nộp thuế, thông tin thuế thu nhập cá nhân khấu trừ trong USB với thư xác nhận thu nhập không giống nhau";
                    return false;
                }
            }

            tags = doc.SelectNodes(@"//DLTXNTNhap/NDTXNTNhap/NNT/Ten");
            if (tags.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ tên người nộp thuế: DLTXNTNhap/NDTXNTNhap/NNT/Ten";
                return false;
            }

            tags = doc.SelectNodes(@"//DLTXNTNhap/NDTXNTNhap/TTTNhap/Nam");
            if (tags.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ năm trả thu nhập: DLTXNTNhap/NDTXNTNhap/TTTNhap/Nam";
                return false;
            }

            tags = doc.SelectNodes(@"//DLTXNTNhap/NDTXNTNhap/TTTNhap/TNgay");
            if (tags.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ từ ngày trả thu nhập: DLTXNTNhap/NDTXNTNhap/TTTNhap/TNgay";
                return false;
            }

            tags = doc.SelectNodes(@"//DLTXNTNhap/NDTXNTNhap/TTTNhap/DNgay");
            if (tags.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ đến ngày trả thu nhập: DLTXNTNhap/NDTXNTNhap/TTTNhap/DNgay";
                return false;
            }

            tags = doc.SelectNodes(@"//DLTXNTNhap/NDTXNTNhap/TTTNhap/TTNhap");
            if (tags.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ tổng thu nhập (số tiền thu nhập trong năm): DLTXNTNhap/NDTXNTNhap/TTTNhap/TTNhap";
                return false;
            }

            tags = doc.SelectNodes(@"//DLTXNTNhap/NDTXNTNhap/TTTNhap/STTNTVNam");
            if (tags.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ số tiền thu nhập tại Việt Nam: DLTXNTNhap/NDTXNTNhap/TTTNhap/STTNTVNam";
                return false;
            }

            tags = doc.SelectNodes(@"//DLTXNTNhap/NDTXNTNhap/TTTNhap/STTNTNNgoai");
            if (tags.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ số tiền thu nhập tại nước ngoài: DLTXNTNhap/NDTXNTNhap/TTTNhap/STTNTNNgoai";
                return false;
            }

            tags = doc.SelectNodes(@"//DLTXNTNhap/NDTXNTNhap/TTTNhap/TSTKTru");
            if (tags.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ tổng số tiền khấu trừ ngoài Việt Nam: DLTXNTNhap/NDTXNTNhap/TTTNhap/TSTKTru";
                return false;
            }

            tags = doc.SelectNodes(@"//DLTXNTNhap/NDTXNTNhap/TTTNhap/STTNCNhan");
            if (tags.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ số thuế TNCN (ngoài Việt Nam): DLTXNTNhap/NDTXNTNhap/TTTNhap/STTNCNhan";
                return false;
            }

            tags = doc.SelectNodes(@"//DLTXNTNhap/NDTXNTNhap/TTTNhap/STBHXHoi");
            if (tags.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ số tiền BHXH (ngoài Việt Nam): DLTXNTNhap/NDTXNTNhap/TTTNhap/STBHXHoi";
                return false;
            }

            tags = doc.SelectNodes(@"//DLTXNTNhap/NDTXNTNhap/TTTNhap/STKTKhac");
            if (tags.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ số tiền khấu trừ khác (ngoài Việt Nam): DLTXNTNhap/NDTXNTNhap/TTTNhap/STKTKhac";
                return false;
            }

            tags = doc.SelectNodes(@"//DLTXNTNhap/NDTXNTNhap/TTTNhap/STNha");
            if (tags.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ số tiền thuê nhà: DLTXNTNhap/NDTXNTNhap/TTTNhap/STNha";
                return false;
            }

            //check có chữ ký người bán chưa
            tags = doc.SelectNodes(@"//DSCKS/TCTTNhap/Signature/SignedInfo");
            if (tags.Count != 0)
            {
                message = "Dữ liệu đã chứa thẻ DSCKS/TCTTNhap/Signature/SignedInfo";
                return false;
            }

            message = null;
            return true;
        }

        private bool ValidationAfterSign(string xml, out string message)
        {
            XmlDocument doc = new XmlDocument();
            doc.LoadXml(xml);

            var tags = doc.GetElementsByTagName("Signature");
            if (tags.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ <Signature>";
                return false;
            }

            var isValid = false;
            for (int i = 0; i < tags.Count; i++)
            {
                for (int j = 0; j < tags[i].Attributes.Count; j++)
                {
                    var attr = tags[i].Attributes[j];
                    if (attr.Name == "Id")
                        isValid = true;
                    else
                        continue;
                }
            }

            message = null;
            return isValid;
        }

        private string SellerSign(string xml, string idData, string idObject, string idSignature)
        {
            // Create a new XML document.
            XmlDocument document = new XmlDocument();

            // Load an XML file into the XmlDocument object.
            document.PreserveWhitespace = true;
            document.LoadXml(xml);

            // Sign the XML document. 
            if (!_isRegistration)
                if (_type == (int)TypeActionModel._52DKSDCT)
                {
                    AddRegistrationSellerSignature(document, _certificate.X509, idData, idObject, idSignature);
                }
                else
                {
                    AddInvoiceSellerSignature(document, _certificate.X509, idData, idObject, idSignature);
                }
            else
                AddRegistrationSellerSignature(document, _certificate.X509, idData, idObject, idSignature);

            return document.OuterXml;
        }

        private string SellerSignInvoiceError(string xml, string idData, string idObject, string idSignature)
        {
            // Create a new XML document.
            XmlDocument document = new XmlDocument();

            // Load an XML file into the XmlDocument object.
            document.PreserveWhitespace = true;
            document.LoadXml(xml);

            var signedXml = new SignedXml(document);
            signedXml.Signature.Id = idSignature;
            signedXml.SigningKey = _certificate.X509.PrivateKey;

            var keyInfo = new KeyInfo();

            var keyInfoData = new KeyInfoX509Data(_certificate.X509);
            keyInfoData.AddSubjectName(_certificate.X509.SubjectName.Name);
            keyInfo.AddClause(keyInfoData);

            signedXml.KeyInfo = keyInfo;

            // reference SigningTime
            var referenceSigningTime = new Reference { Uri = $"#{idObject}" };
            signedXml.AddReference(referenceSigningTime);
            signedXml.ComputeSignature();

            // reference data
            var referenceData = new Reference { Uri = $"#{idData}" };
            signedXml.AddReference(referenceData);
            signedXml.ComputeSignature();

            // Append the element to the XML document.
            var xmlDigitalSignature = signedXml.GetXml();
            var childNodes = xmlDigitalSignature.ChildNodes;
            var element = document.SelectSingleNode($"//*[@Id='{idSignature}']");


            if (element != null)
            {
                for (int i = (childNodes.Count - 1); i >= 0; i--)
                {
                    element.PrependChild(document.ImportNode(childNodes[i], true));
                }
            }
            else
            {
                var tagDsck = document.SelectSingleNode(@"//DSCKS");
                if (tagDsck != null)
                {
                    var nBan = document.CreateElement("NBan");
                    nBan.AppendChild(document.ImportNode(xmlDigitalSignature, true));
                    tagDsck.AppendChild(document.ImportNode(nBan, true));

                    var el = document.SelectSingleNode(@"//HDon");
                    el.AppendChild(document.ImportNode(tagDsck, true));
                }
                else
                {
                    var dscks = document.CreateElement("DSCKS");
                    var nBan = document.CreateElement("NBan");
                    nBan.AppendChild(document.ImportNode(xmlDigitalSignature, true));
                    dscks.AppendChild(document.ImportNode(nBan, true));

                    var el = document.SelectSingleNode(@"//HDon");
                    el.AppendChild(document.ImportNode(dscks, true));
                }
            }

            return document.OuterXml;
        }

        //private static XmlDocument AddNBanElement(XmlDocument document)
        //{
        //    //var obj = new ObjectSignatureModel
        //    //{
        //    //    SignatureProperties = new ObjectSignatureModel.SignaturePropertiesModel
        //    //    {
        //    //        SignatureProperty = new ObjectSignatureModel.SignaturePropertyModel
        //    //        {
        //    //            Id = "NMuaSignTimeStamp",
        //    //            Target = "#NMuaSignature",
        //    //            SigningTime = DateTime.Now.ToString("yyyy-MM-ddThh:mm:ss")
        //    //        }
        //    //    }
        //    //};

        //    //string objXml = XmlExtension.XmlSerialize(XmlExtension.ObjToXml(obj));

        //    ////bỏ ký tự <xml ../> ở đầu
        //    //var indexTKhai = objXml.IndexOf("<Object>");
        //    //objXml = objXml.Substring(indexTKhai, objXml.Length - indexTKhai);

        //    string objXml = $@"<Object><SignatureProperties><SignatureProperty id =""NBanSignTimeStamp"" Target =""#NBSignature""><SigningTime>{DateTime.Now:yyyy-MM-ddThh:mm:ss}</SigningTime></SignatureProperty></SignatureProperties></Object>";


        //    var element = document.SelectSingleNode(@"//DSCKS/NBan");
        //    if (element != null)
        //    {
        //        element.InnerXml = objXml;
        //    }
        //    else
        //    {
        //        var tagDsck = document.SelectSingleNode(@"//DSCKS");
        //        if (tagDsck != null)
        //        {
        //            var nMuaNode = document.CreateElement("NMua");
        //            nMuaNode.InnerXml = objXml;
        //            tagDsck.AppendChild(nMuaNode);

        //            var hdon = document.SelectSingleNode(@"//HDon");
        //            document.ImportNode(tagDsck, true);
        //        }
        //        else
        //        {
        //            var dscks = document.CreateElement("DSCKS");
        //            var nMua = document.CreateElement("NMua");
        //            nMua.InnerXml = objXml;
        //            dscks.AppendChild(document.ImportNode(nMua, true));

        //            var hdon = document.SelectSingleNode(@"//HDon");
        //            document.ImportNode(dscks, true);
        //        }
        //    }

        //    return document;
        //}

        private void AddInvoiceSellerSignature(XmlDocument document, X509Certificate2 certificate, string idData, string idObject, string idSignature)
        {
            var signedXml = new SignedXml(document);
            signedXml.Signature.Id = idSignature;
            signedXml.SigningKey = certificate.PrivateKey;

            var keyInfo = new KeyInfo();

            var keyInfoData = new KeyInfoX509Data(certificate);
            keyInfoData.AddSubjectName(certificate.SubjectName.Name);
            keyInfo.AddClause(keyInfoData);

            signedXml.KeyInfo = keyInfo;

            //reference SigningTime
            var referenceSigningTime = new Reference { Uri = $"#{idObject}" };
            //var envSingingTime = new XmlDsigEnvelopedSignatureTransform();
            //referenceSigningTime.AddTransform(envSingingTime);
            //var env1SigningTime = new XmlDsigC14NTransform();
            //referenceSigningTime.AddTransform(env1SigningTime);

            signedXml.AddReference(referenceSigningTime);
            signedXml.ComputeSignature();


            //reference data
            var referenceData = new Reference { Uri = $"#{idData}" };
            //var env = new XmlDsigEnvelopedSignatureTransform();
            //referenceData.AddTransform(env);
            //var env1 = new XmlDsigC14NTransform();
            //referenceData.AddTransform(env1);

            signedXml.AddReference(referenceData);
            signedXml.ComputeSignature();


            //// Append the element to the XML document.
            var xmlDigitalSignature = signedXml.GetXml();
            var childNodes = xmlDigitalSignature.ChildNodes;
            //var element = document.SelectSingleNode(@"//DSCKS/NBan");
            var element = document.SelectSingleNode($"//*[@Id='{idSignature}']");


            if (element != null)
            {
                for (int i = (childNodes.Count - 1); i >= 0; i--)
                {
                    element.PrependChild(document.ImportNode(childNodes[i], true));
                }
                //element.AppendChild(document.ImportNode(xmlDigitalSignature, true));
            }
            else
            {
                var tagDsck = document.SelectSingleNode(@"//DSCKS");
                if (tagDsck != null)
                {
                    var nBan = document.CreateElement("NBan");
                    nBan.AppendChild(document.ImportNode(xmlDigitalSignature, true));
                    tagDsck.AppendChild(document.ImportNode(nBan, true));

                    var el = document.SelectSingleNode(@"//HDon");
                    el.AppendChild(document.ImportNode(tagDsck, true));
                }
                else
                {
                    var dscks = document.CreateElement("DSCKS");
                    var nBan = document.CreateElement("NBan");
                    nBan.AppendChild(document.ImportNode(xmlDigitalSignature, true));
                    dscks.AppendChild(document.ImportNode(nBan, true));

                    var el = document.SelectSingleNode(@"//HDon");
                    el.AppendChild(document.ImportNode(dscks, true));
                }
            }
        }


        private void AddRegistrationSellerSignature(XmlDocument document, X509Certificate2 certificate, string idData, string idObject, string idSignature)
        {
            var signedXml = new SignedXml(document);
            signedXml.Signature.Id = idData;
            signedXml.SigningKey = certificate.PrivateKey;

            var keyInfo = new KeyInfo();

            var keyInfoData = new KeyInfoX509Data(certificate);
            keyInfoData.AddSubjectName(certificate.SubjectName.Name);
            keyInfo.AddClause(keyInfoData);

            signedXml.KeyInfo = keyInfo;

            //reference SigningTime
            var referenceSigningTime = new Reference { Uri = $"#{idObject}" };
            //var envSingingTime = new XmlDsigEnvelopedSignatureTransform();
            ////referenceSigningTime.AddTransform(envSingingTime);
            //var env1SigningTime = new XmlDsigC14NTransform();
            //referenceSigningTime.AddTransform(env1SigningTime);

            signedXml.AddReference(referenceSigningTime);
            signedXml.ComputeSignature();


            //reference data
            var referenceData = new Reference { Uri = $"#{idData}" };
            //var env = new XmlDsigEnvelopedSignatureTransform();
            //referenceData.AddTransform(env);
            //var env1 = new XmlDsigC14NTransform();
            //referenceData.AddTransform(env1);

            signedXml.AddReference(referenceData);
            signedXml.ComputeSignature();


            //// Append the element to the XML document.

            var xmlDigitalSignature = signedXml.GetXml();
            //var element = document.SelectSingleNode(@"//DSCKS/NNT");
            //if (element != null)
            //{
            //    element.AppendChild(document.ImportNode(xmlDigitalSignature, true));
            //}
            var childNodes = xmlDigitalSignature.ChildNodes;

            //var element = document.SelectSingleNode(@"//DSCKS/NBan");
            var element = document.SelectSingleNode($"//*[@Id='{idSignature}']");
            if (element != null)
            {
                for (int i = (childNodes.Count - 1); i >= 0; i--)
                {
                    element.PrependChild(document.ImportNode(childNodes[i], true));
                }

                //foreach (var childNode in childNodes)
                //{
                //    var itemdata = (XmlNode)childNode;
                //    element.PrependChild(document.ImportNode(itemdata, true));
                //}
                //element.AppendChild(document.ImportNode(xmlDigitalSignature, true));
            }
            else
            {
                var tagDsck = document.SelectSingleNode(@"//DSCKS");
                if (tagDsck != null)
                {
                    var nBan = document.CreateElement("NNT");
                    nBan.AppendChild(document.ImportNode(xmlDigitalSignature, true));
                    tagDsck.AppendChild(document.ImportNode(nBan, true));

                    //var el = document.SelectSingleNode(@"//HDon");
                    //el.AppendChild(document.ImportNode(tagDsck, true));
                }
                else
                {
                    var dscks = document.CreateElement("DSCKS");
                    var nBan = document.CreateElement("NNT");
                    nBan.AppendChild(document.ImportNode(xmlDigitalSignature, true));
                    dscks.AppendChild(document.ImportNode(nBan, true));

                    //var el = document.SelectSingleNode(@"//HDon");
                    //el.AppendChild(document.ImportNode(dscks, true));
                }
            }
        }


        #region sign pit document


        private string SellerSignPITDocument(string xml, string idData, string idObject, string idSignature)
        {
            // Create a new XML document.
            XmlDocument document = new XmlDocument();

            // Load an XML file into the XmlDocument object.
            document.PreserveWhitespace = true;
            document.LoadXml(xml);

            // Sign the XML document. 
            AddPITDocumentSellerSignature(document, _certificate.X509, idData, idObject, idSignature);

            return document.OuterXml;
        }

        private void AddPITDocumentSellerSignature(XmlDocument document, X509Certificate2 certificate, string idData, string idObject, string idSignature)
        {
            var signedXml = new SignedXml(document);
            signedXml.Signature.Id = idSignature;
            signedXml.SigningKey = certificate.PrivateKey;

            var keyInfo = new KeyInfo();

            var keyInfoData = new KeyInfoX509Data(certificate);
            keyInfoData.AddSubjectName(certificate.SubjectName.Name);
            keyInfo.AddClause(keyInfoData);

            signedXml.KeyInfo = keyInfo;

            //reference SigningTime
            var referenceSigningTime = new Reference { Uri = $"#{idObject}" };

            signedXml.AddReference(referenceSigningTime);
            signedXml.ComputeSignature();


            //reference data
            var referenceData = new Reference { Uri = $"#{idData}" };

            signedXml.AddReference(referenceData);
            signedXml.ComputeSignature();


            //// Append the element to the XML document.
            var xmlDigitalSignature = signedXml.GetXml();
            var childNodes = xmlDigitalSignature.ChildNodes;
            var element = document.SelectSingleNode($"//*[@Id='{idSignature}']");


            if (element != null)
            {
                for (int i = (childNodes.Count - 1); i >= 0; i--)
                {
                    element.PrependChild(document.ImportNode(childNodes[i], true));
                }
            }
            else
            {
                var tagDsck = document.SelectSingleNode(@"//DSCKS");
                if (tagDsck != null)
                {
                    var nBan = document.CreateElement("TCTTNhap");
                    nBan.AppendChild(document.ImportNode(xmlDigitalSignature, true));
                    tagDsck.AppendChild(document.ImportNode(nBan, true));

                    var el = document.SelectSingleNode(@"//CTu");
                    el.AppendChild(document.ImportNode(tagDsck, true));
                }
                else
                {
                    var dscks = document.CreateElement("DSCKS");
                    var nBan = document.CreateElement("TCTTNhap");
                    nBan.AppendChild(document.ImportNode(xmlDigitalSignature, true));
                    dscks.AppendChild(document.ImportNode(nBan, true));

                    var el = document.SelectSingleNode(@"//CTu");
                    el.AppendChild(document.ImportNode(dscks, true));
                }
            }
        }

        #endregion

        #region sign pit letter


        private string SellerSignPITLetter(string xml, string idData, string idObject, string idSignature)
        {
            // Create a new XML document.
            XmlDocument document = new XmlDocument();

            // Load an XML file into the XmlDocument object.
            document.PreserveWhitespace = true;
            document.LoadXml(xml);

            // Sign the XML document. 
            AddPITLetterSellerSignature(document, _certificate.X509, idData, idObject, idSignature);

            return document.OuterXml;
        }

        private void AddPITLetterSellerSignature(XmlDocument document, X509Certificate2 certificate, string idData, string idObject, string idSignature)
        {
            var signedXml = new SignedXml(document);
            signedXml.Signature.Id = idSignature;
            signedXml.SigningKey = certificate.PrivateKey;

            var keyInfo = new KeyInfo();

            var keyInfoData = new KeyInfoX509Data(certificate);
            keyInfoData.AddSubjectName(certificate.SubjectName.Name);
            keyInfo.AddClause(keyInfoData);

            signedXml.KeyInfo = keyInfo;

            //reference SigningTime
            var referenceSigningTime = new Reference { Uri = $"#{idObject}" };

            signedXml.AddReference(referenceSigningTime);
            signedXml.ComputeSignature();


            //reference data
            var referenceData = new Reference { Uri = $"#{idData}" };

            signedXml.AddReference(referenceData);
            signedXml.ComputeSignature();


            //// Append the element to the XML document.
            var xmlDigitalSignature = signedXml.GetXml();
            var childNodes = xmlDigitalSignature.ChildNodes;
            var element = document.SelectSingleNode($"//*[@Id='{idSignature}']");


            if (element != null)
            {
                for (int i = (childNodes.Count - 1); i >= 0; i--)
                {
                    element.PrependChild(document.ImportNode(childNodes[i], true));
                }
            }
            else
            {
                var tagDsck = document.SelectSingleNode(@"//DSCKS");
                if (tagDsck != null)
                {
                    var nBan = document.CreateElement("TCTTNhap");
                    nBan.AppendChild(document.ImportNode(xmlDigitalSignature, true));
                    tagDsck.AppendChild(document.ImportNode(nBan, true));

                    var el = document.SelectSingleNode(@"//TXNTNhap");
                    el.AppendChild(document.ImportNode(tagDsck, true));
                }
                else
                {
                    var dscks = document.CreateElement("DSCKS");
                    var nBan = document.CreateElement("TCTTNhap");
                    nBan.AppendChild(document.ImportNode(xmlDigitalSignature, true));
                    dscks.AppendChild(document.ImportNode(nBan, true));

                    var el = document.SelectSingleNode(@"//TXNTNhap");
                    el.AppendChild(document.ImportNode(dscks, true));
                }
            }
        }

        #endregion

        private void PostInvoice(long id, string xml)
        {
            try
            {
                var client = new WebClient();
                client.BaseAddress = Config.Endpoint;
                client.Encoding = Encoding.UTF8;
                HttpClientService.IgnoreBadCertificates();
                client.Headers.Add("Authorization", $"{Config.Token.TokenType ?? "Bearer"} {Config.Token.AccessToken}");
                client.Headers.Add(HttpRequestHeader.ContentType, "application/json");

                var response = client.UploadString(PostEndpoint(), JsonConvert.SerializeObject(new
                {
                    Id = id,
                    Xml = xml,
                    InvoiceType = _type
                }));
            }
            catch (WebException ex)
            {
                var responseBody = new StreamReader(ex.Response.GetResponseStream()).ReadToEnd();
                MessageBox.Show($"{responseBody}");
                _logger.Error(ex, ex.Message, ex.StackTrace);
                _logger.Error(responseBody);
            }
        }


        private void UnlockInvoice(long id)
        {
            try
            {
                string address = null;
                switch (_type)
                {
                    case (int)TypeActionModel.Invoice01:
                        address = Config.UnlockInvoice01;
                        break;
                    default:
                        // Các loại HD không thỏa mãn
                        break;
                }

                if (address == null)
                {
                    return;
                }

                var client = new WebClient();
                client.BaseAddress = Config.Endpoint;
                client.Encoding = Encoding.UTF8;
                HttpClientService.IgnoreBadCertificates();
                client.Headers.Add("Authorization", $"{Config.Token.TokenType ?? "Bearer"} {Config.Token.AccessToken}");
                client.Headers.Add(HttpRequestHeader.ContentType, "application/json");

                var response = client.UploadString(address, JsonConvert.SerializeObject(new
                {
                    Id = id
                }));
            }
            catch (WebException ex)
            {
                var responseBody = new StreamReader(ex.Response.GetResponseStream()).ReadToEnd();
                MessageBox.Show($"{responseBody}");
                _logger.Error(ex, ex.Message, ex.StackTrace);
                _logger.Error(responseBody);
            }
        }
        
        /// <summary>
        /// Lưu XML sau khi ký
        /// </summary>
        /// <param name="groupCode">dành cho TBSS cho hoá đơn có trong hệ thống</param>
        /// <param name="xml">XML TBSS</param>
        /// <param name="tbssHeaderId">Id bảng TbssHeader: Lưu TBSS cho hoá đơn ngoài hệ thống</param>
        private void PostInvoiceError(long groupCode, string xml, long tbssHeaderId)
        {
            try
            {
                var client = new WebClient();
                client.BaseAddress = Config.Endpoint;
                client.Encoding = Encoding.UTF8;
                HttpClientService.IgnoreBadCertificates();
                client.Headers.Add("Authorization", $"{Config.Token.TokenType ?? "Bearer"} {Config.Token.AccessToken}");
                client.Headers.Add(HttpRequestHeader.ContentType, "application/json");

                var invoiceType = GetInvoiceTypeError();

                var response = client.UploadString(PostEndpointInvoiceError(), JsonConvert.SerializeObject(new
                {
                    GroupCode = groupCode,
                    Xml = xml,
                    InvoiceType = invoiceType,
                    TbssHeaderId = tbssHeaderId
                }));
            }
            catch (WebException ex)
            {
                var responseBody = new StreamReader(ex.Response.GetResponseStream()).ReadToEnd();
                MessageBox.Show($"{responseBody}");
                _logger.Error(ex, ex.Message, ex.StackTrace);
                _logger.Error(responseBody);
            }
        }

        /// <summary>
        /// lấy báo cáo thuế để ký
        /// </summary>
        /// <returns></returns>
        private SignTaxReportModel GetTaxReport()
        {
            var client = new WebClient();
            client.BaseAddress = Config.Endpoint;
            client.Encoding = Encoding.UTF8;
            HttpClientService.IgnoreBadCertificates();
            client.Headers.Add("Authorization", $"{Config.Token.TokenType ?? "Bearer"} {Config.Token.AccessToken}");
            client.Headers.Add(HttpRequestHeader.ContentType, "application/json");

            try
            {
                var response = client.DownloadString($"{GetEndpointTaxReport()}?serialNumber={_certificate.SerialNumber}&subjectName={HttpUtility.UrlEncode(_certificate.SubjectName)}");
                return JsonConvert.DeserializeObject<SignTaxReportModel>(response);
            }
            catch (WebException ex)
            {
                var responseBody = new StreamReader(ex.Response.GetResponseStream()).ReadToEnd();
                if (((HttpWebResponse)ex.Response).StatusCode == HttpStatusCode.NotFound)
                    MessageBox.Show("Không có báo cáo thuế để ký");
                else
                {
                    MessageBox.Show($"{responseBody}");
                    _logger.Error(ex, ex.Message, ex.StackTrace);
                    _logger.Error(responseBody);
                }
            }

            return null;
        }

        private object GetEndpointTaxReport()
        {
            switch (_type)
            {
                case 13:
                    return $"{Config.UrlGetXmlTaxReport01}";
                case 14:
                    return $"{Config.UrlGetXmlTaxReport03}";
                default:
                    MessageBox.Show("Loại báo cáo không đúng");
                    break;
            }
            return null;
        }


        private void PostTaxReportAfterSign(SignTaxReportModel taxReport)
        {
            try
            {
                var client = new WebClient();
                client.BaseAddress = Config.Endpoint;
                client.Encoding = Encoding.UTF8;
                HttpClientService.IgnoreBadCertificates();
                client.Headers.Add("Authorization", $"{Config.Token.TokenType ?? "Bearer"} {Config.Token.AccessToken}");
                client.Headers.Add(HttpRequestHeader.ContentType, "application/json");

                var response = client.UploadString(PostEndpointTaxReport(), JsonConvert.SerializeObject(taxReport));
            }
            catch (WebException ex)
            {
                var responseBody = new StreamReader(ex.Response.GetResponseStream()).ReadToEnd();
                MessageBox.Show($"{responseBody}");
                _logger.Error(ex, ex.Message, ex.StackTrace);
                _logger.Error(responseBody);
            }
        }

        private string PostEndpointTaxReport()
        {
            switch (_type)
            {
                case 13:
                    return $"{Config.UrlSaveXmlTaxReport01}";
                case 14:
                    return $"{Config.UrlSaveXmlTaxReport03}";
                default:
                    MessageBox.Show("Loại báo cáo không đúng");
                    break;
            }

            return null;
        }

        private List<CertificateModel> GetCertificates()
        {
            var store = new X509Store(StoreLocation.CurrentUser);
            store.Open(OpenFlags.ReadOnly);
            var certificates = new List<CertificateModel>();

            foreach (var certificate in store.Certificates)
            {
                certificates.Add(new CertificateModel
                {
                    StartDate = certificate.NotBefore,
                    Id = 0,
                    Selected = false,
                    SerialNumber = certificate.SerialNumber,
                    SubjectName = certificate.SubjectName.Name,
                    EndDate = certificate.NotAfter,
                    X509 = certificate
                });
            }
            return certificates;
        }
        private List<CertificateModel> CheckCertificates()
        {
            var client = new WebClient();
            client.BaseAddress = Config.Endpoint;
            client.Encoding = Encoding.UTF8;
            HttpClientService.IgnoreBadCertificates();
            client.Headers.Add("Authorization", $"{Config.Token.TokenType ?? "Bearer"} {Config.Token.AccessToken}");
            client.Headers.Add(HttpRequestHeader.ContentType, "application/json");

            var parameters = new List<string>();
            for (int i = 0; i < _certificates.Count; i++)
            {
                parameters.Add($"serials[{i}]={_certificates[i].SerialNumber}");
            }
            var response = client.DownloadString($"{Config.UrlGetCertificates}?{string.Join("&", parameters.ToArray())}");

            try
            {
                var model = JsonConvert.DeserializeObject<List<CertificateModel>>(response);

                return model;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Có lỗi trong quá trình lấy chứng thư số: {ex.Message}");
                _logger.Error(ex, ex.Message, ex.StackTrace);
                return new List<CertificateModel>();
            }
        }

        private void ToggleButtons()
        {
            if (_certificate == null)
            {
                Btn01.Enabled = false;
                Btn02.Enabled = false;
                Btn03.Enabled = false;
                Btn04.Enabled = false;
                Btn05.Enabled = false;
                Btn06.Enabled = false;
                BtnSignRegistration.Enabled = false;
                BtnRegister.Enabled = false;
                BtnSignInvoice01Error.Enabled = false;
                BtnSignInvoice02Error.Enabled = false;
                BtnSignInvoice03Error.Enabled = false;
                BtnSignInvoice04Error.Enabled = false;
                BtnSignInvoice05Error.Enabled = false;
                BtnSignTaxReport01.Enabled = false;
                LblMessage.Text = "Chọn 1 chứng thư số";
            }
            else
            {
                if (!_certificate.Registed)
                {
                    Btn01.Enabled = false;
                    Btn02.Enabled = false;
                    Btn03.Enabled = false;
                    Btn04.Enabled = false;
                    Btn05.Enabled = false;
                    Btn06.Enabled = false;
                    BtnSignRegistration.Enabled = false;
                    BtnSignInvoice01Error.Enabled = false;
                    BtnSignInvoice02Error.Enabled = false;
                    BtnSignInvoice03Error.Enabled = false;
                    BtnSignInvoice04Error.Enabled = false;
                    BtnSignInvoice05Error.Enabled = false;
                    BtnRegister.Enabled = true;
                }
                else
                {
                    Btn01.Enabled = true;
                    Btn02.Enabled = true;
                    Btn03.Enabled = true;
                    Btn04.Enabled = true;
                    Btn05.Enabled = true;
                    Btn06.Enabled = true;
                    BtnSignRegistration.Enabled = true;
                    BtnSignInvoice01Error.Enabled = true;
                    BtnSignInvoice02Error.Enabled = true;
                    BtnSignInvoice03Error.Enabled = true;
                    BtnSignInvoice04Error.Enabled = true;
                    BtnSignInvoice05Error.Enabled = true;
                    BtnRegister.Enabled = false;
                }

                LblMessage.Text = $"Bạn đã chọn chứng thư số {_certificate.SubjectName}, vui lòng chọn chức năng ký";
            }
        }

        private void ToggleLoading(bool isCompleted)
        {
            if (isCompleted)
            {
                Btn01.Enabled = true;
                Btn02.Enabled = true;
                Btn03.Enabled = true;
                Btn04.Enabled = true;
                Btn05.Enabled = true;
                Btn06.Enabled = true;
                Btn07.Enabled = true;
                btnSignPITDocumentError.Enabled = true;
                BtnRegister.Enabled = false;
                BtnSignRegistration.Enabled = true;
                BtnSignInvoice01Error.Enabled = true;
                BtnSignInvoice02Error.Enabled = true;
                BtnSignInvoice03Error.Enabled = true;
                BtnSignInvoice04Error.Enabled = true;
                BtnSignInvoice05Error.Enabled = true;
                BtnSignTaxReport01.Enabled = true;
                BtnSignPITDeductionDocumentDeclaration.Enabled = true;

                switch (_type)
                {
                    case 1:
                        Btn01.Text = "Ký 01GTGT (Ctrl + F1)";
                        Btn01.Image = Properties.Resources._01;
                        break;
                    case 2:
                        Btn02.Text = "Ký 02GTTT (Ctrl + F2)";
                        Btn02.Image = Properties.Resources._02;
                        break;
                    case 3:
                        Btn03.Text = "Ký 03XKNB (Ctrl + F3)";
                        Btn03.Image = Properties.Resources._03;
                        break;
                    case 4:
                        Btn04.Text = "Ký 04HGDL (Ctrl + F4)";
                        Btn04.Image = Properties.Resources._04;
                        break;
                    case 5:
                        Btn05.Text = "Ký vé điện tử (Ctrl + F5)";
                        Btn05.Image = Properties.Resources._05;
                        break;
                    case 6:
                        Btn06.Text = "Ký chứng từ khấu trừ thuế TNCN";
                        Btn06.Image = Properties.Resources._06;
                        break;
                    case 7:
                        BtnSignInvoice01Error.Text = "Ký 01GTGT sai sót";
                        BtnSignInvoice01Error.Image = null;
                        break;
                    case 8:
                        BtnSignInvoice02Error.Text = "Ký 02GTTT sai sót";
                        BtnSignInvoice02Error.Image = null;
                        break;
                    case 9:
                        BtnSignInvoice03Error.Text = "Ký 03XKNB sai sót";
                        BtnSignInvoice03Error.Image = null;
                        break;
                    case 10:
                        BtnSignInvoice04Error.Text = "Ký 04HGDL sai sót";
                        BtnSignInvoice04Error.Image = null;
                        break;
                    case 12:
                        BtnSignInvoice05Error.Text = "Ký vé điện tử sai sót";
                        BtnSignInvoice05Error.Image = null;
                        break;
                    case 13:
                        BtnSignTaxReport01.Text = "Ký báo cáo thuế 01/TH-HDDT";
                        BtnSignTaxReport01.Image = null;
                        break;
                    case 14:
                        BtnSignTaxReport03.Text = "Ký báo cáo thuế 03/DL-HDDT";
                        BtnSignTaxReport03.Image = null;
                        break;
                    case 15:
                        Btn07.Text = "Ký thư xác nhận thu nhập";
                        Btn07.Image = Properties.Resources._07;
                        break;
                    case 20:
                        btnSignPITDocumentError.Text = "Ký chứng từ TNCN sai sót";
                        btnSignPITDocumentError.Image = null;
                        break;
                    case 52:
                        BtnSignPITDeductionDocumentDeclaration.Text = "Ký đăng ký sử dụng chứng từ điện tử";
                        BtnSignPITDeductionDocumentDeclaration.Image = null;
                        break;
                    case 11:
                        break;
                    default:
                        break;
                }
            }
            else
            {
                Btn01.Enabled = false;
                Btn02.Enabled = false;
                Btn03.Enabled = false;
                Btn04.Enabled = false;
                Btn05.Enabled = false;
                Btn06.Enabled = false;
                Btn07.Enabled = false;
                BtnSignRegistration.Enabled = false;
                BtnSignInvoice01Error.Enabled = false;
                BtnSignInvoice02Error.Enabled = false;
                BtnSignInvoice03Error.Enabled = false;
                BtnSignInvoice04Error.Enabled = false;
                BtnSignInvoice05Error.Enabled = false;
                BtnSignTaxReport01.Enabled = false;
                btnSignPITDocumentError.Enabled = false;
                BtnSignPITDeductionDocumentDeclaration.Enabled = false;

                switch (_type)
                {
                    case 1:
                        Btn01.Text = "Đang ký";
                        Btn01.Image = Properties.Resources.loading;
                        Btn01.Enabled = true;
                        break;
                    case 2:
                        Btn02.Text = "Đang ký";
                        Btn02.Image = Properties.Resources.loading;
                        Btn02.Enabled = true;
                        break;
                    case 3:
                        Btn03.Text = "Đang ký";
                        Btn03.Image = Properties.Resources.loading;
                        Btn03.Enabled = true;
                        break;
                    case 4:
                        Btn04.Text = "Đang ký";
                        Btn04.Image = Properties.Resources.loading;
                        Btn04.Enabled = true;
                        break;
                    case 5:
                        Btn05.Text = "Đang ký";
                        Btn05.Image = Properties.Resources.loading;
                        Btn05.Enabled = true;
                        break;
                    case 6:
                        Btn06.Text = "Đang ký";
                        Btn06.Image = Properties.Resources.loading;
                        Btn06.Enabled = true;
                        break;
                    case 7:
                        BtnSignInvoice01Error.Text = "Đang ký";
                        BtnSignInvoice01Error.Image = Properties.Resources.loading;
                        BtnSignInvoice01Error.Enabled = true;
                        break;
                    case 8:
                        BtnSignInvoice02Error.Text = "Đang ký";
                        BtnSignInvoice02Error.Image = Properties.Resources.loading;
                        BtnSignInvoice02Error.Enabled = true;
                        break;
                    case 9:
                        BtnSignInvoice03Error.Text = "Đang ký";
                        BtnSignInvoice03Error.Image = Properties.Resources.loading;
                        BtnSignInvoice03Error.Enabled = true;
                        break;
                    case 10:
                        BtnSignInvoice04Error.Text = "Đang ký";
                        BtnSignInvoice04Error.Image = Properties.Resources.loading;
                        BtnSignInvoice04Error.Enabled = true;
                        break;
                    case 12:
                        BtnSignInvoice05Error.Text = "Đang ký";
                        BtnSignInvoice05Error.Image = Properties.Resources.loading;
                        BtnSignInvoice05Error.Enabled = true;
                        break;
                    case 13:
                        BtnSignTaxReport01.Text = "Đang ký";
                        BtnSignTaxReport01.Image = Properties.Resources.loading;
                        BtnSignTaxReport01.Enabled = true;
                        break;
                    case 15:
                        Btn07.Text = "Đang ký";
                        Btn07.Image = Properties.Resources.loading;
                        Btn07.Enabled = true;
                        break;
                    case 20:
                        btnSignPITDocumentError.Text = "Đang ký";
                        btnSignPITDocumentError.Image = Properties.Resources.loading;
                        break;
                    case 52:
                        BtnSignPITDeductionDocumentDeclaration.Text = "Đang ký";
                        BtnSignPITDeductionDocumentDeclaration.Image = Properties.Resources.loading;
                        break;
                    default:
                        break;
                }
            }
        }

        private void BtnRegister_Click(object sender, EventArgs e)
        {
            try
            {
                var client = new WebClient();
                client.BaseAddress = Config.Endpoint;
                client.Encoding = Encoding.UTF8;
                HttpClientService.IgnoreBadCertificates();
                client.Headers.Add("Authorization", $"{Config.Token.TokenType ?? "Bearer"} {Config.Token.AccessToken}");
                client.Headers.Add(HttpRequestHeader.ContentType, "application/json");

                var obj = new
                {
                    SubjectName = _certificate.SubjectName,
                    _certificate.SerialNumber,
                    StartDate = _certificate.StartDate.ToString("yyyy-MM-dd"),
                    EndDate = _certificate.EndDate.ToString("yyyy-MM-dd")
                };
                var json = JsonConvert.SerializeObject(new List<object> { obj });
                client.UploadString($"{Config.UrlPostCertificates}", json);

                MessageBox.Show("Đăng ký chứng thư số thành công");

                _certificate.Registed = true;
                var certificate = _certificates.Find(x => x == _certificate);
                if (certificate != null)
                    certificate.Registed = true;

                var index = _certificates.FindIndex(x => x == _certificate);
                DgvCertificates.Rows[index].Cells["Select"].Value = true;
                DgvCertificates.Rows[index].Cells["Status"].Value = "Đã đăng ký";
                ToggleButtons();
            }
            catch (WebException ex)
            {
                var resp = new StreamReader(ex.Response.GetResponseStream()).ReadToEnd();
                //var obj2 = JsonConvert.DeserializeObject<ResponseModel>(resp);
                //var messageFromServer = obj2.error.message;

                MessageBox.Show($"{resp}");
                _logger.Error(ex, ex.Message, ex.StackTrace);
                _logger.Error(resp);
            }
        }

        private bool ValidationTaxReport01BeforeSign(string xml, out string message)
        {
            XmlDocument doc = new XmlDocument();
            doc.LoadXml(xml);

            var nBanNode = doc.SelectNodes(@"//DLBTHop/TTChung");
            if (nBanNode == null || nBanNode.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ thông tin chung: DLBTHop/TTChung";
                return false;
            }

            var tags = doc.SelectNodes(@"//DLBTHop/TTChung/MST");
            if (tags.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ thông tin mã số thuế người bán: DLBTHop/TTChung/MST";
                return false;
            }

            var sellerTaxCode = tags[0].InnerText;

            tags = doc.SelectNodes(@"//DLBTHop/NDBTHDLieu");
            if (tags.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ thông tin bảng tổng hợp dữ liệu: DLBTHop/NDBTHDLieu";
                return false;
            }

            if (_certificate.SubjectName.IndexOf(sellerTaxCode, StringComparison.Ordinal) < 0)
            {
                message = $"Mã số thuế người bán trong USB với báo cáo thuế không giống nhau";
                return false;
            }

            //check có chữ ký người bán chưa
            tags = doc.SelectNodes(@"//DSCKS/NNT/Signature/SignedInfo");
            if (tags.Count != 0)
            {
                message = "Dữ liệu đã chứa thẻ DSCKS/NNT/Signature/SignedInfo";
                return false;
            }

            message = null;
            return true;
        }

        private bool ValidationTaxReport03BeforeSign(string xml, out string message)
        {
            XmlDocument doc = new XmlDocument();
            doc.LoadXml(xml);

            var nBanNode = doc.SelectNodes(@"//DLTKhai/TTChung");
            if (nBanNode == null || nBanNode.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ thông tin chung: DLTKhai/TTChung";
                return false;
            }

            var tags = doc.SelectNodes(@"//DLTKhai/TTChung/MST");
            if (tags.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ thông tin mã số thuế người bán: DLTKhai/TTChung/MST";
                return false;
            }

            var sellerTaxCode = tags[0].InnerText;

            tags = doc.SelectNodes(@"//DLTKhai/NDTKhai");
            if (tags.Count == 0)
            {
                message = "Dữ liệu không chứa thẻ thông tin nội dung tờ khai: DLTKhai/NDTKhai";
                return false;
            }

            if (_certificate.SubjectName.IndexOf(sellerTaxCode, StringComparison.Ordinal) < 0)
            {
                message = $"Mã số thuế người bán trong USB với báo cáo thuế không giống nhau";
                return false;
            }

            //check có chữ ký người bán chưa
            tags = doc.SelectNodes(@"//DSCKS/NNT/Signature/SignedInfo");
            if (tags.Count != 0)
            {
                message = "Dữ liệu đã chứa thẻ DSCKS/NNT/Signature/SignedInfo";
                return false;
            }

            message = null;
            return true;
        }

        private void btnSignPITDocumentError_Click(object sender, EventArgs e)
        {
            if (BgwSigning.IsBusy)
                return;

            _type = TypeActionModel.PITDocumentError.GetHashCode();
            _isRegistration = false;
            ToggleLoading(false);
            BgwSigning.RunWorkerAsync();
        }

        //private void BtnSignInvoiceError32_Click(object sender, EventArgs e)
        //{
        //    try
        //    {
        //        {
        //            if (BgwSigning.IsBusy)
        //                return;

        //            _type = TypeActionModel.TaxReport01.GetHashCode();
        //            _isRegistration = false;
        //            ToggleLoading(false);

        //            //lấy xml
        //            var folder = ConfigurationManager.AppSettings["FolderXmlInvoiceError32UnSign"];
        //            var folderXmlSigned = Path.Combine(folder, "XmlSigned");

        //            if (!Directory.Exists(folderXmlSigned))
        //                Directory.CreateDirectory(folderXmlSigned);

        //            DirectoryInfo d = new DirectoryInfo(@"D:\Test"); //Assuming Test is your Folder

        //            FileInfo[] fileXmls = d.GetFiles("*.xml"); //Getting Text files

        //            foreach (var fileXml in fileXmls)
        //            {
        //                var xmlUnSign = File.ReadAllText(fileXml.FullName);

        //                XmlSerializer serializer = new XmlSerializer(typeof(DLieuInvoiceErrorRequestModel));
        //                DLieuInvoiceErrorRequestModel modelXml = (DLieuInvoiceErrorRequestModel)serializer.Deserialize(new XmlTextReader(fileXml.FullName));

        //                var xml = SellerSign(xmlUnSign, modelXml.TBao.DLTBao.Data, modelXml.TBao.DSCKS.NNT.Signature.Object.Id, modelXml.TBao.DSCKS.NNT.Signature.Id);

        //                File.WriteAllText(Path.Combine(folderXmlSigned, fileXml.Name), xml);
        //            }

        //            //BgwSigning.RunWorkerAsync();
        //        }
        //    }
        //    catch (WebException ex)
        //    {
        //        var resp = new StreamReader(ex.Response.GetResponseStream()).ReadToEnd();
        //        //var obj2 = JsonConvert.DeserializeObject<ResponseModel>(resp);
        //        //var messageFromServer = obj2.error.message;

        //        MessageBox.Show($"{resp}");
        //        _logger.Error(ex, ex.Message, ex.StackTrace);
        //        _logger.Error(resp);
        //    }
        //}
    }
}