# Tài liệu nghiệp vụ hàm RemoveDataMongoAsync

## 1. <PERSON><PERSON><PERSON> đích và chức năng chính

Hàm `RemoveDataMongoAsync` trong class `Invoice01RemoveDataMongoBusinessV2` có nhiệm vụ **dọn dẹp dữ liệu hóa đơn trong MongoDB** sau khi các hóa đơn đã hoàn thành quy trình xử lý và đồng bộ thành công về hệ thống Oracle Core.

### Mục tiêu chính:
- **Tối ưu hóa hiệu suất**: Giảm tải dữ liệu trong MongoDB để cải thiện performance
- **Quản lý dung lượng**: Tránh MongoDB bị quá tải do tích lũy dữ liệu
- **Đ<PERSON><PERSON> bảo tính toàn vẹn**: Chỉ xóa những hóa đơn đã hoàn thành đầy đủ quy trình nghiệp vụ
- **<PERSON><PERSON><PERSON> bộ ErpId**: Chuyển dữ liệu ErpId từ MongoDB sang Oracle trước khi xóa

## 2. Luồng xử lý nghiệp vụ chi tiết

### Bước 1: Khởi tạo và cấu hình
```
1. Đọc cấu hình từ appsettings:
   - NumberInvoicesTake: Số lượng hóa đơn xử lý mỗi lần (mặc định: 1000)
   - NumberInvoicesSkip: Số lượng hóa đơn bỏ qua (mặc định: 0)

2. Xác định loại hóa đơn:
   - invoiceHasCode = true  → prefixSerialNo = "C" (hóa đơn có mã)
   - invoiceHasCode = false → prefixSerialNo = "K" (hóa đơn không mã)
```

### Bước 2: Truy vấn hóa đơn đủ điều kiện xóa
Hệ thống truy vấn MongoDB để lấy danh sách hóa đơn thỏa mãn **TẤT CẢ** các điều kiện sau:

```sql
-- Điều kiện truy vấn hóa đơn
prefixSerialNo: "C" hoặc "K"
signStatus: 5 (SignStatus.DaKy - Đã ký)
isSyncToCore: 1 (SyncedToCoreStatus.Synced - Đã đồng bộ về Core)
isSyncToEs: 1 (SyncElasticSearchStatus.Synced - Đã đồng bộ về ElasticSearch)
tvanStatus: 3 (TvanStatus.TCTAccept - TCT chấp nhận)
isSyncVerificationCodeTocore: 1 (SyncVerificationCodeTocore.Success - Đã đồng bộ mã xác thực)
mailStatus: [1, -2] (1: đã tạo content mail, -2: không cấu hình gửi mail tự động)
syncCatalogStatus: [1, -2] (1: đã đồng bộ danh mục, -2: không cấu hình đồng bộ danh mục)
```

### Bước 3: Kiểm tra tồn tại trong Oracle
```
1. Lấy danh sách ID hóa đơn từ MongoDB
2. Truy vấn Oracle để kiểm tra hóa đơn có tồn tại không
3. Chỉ xử lý những hóa đơn có trong cả MongoDB và Oracle
```

### Bước 4: Phân loại hóa đơn theo TenantId
```
1. Nhóm hóa đơn theo TenantId
2. Với mỗi nhóm TenantId:
   - Kiểm tra điều kiện nghiệp vụ chi tiết
   - Phân loại thành 2 danh sách:
     * invoiceMongoRemove: Hóa đơn được phép xóa
     * invoiceMongoUpdate: Hóa đơn cần cập nhật trạng thái
```

### Bước 5: Xử lý đồng bộ ErpId
```
1. Lấy danh sách ErpId từ MongoDB (collection: VnisInvoice01ErpId)
2. Chuyển dữ liệu ErpId sang Oracle với status = IsSyncedToCore (2)
3. Xóa ErpId khỏi MongoDB sau khi đồng bộ thành công
```

### Bước 6: Thực hiện xóa dữ liệu
```
1. Xóa hóa đơn khỏi MongoDB (collection: VnisInvoice01)
2. Ghi log số lượng hóa đơn đã xóa
3. Cập nhật trạng thái cho những hóa đơn không được xóa
```

## 3. Các collection MongoDB được tác động

### 3.1 Collection VnisInvoice01 (Chính)
- **Thao tác**: DELETE
- **Điều kiện**: Hóa đơn thỏa mãn tất cả điều kiện nghiệp vụ
- **Phương thức**: `DeleteManyByIdsAsync()`

### 3.2 Collection VnisInvoice01ErpId
- **Thao tác**: DELETE  
- **Điều kiện**: ErpId đã được đồng bộ sang Oracle
- **Phương thức**: `DeleteManyByTenantIdAndErpIdsAsync()`

### 3.3 Oracle Table Invoice01Header
- **Thao tác**: SELECT (chỉ đọc)
- **Mục đích**: Kiểm tra sự tồn tại và trạng thái đồng bộ

### 3.4 Oracle Table Invoice01ErpId  
- **Thao tác**: INSERT
- **Mục đích**: Lưu trữ ErpId từ MongoDB trước khi xóa

## 4. Cấu trúc dữ liệu và các field quan trọng

### 4.1 MongoInvoice01Entity (MongoDB)
```csharp
// Các field quan trọng được kiểm tra
Id: long                           // ID hóa đơn
TenantId: Guid                     // ID tenant
ErpId: string                      // ID từ hệ thống ERP
SerialNo: string                   // Ký hiệu hóa đơn (C*/K*)
SignStatus: short                  // Trạng thái ký (5 = Đã ký)
IsSyncedToCore: short             // Trạng thái đồng bộ Core (1 = Đã đồng bộ)
IsSyncedToElasticSearch: short    // Trạng thái đồng bộ ES (1 = Đã đồng bộ)
StatusTvan: short                 // Trạng thái TVAN (3 = TCT chấp nhận)
IsSyncVerificationCodeTocore: short // Đồng bộ mã xác thực (1 = Thành công)
IsCreatedContentEmail: short      // Trạng thái tạo email (1/-2)
VerificationCode: string          // Mã xác thực từ TCT
```

### 4.2 Invoice01HeaderEntity (Oracle)
```csharp
// Các field được so sánh
Id: long                    // ID hóa đơn (khớp với MongoDB)
SignStatus: short          // Trạng thái ký
StatusTvan: short          // Trạng thái TVAN
VerificationCode: string   // Mã xác thực
SerialNo: string          // Ký hiệu hóa đơn
```

## 5. Điều kiện và logic nghiệp vụ

### 5.1 Điều kiện BẮT BUỘC để xóa hóa đơn
1. **Trạng thái ký**: `SignStatus = 5` (Đã ký)
2. **Đồng bộ Core**: `IsSyncedToCore = 1` (Đã đồng bộ)
3. **Đồng bộ ElasticSearch**: `IsSyncedToElasticSearch = 1` (Đã đồng bộ)
4. **Trạng thái TVAN**: `StatusTvan = 3` (TCT chấp nhận)
5. **Đồng bộ mã xác thực**: `IsSyncVerificationCodeTocore = 1` (Thành công)
6. **Trạng thái email**: `IsCreatedContentEmail ∈ {1, -2}` (Đã tạo hoặc không cấu hình)
7. **Đồng bộ danh mục**: `SyncCatalogStatus ∈ {1, -2}` (Đã đồng bộ hoặc không cấu hình)

### 5.2 Logic xử lý đặc biệt

#### Hóa đơn có mã (SerialNo bắt đầu bằng "C"):
```
- Phải có VerificationCode (mã xác thực từ TCT)
- StatusTvan phải khác UnSent (0) và SendError (-1)
- Nếu thiếu điều kiện → cập nhật trạng thái thay vì xóa
```

#### Hóa đơn không mã (SerialNo bắt đầu bằng "K"):
```
- Không yêu cầu VerificationCode
- Kiểm tra trạng thái kê khai (hiện tại đang comment)
```

### 5.3 Trường hợp cập nhật thay vì xóa
```
1. Hóa đơn MongoDB và Oracle có trạng thái không đồng bộ
2. Hóa đơn có mã nhưng chưa có VerificationCode
3. StatusTvan giữa MongoDB và Oracle không khớp
→ Cập nhật: IsSyncSignTocore = 0, IsSyncedToElasticSearch = 7
```

## 6. Các trường hợp ngoại lệ và xử lý lỗi

### 6.1 Exception Handling
```csharp
try {
    // Toàn bộ logic xử lý
} catch (Exception ex) {
    Log.Error(ex, ex.Message);
    // Không throw exception, tiếp tục xử lý
}
```

### 6.2 Các trường hợp ngoại lệ

#### Lỗi truy vấn MongoDB:
- **Nguyên nhân**: Kết nối MongoDB bị gián đoạn, timeout
- **Xử lý**: Ghi log lỗi, bỏ qua batch hiện tại
- **Tác động**: Không ảnh hưởng đến dữ liệu, sẽ retry ở lần chạy tiếp theo

#### Lỗi truy vấn Oracle:
- **Nguyên nhân**: Kết nối Oracle bị gián đoạn, query timeout
- **Xử lý**: Ghi log lỗi, dừng xử lý batch hiện tại
- **Tác động**: Dữ liệu MongoDB không bị xóa, đảm bảo tính toàn vẹn

#### Lỗi đồng bộ ErpId:
- **Nguyên nhân**: Lỗi insert vào Oracle, constraint violation
- **Xử lý**: Ghi log chi tiết, không xóa ErpId khỏi MongoDB
- **Tác động**: ErpId sẽ được retry ở lần chạy tiếp theo

#### Không tìm thấy hóa đơn:
- **Trường hợp**: `invoicesMongo.IsNullOrEmpty()`
- **Xử lý**: Ghi log thông tin, kết thúc xử lý
- **Tác động**: Không có tác động, hệ thống hoạt động bình thường

### 6.3 Data Consistency Issues
```
Vấn đề: Hóa đơn tồn tại trong MongoDB nhưng không có trong Oracle
Nguyên nhân: Lỗi đồng bộ trước đó, dữ liệu bị mất
Xử lý: Không xóa hóa đơn, ghi log để điều tra
```

## 7. Tác động đến hệ thống và các module liên quan

### 7.1 Tác động tích cực
- **Performance MongoDB**: Giảm tải dữ liệu, cải thiện tốc độ truy vấn
- **Storage**: Tiết kiệm dung lượng lưu trữ
- **Memory**: Giảm memory usage của MongoDB
- **Backup**: Giảm thời gian backup/restore

### 7.2 Module liên quan

#### Invoice01 Core Module:
- **Tác động**: Không ảnh hưởng, dữ liệu chính vẫn trong Oracle
- **Dependency**: Sử dụng Oracle repositories để kiểm tra dữ liệu

#### ElasticSearch Sync Module:
- **Tác động**: Không ảnh hưởng, dữ liệu đã được đồng bộ
- **Lưu ý**: Chỉ xóa hóa đơn đã sync thành công

#### TVAN Integration Module:
- **Tác động**: Không ảnh hưởng, chỉ xóa hóa đơn đã được TCT chấp nhận
- **Dependency**: Kiểm tra StatusTvan trước khi xóa

#### Email Service Module:
- **Tác động**: Không ảnh hưởng, chỉ xóa hóa đơn đã gửi email hoặc không cấu hình gửi email
- **Dependency**: Kiểm tra IsCreatedContentEmail

#### Background Workers:
- **RemoveDataMongoInvoice01HasCodeBackgroundWorker**: Xử lý hóa đơn có mã
- **RemoveDataMongoInvoice01WithoutCodeBackgroundWorker**: Xử lý hóa đơn không mã
- **Frequency**: Chạy định kỳ theo cấu hình Timer.Period

### 7.3 Monitoring và Logging
```
Log Levels:
- Information: Thống kê số lượng hóa đơn xử lý
- Fatal: Số lượng hóa đơn xóa/cập nhật (quan trọng)
- Error: Lỗi exception, kết nối database
```

## 8. Lưu ý quan trọng cho DBA và team vận hành

### 8.1 Performance Considerations

#### MongoDB Performance:
```sql
-- Index cần thiết cho query hiệu quả
db.VnisInvoice01.createIndex({
    "SerialNo": 1,
    "SignStatus": 1,
    "IsSyncedToCore": 1,
    "IsSyncedToElasticSearch": 1,
    "StatusTvan": 1,
    "IsSyncVerificationCodeTocore": 1
})

-- Index cho ErpId collection
db.VnisInvoice01ErpId.createIndex({
    "TenantId": 1,
    "ErpId": 1,
    "Status": 1
})
```

#### Oracle Performance:
```sql
-- Index cho bảng Invoice01Header
CREATE INDEX IX_Invoice01Header_RemoveData
ON Invoice01Header (Id, SignStatus, StatusTvan, VerificationCode);

-- Index cho bảng Invoice01ErpId
CREATE INDEX IX_Invoice01ErpId_TenantErp
ON Invoice01ErpId (TenantId, ErpId, Status);
```

### 8.2 Data Integrity Safeguards

#### Backup Strategy:
```
1. Backup MongoDB trước khi chạy job remove data
2. Thiết lập retention policy cho backup
3. Test restore procedure định kỳ
```

#### Monitoring Alerts:
```
1. Số lượng hóa đơn xóa bất thường (> threshold)
2. Lỗi kết nối database
3. Thời gian xử lý quá lâu (> timeout)
4. Dung lượng MongoDB giảm đột ngột
```

### 8.3 Configuration Management

#### Cấu hình quan trọng:
```json
{
  "Settings": {
    "NumberInvoicesTake": 1000,    // Số lượng xử lý mỗi batch
    "NumberInvoicesSkip": 0,       // Số lượng bỏ qua
    "TimePeriod": 30000           // Chu kỳ chạy (ms)
  }
}
```

#### Khuyến nghị cấu hình:
- **Production**: NumberInvoicesTake = 500-1000
- **Development**: NumberInvoicesTake = 100-200
- **TimePeriod**: 30-60 giây để tránh overload

### 8.4 Disaster Recovery

#### Rollback Strategy:
```
1. Không thể rollback dữ liệu đã xóa khỏi MongoDB
2. Dữ liệu chính vẫn an toàn trong Oracle
3. Có thể rebuild MongoDB từ Oracle nếu cần thiết
```

#### Emergency Procedures:
```
1. Dừng Background Workers nếu phát hiện lỗi nghiêm trọng
2. Kiểm tra data consistency giữa MongoDB và Oracle
3. Restore từ backup nếu cần thiết
```

### 8.5 Capacity Planning

#### Dự đoán dung lượng:
```
- Ước tính số hóa đơn tạo mới mỗi ngày
- Thời gian lưu trữ trong MongoDB trước khi xóa
- Tính toán dung lượng cần thiết cho MongoDB
```

#### Scaling Considerations:
```
- Tăng NumberInvoicesTake khi volume tăng
- Giảm TimePeriod khi cần xử lý nhanh hơn
- Monitor MongoDB performance metrics
```

---

**Lưu ý cuối**: Hàm này là một phần quan trọng của chiến lược quản lý dữ liệu, đảm bảo hệ thống hoạt động hiệu quả trong dài hạn. Cần monitoring chặt chẽ và có kế hoạch backup/recovery đầy đủ.
